﻿/**
 * @name umi 的路由配置
 * @description 只支持 path,component,routes,redirect,wrappers,name,icon 的配置
 * @param path  path 只支持两种占位符配置，第一种是动态参数 :id 的形式，第二种是 * 通配符，通配符只能出现路由字符串的最后。
 * @param component 配置 location 和 path 匹配后用于渲染的 React 组件路径。可以是绝对路径，也可以是相对路径，如果是相对路径，会从 src/pages 开始找起。
 * @param routes 配置子路由，通常在需要为多个路径增加 layout 组件时使用。
 * @param redirect 配置路由跳转
 * @param wrappers 配置路由组件的包装组件，通过包装组件可以为当前的路由组件组合进更多的功能。 比如，可以用于路由级别的权限校验
 * @param name 配置路由的标题，默认读取国际化文件 menu.ts 中 menu.xxxx 的值，如配置 name 为 login，则读取 menu.ts 中 menu.login 的取值作为标题
 * @param icon 配置路由的图标，取值参考 https://ant.design/components/icon-cn， 注意去除风格后缀和大小写，如想要配置图标为 <StepBackwardOutlined /> 则取值应为 stepBackward 或 StepBackward，如想要配置图标为 <UserOutlined /> 则取值应为 user 或者 User
 * @doc https://umijs.org/docs/guides/routes
 */
export default [
  {
    path: '/user',
    layout: false,
    routes: [
      {
        name: 'login',
        path: '/user/login',
        component: './User/Login',
      },
    ],
  },
  {
    path: '/welcome',
    name: '欢迎',
    icon: 'smile',
    component: './Welcome',
  },
  // {
  //   path: '/product',
  //   icon: 'FundOutlined',
  //   name: '区域材料管理',
  //   routes: [
  //     {
  //       path: '/product/list',
  //       name: '商品管理',
  //       component: './Product',
  //     },
  //   ],
  // },
  {
    path: '/areaMaterials',
    icon: 'FundOutlined',
    name: '区域材料管理',
    // component: './AreaMaterials',
    routes: [
      {
        path: '/areaMaterials/toBePublished',
        name: '待发布材料',
        component: './AreaMaterials/toBePublished',
      },
      {
        path: '/areaMaterials/list',
        name: '已发布材料',
        component: './AreaMaterials',
      },
      {
        path: '/areaMaterials/materialsProperty',
        name: '材料属性管理',
        component: './MaterialsProperty',
      },
      {
        path: '/areaMaterials/areaProperty/list',
        icon: 'file',
        name: '区域属性管理',
        component: './AreaProperty',
      },
    ],
  },
  // TODO: 邪门歪道，默认打开一个路由，但是这个路由是隐藏的
  {
    path: '/',
    redirect: '/YLFManage/school/list',
    hideInMenu: true,
  },
  // 幼立方管理
  {
    path: '/YLFManage',
    icon: 'shop',
    name: '幼立方管理',
    // redirect: '/user',
    routes: [
      {
        path: '/YLFManage/school/list',
        icon: 'home',
        name: '学校管理',
        component: './School',
      },
      {
        path: '/YLFManage/class/list',
        icon: 'read',
        name: '班级管理',
        component: './SchoolClass',
      },
      {
        path: '/YLFManage/userManager/list',
        name: '用户管理',
        icon: 'user',
        component: './UserManager/List/index',
      },
      {
        path: '/YLFManage/documentTemplate/list',
        name: '学校模版',
        icon: 'CodeOutlined',
        component: './DocumentTemplate/index',
      },
      {
        path: '/YLFManage/documentTemplate/templateList',
        name: '模版列表',
        icon: 'CodeOutlined',
        component: './DocumentTemplate/TemplateList',
      },
      {
        path: '/YLFManage/child/list',
        name: '儿童列表',
        icon: 'UsergroupAddOutlined',
        component: './Child/index',
      },
    ],
  },
  {
    path: '/userManager/detail/:id',
    name: '用户详情',
    hideInMenu: true,
    component: './UserManager/components/detail',
  },
  // 青禾AI管理
  {
    path: '/aiTemplate',
    name: ' 青禾AI管理',
    icon: 'GlobalOutlined',
    routes: [
      {
        path: '/aiTemplate/list',
        name: 'AI模版',
        icon: 'CodeOutlined',
        component: './AITemplate/index',
      },
    ],
  },
  // 核心经验管理
  {
    path: '/CoreExperience',
    name: '核心经验管理',
    icon: 'CodeOutlined',
    routes: [
      {
        path: '/CoreExperience/Matrix/tree',
        icon: 'table',
        name: '矩阵管理',
        component: './CoreExperience/Matrix',
      },
      {
        path: '/CoreExperience/Target/list',
        icon: 'pushpin',
        name: '指标管理',
        component: './CoreExperience/Target',
      },
    ],
  },
  {
    path: '/resource/list',
    icon: 'file',
    name: '资源管理',
    component: './Resource',
  },
  //儿童心情管理
  {
    path: '/childMood',
    name: '儿童心情管理',
    icon: 'smile',
    component: './ChildMood',
  },
  {
    path: '/SystemManagement',
    name: '系统管理',
    icon: 'crown',
    // flatMenu: true,
    routes: [
      {
        path: '/SystemManagement/dictionary',
        icon: 'cluster',
        name: '配置项管理',
        component: './SystemManagement/Dictionary/index',
      },
      {
        path: '/SystemManagement/dictManaged',
        name: '字典管理',
        icon: 'barcode-outlined',
        component: './SystemManagement/dict/dictManaged',
        // hideInMenu: true,
      },
      {
        path: '/SystemManagement/dictManagedItem',
        name: '字典项管理',
        icon: 'barcode-outlined',
        component: './SystemManagement/dict/dictManagedItem',
        hideInMenu: true,
      },
    ],
  },

  {
    path: '/privilege',
    name: '权限管理',
    icon: 'crown',
    flatMenu: true,
    routes: [
      {
        path: '/privilege/role',
        name: '角色管理',
        icon: 'crown',
        component: './Privilege/Role/List/index',
      },
      {
        path: '/privilege/role/detail/:id',
        name: '角色详情',
        hideInMenu: true,
        component: './Privilege/Role/List/components/detail',
      },
    ],
  },
  {
    path: '/product/add',
    hideInMenu: true,
    name: '新增/编辑商品',
    component: './Product/components/add',
  },
  {
    path: '/product/detail/:id',
    hideInMenu: true,
    name: '商品详情',
    component: './Product/components/detail',
  },
  {
    path: '/school/detail/:id',
    hideInMenu: true,
    name: '学校详情',
    component: './School/components/detail',
  },
  {
    path: '/userManager/viewlog/:id',
    hideInMenu: true,
    name: '日志',
    component: './UserManager/ViewLog',
  },
  // {
  //   path: '/class/list',
  //   icon: 'read',
  //   name: '班级管理',
  //   component: './Class/index',
  // },
  {
    path: '/class/detail/:id',
    hideInMenu: true,
    name: '班级详情',
    component: './SchoolClass/components/detail',
  },
  {
    path: '/class-product/list/:id',
    hideInMenu: true,
    name: '班级商品管理',
    component: './ClassProduct',
  },
  {
    path: '/admin',
    name: 'admin',
    icon: 'crown',
    access: 'canAdmin',
    routes: [
      {
        path: '/admin',
        redirect: '/admin/sub-page',
      },
      {
        path: '/admin/sub-page',
        name: 'sub-page',
        component: './Admin',
      },
    ],
  },
  {
    path: '/child',
    name: '儿童管理',
    icon: 'UsergroupAddOutlined',
    flatMenu: true,
    routes: [
      {
        path: '/child/detail/:id',
        name: '儿童详情',
        hideInMenu: true,
        component: './Child/Detail',
      },
    ],
  },

  {
    path: '/feedback',
    name: '反馈列表',
    icon: 'feedback',
    flatMenu: true,
    routes: [
      {
        path: '/feedback/list',
        name: '反馈列表',
        icon: 'editOutlined',
        component: './Feedback/index',
      },
      {
        path: '/feedback/add',
        name: '新增反馈',
        hideInMenu: true,
        component: './Feedback/Edit',
      },
      {
        path: '/feedback/detail/:id',
        name: '反馈详情',
        hideInMenu: true,
        component: './Feedback/Detail',
      },
      {
        path: '/feedback',
        redirect: '/feedback/list',
        hideInMenu: true,
      },
    ],
  },
  {
    path: '/toolkit',
    name: '工具包',
    icon: 'tool',
    routes: [
      {
        path: '/toolkit/list',
        name: '临时用户列表',
        icon: 'tool',
        component: './Toolkit/index',
      },
      {
        path: '/toolkit/qrCode',
        name: '生成二维码',
        icon: 'barcode-outlined',
        component: './Toolkit/qrCode',
      },
    ],
  },
  {
    path: '/',
    redirect: '/welcome',
  },
  {
    path: '*',
    layout: false,
    component: './404',
  },
];
