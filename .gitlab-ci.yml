stages:
  - build

cache:
  paths:
    - node_modules/
    
build-staging:
  stage: build
  image: node:16
  script:
    - npm install
    - APP_ENV=development NODE_ENV=staging npm run build
    - rm -rf /deploy_staging/*
    - cp -a dist/. /deploy_staging/
  only:
    - staging
  tags:
    - pts-admin-runner

build-prod:
  stage: build
  image: node:16
  script:
    - npm install
    - APP_ENV=production NODE_ENV=production npm run build
    - rm -rf /deploy/*
    - cp -a dist/. /deploy/
  only:
    - master
  tags:
    - pts-admin-runner