import { getRequest, postRequest } from './comon';

export type AITemplateParams = {
  current?: number;
  pageSize?: number;
  state?: number;
  name?: string;
};

export const getAITemplateList = async (params: AITemplateParams) => {
  return await getRequest<API.AITemplateList>('/pts/admin/ai-assistant-template/list', params);
};

export const createAITemplate = async (params: Record<string, any>) => {
  return await postRequest<API.AdminResponse>('/pts/admin/ai-assistant-template/create', params);
};

export const updateAITemplate = async (params: Record<string, any>) => {
  return await postRequest<API.AdminResponse>('/pts/admin/ai-assistant-template/update', params);
};

export const deleteAITemplate = async (id: number) => {
  return await getRequest<API.AdminResponse>(`/pts/admin/ai-assistant-template/delete/${id}`);
};
