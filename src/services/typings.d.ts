// @ts-ignore
/* eslint-disable */

declare namespace API {
  type AdminResponse = {
    message?: string;
    metadata?: any;
    data?: any;
    status?: number;
    flag?: boolean;
    code?: number;
  };

  type RequestOptions = {
    /**
     * 列表类型
     * 1 - 分页，即有 data+page+pageNo
     * 5 - 列表，但只要第 1 个，即 detail 接口
     * 10 - 全部，即只有 data
     * 15 - 字典值列表，转 Select 值列表
     */
    listType?: number;
    banAfter?: boolean; // 是否禁止自动解析请求结果，默认为false
  };

  type CurrentUser = {
    id?: number;
    nickname?: string;
    headerId?: number;
    createdAt?: string;
    updatedAt?: string;
    deleteAt?: string;
    mobile?: string;
    state?: number;
    name?: string;
  };

  type CaptchaResult = {
    data?: {
      code: string;
    };
  };

  type LoginResult = {
    data?: {
      token: string;
      user: CurrentUser;
    };
  };

  type PageParams = {
    current?: number;
    pageSize?: number;
  };
  // == 文件上传返回结构
  type DocumentEntity = {
    hash: string;
    filename: string;
    filepath: string;
    createdAt: string;
    updatedAt: string;
    deletedAt: string;
  };

  // == 资源结构
  type ResourceDTO = {
    filepath: string;
    hash: string;
    filename: string;
    fileCategory: number;
    category: number;
  };
  type ResourceItem = BaseResultItem & ResourceDTO;

  type FileItem = {
    id: number;
    uri: string;
    filename: string;
  };

  /** 字典类型 */
  enum DictionaryCategory {
    // 系统
    System = 1,
    // 年级
    Grade = 3,
    // 品牌
    Brand = 5,
    // 标签
    Tag = 7,
    // 品质
    Quality = 8,
    // 安全性
    Safety = 9,
    // 区域
    Area = 11,
    // 规格
    Specification = 13,
    // 老师学历
    TeacherEducation = 15,
    // 材质
    Material = 17,
    // 角色
    Role = 21,
  }

  /** 状态 */
  enum StateStatus {
    // 停用
    Off = 0,
    // 启用
    On = 1,
  }

  /** 资源类型 */
  enum SourceType {
    // 图片
    Image = 1,
    // 视频
    Video = 5,
    // 文件
    File = 10,
  }

  /** 资源业务类型 */
  enum SourceBusinessType {
    // 通用类型
    Common = 1,
    // 用户 - 头像
    UserAvatar = 10,
    // 商品 - 预览图
    ProductHeader = 15,
    // 商品 - 轮播图
    ProductBanner = 16,
    // 商品 - 详情图
    ProductDetail = 17,
    // 商品 - 使用说明
    ProductInstruction = 19,
    // 学校 - 详情图
    SchoolDetail = 21,
    // 采购单
    Order = 101,
  }

  /** 地区类型 */
  enum AreaType {
    // 国家
    Country = 0,
    // 省
    Province = 1,
    // 城市
    City = 2,
    // 区县
    District = 3,
    // 街道
    Street = 4,
  }

  /** 维度 */
  enum GradeEnum {
    // 领域
    Field = 1,
    // 大维度
    ParentGrade = 2,
    // 维度
    Grade = 3,
    // 子维度
    ChildrenGrade = 4,
  }

  type RuleListItem = {
    key?: number;
    disabled?: boolean;
    href?: string;
    avatar?: string;
    name?: string;
    owner?: string;
    desc?: string;
    callNo?: number;
    status?: number;
    updatedAt?: string;
    createdAt?: string;
    progress?: number;
  };

  type RuleList = {
    data?: RuleListItem[];
    /** 列表的内容总数 */
    total?: number;
    success?: boolean;
  };

  type AITemplateListItem = {
    name: string;
    content: string;
    applicationScenarios?: string;
    aiType?: string;
    state: StateStatus;
    templateImageUrl: string;
    templatesForms: Array<{
      deletedAt: string;
      updatedBy: number;
      keyName: string;
      type: string;
      tip: string;
      required: number;
      defaultValue: string;
      extend: string;
      templateId: number;
      id: number;
      createdAt: string;
      updatedAt: string;
    }>;
    id?: number;
    createdAt: string;
    updatedAt: string;
    updatedBy: number;
    deletedAt: string;
  };

  type DocumentTemplateListItem = {
    id?: number;
    createdAt?: string;
    updatedAt?: string;
    deletedAt?: string;
    updatedBy?: number;
    documentTemplateCategory: string;
    subjectType: string;
    subjectId: number;
    name: string;
    description?: string;
    templateResource?: any;
    templateResourceId?: number;
    state?: number;
    school?: any;
  };

  type AITemplateList = {
    data?: AITemplateListItem[];
    /** 列表的内容总数 */
    total?: number;
    success?: boolean;
  };

  type LoginParams = {
    mobile?: string;
    countryCode?: string;
    code?: string;
    autoLogin?: boolean;
  };

  type ErrorResponse = {
    /** 业务约定的错误码 */
    errorCode: string;
    /** 业务上的错误信息 */
    errorMessage?: string;
    /** 业务上的请求是否成功 */
    success?: boolean;
  };

  type NoticeIconList = {
    data?: NoticeIconItem[];
    /** 列表的内容总数 */
    total?: number;
    success?: boolean;
  };

  type NoticeIconItemType = 'notification' | 'message' | 'event';

  type NoticeIconItem = {
    id?: string;
    extra?: string;
    key?: string;
    read?: boolean;
    avatar?: string;
    title?: string;
    status?: string;
    datetime?: string;
    description?: string;
    type?: NoticeIconItemType;
  };

  type TPrivilege = {
    id?: string;
    value: string;
  };
  type TUserManager = {
    id?: string;
    name: string;
    mobile: string;
    state: number;
  };

  type SyncFileData = {
    uid: string;
    filename: string;
    uri: string;
    id: number;
    hash: string;
  };
}
