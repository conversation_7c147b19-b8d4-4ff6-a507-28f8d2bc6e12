import { getJavaRequest, postJavaRequest } from './comon';

// 分页列表查询 参数 area pageNo pageSize
export async function queryAreaPropertyList(params: any) {
  return getJavaRequest('/api/global/areaAttrRel/list', params);
}

// 添加 参数如下
// {
//     "id": 0,
//     "area": "string",
//     "attributesIds": "string",
//     "matrixIds": "string",
//     "type": "string",
//     "schoolId": 0,
//     "isDeleted": 0,
//     "createTime": "string",
//     "createBy": 0,
//     "updateTime": "string",
//     "updateBy": 0
// }
export async function addAreaProperty(params: any) {
  return postJavaRequest('/api/global/areaAttrRel/add', params);
}

// 编辑 /global/areaAttrRel/edit
export async function editAreaProperty(params: any) {
  return postJavaRequest('/api/global/areaAttrRel/edit', params);
}

// 通过id删除 /global/areaAttrRel/delete
export async function deleteAreaProperty(params: any) {
  return postJavaRequest('/api/global/areaAttrRel/delete', params);
}
// 获取材料属性维度列表  /global/areaAttrRel/getMaterialAttr
export async function getMaterialAttr(params: any) {
  return getJavaRequest('/api/global/areaAttrRel/getMaterialAttr', params);
}

//  获取核心经验子维度列表 /global/areaAttrRel/getMatrix
export async function getMatrix(params: any) {
  return getJavaRequest('/api/global/areaAttrRel/getMatrix', params);
}
