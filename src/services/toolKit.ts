import { getRequest, postRequest, postJavaRequest, getJavaRequest } from './api';

/** 查询 - 反馈列表 */
export async function geUserList(params: any) {
  return await getRequest<API.CaptchaResult>('/pts/admin/temp_user/list', params);
}

/** 获取 - 用户临时二维码 */
export async function getUserQrcode(params: Record<string, any>) {
  return await postRequest<API.AdminResponse>(`/pts/admin/temp_user/qrcode`, params);
}

/** 获取 -  字典分页查询*/
export async function getPageDict(params: Record<string, any>) {
  return await postJavaRequest<API.CaptchaResult>(`/api/global/sysDict/list`, params);
}

/** 新增字典 */
export async function addDict(params: Record<string, any>) {
  return await postJavaRequest<API.CaptchaResult>(`/api/global/sysDict/save`, params);
}

/** 删除字典 */
export async function deleteDict(params: Record<string, any>) {
  return await postJavaRequest<API.CaptchaResult>(`/api/global/sysDict/remove/${params}`);
}

/** 修改字典 */
export async function updateDict(params: Record<string, any>) {
  return await postJavaRequest<API.CaptchaResult>(`/api/global/sysDict/update`, params);
}

/** 获取某个字典详情 */
export async function getDictDe(params: Record<string, any>) {
  return await getJavaRequest<API.CaptchaResult>(`/api/global/sysDict/getInfo/${params}`);
}

/** 字典项 -  分页查询 */
export async function getDictItem(params: Record<string, any>) {
  return await postJavaRequest<API.CaptchaResult>(`/api/global/sysDictItem/page`, params);
}

/** 新增字典项 */
export async function addDictItem(params: Record<string, any>) {
  return await postJavaRequest<API.CaptchaResult>(`/api/global/sysDictItem/save`, params);
}

/** 删除字典项 */
export async function deleteDictItem(params: Record<string, any>) {
  return await postJavaRequest<API.CaptchaResult>(`/api/global/sysDictItem/remove/${params}`);
}

/** 修改字典项 */
export async function updateDictItem(params: Record<string, any>) {
  return await postJavaRequest<API.CaptchaResult>(`/api/global/sysDictItem/update`, params);
}
