// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';
import { message } from 'antd';
enum EEnv {
  local = 'local',
  localhost = 'localhost',
  development = 'development',
  production = 'production',
}

const serveUrlMap: any = {
  [EEnv.local]: 'http://************:3582',
  [EEnv.development]: 'https://api.mypacelab.com',
  devjava: 'https://japi.mypacelab.com',
  // [EEnv.staging]: 'https://dev-api.mypacelab.com',
  [EEnv.production]: 'https://api.mypacelab.com',
  prodjava: 'https://japi.mypacelab.com',
};

export const generateUrl = (url: string) => {
  let param = process.env.APP_ENV === 'production' ? 'prodjava' : 'devjava';
  // 判断是否是Java API请求

  const baseUrl = url.startsWith('/api')
    ? serveUrlMap[param]
    : serveUrlMap[process.env.APP_ENV || 'development'];
  console.log(process.env.APP_ENV);

  return `${baseUrl}`;
};

export const BASE_URL = serveUrlMap[process.env.APP_ENV || 'development'];

/** 本地存储 - 读写token */
export function setToken(token: string): void {
  localStorage.setItem('token', token);
}

/** 本地存储 - 清空token */
export function clearToken() {
  localStorage.removeItem('token');
}

export function getToken(): string {
  const tk = localStorage.getItem('token');
  if (tk) {
    return tk as string;
  }
  return '';
}

/**
 * 通用 - post方法
 * @param url
 * @param body
 * @param options
 * @returns
 */
export async function postRequest<T extends API.AdminResponse>(
  url: string,
  body?: any,
  options?: API.RequestOptions,
) {
  const token = await getToken();

  const result: API.AdminResponse = await request<T>(`${BASE_URL}${url}`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json', Authorization: token },
    data: body,
    ...options,
  });

  if (result.status !== 0 && result.status !== 1) {
    const error: any = new Error(result.message);
    // throw error;
    message.error(result.message);
  }

  return result;
}

/**
 * 通用 - put方法
 * @param url
 * @param body
 * @param options
 * @returns
 */
export async function putRequest<T extends API.AdminResponse>(
  url: string,
  body?: any,
  options?: API.RequestOptions,
) {
  const token = await getToken();

  const result: API.AdminResponse = await request<T>(`${BASE_URL}${url}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json', Authorization: token },
    data: body,
    ...options,
  });

  if (result.status !== 0 && result.status !== 1) {
    const error: any = new Error(result.message);
    throw error;
  }

  return result;
}

/**
 * 通用 - get方法
 * @param url
 * @param params
 * @param options
 * @returns
 */
export async function getRequest<T extends API.AdminResponse>(
  url: string,
  params: any = {},
  options?: API.RequestOptions,
) {
  const token = await getToken();

  const result = await request<T>(`${BASE_URL}${url}`, {
    method: 'GET',
    headers: { Authorization: token },
    params,
    ...options,
  });

  if (result.status !== 0 && result.status !== 1) {
    const error: any = new Error(result.message);
    // throw error;
  }

  return result;
}

/**
 * 通用 - delete方法
 * @param url
 * @param body
 * @param options
 * @returns
 */
export async function deleteRequest<T extends API.AdminResponse>(
  url: string,
  body?: any,
  options?: API.RequestOptions,
) {
  const token = await getToken();

  const result: API.AdminResponse = await request<T>(`${BASE_URL}${url}`, {
    method: 'DELETE',
    headers: { 'Content-Type': 'application/json', Authorization: token },
    data: body,
    ...options,
  });

  if (result.status !== 0 && result.status !== 1) {
    const error: any = new Error(result.message);
    throw error;
  }

  return result;
}

/**
 * java后台 - post方法
 * @param url
 * @param body
 * @param options
 * @returns
 */
export async function postJavaRequest<T extends API.AdminResponse>(
  url: string,
  body?: any,
  options?: API.RequestOptions,
) {
  const token = await getToken();

  // 检查是否是 FormData
  const isFormData = body instanceof FormData;

  let headers: Record<string, string> = { Authorization: token };
  let data = body;

  if (!isFormData) {
    // 如果不是 FormData，使用 JSON 格式
    headers['Content-Type'] = 'application/json';
    const { token: urlToken, ...rest } = body || {};
    data = rest;
  }
  // 如果是 FormData，不设置 Content-Type，让浏览器自动设置

  const result: API.AdminResponse = await request<T>(`${generateUrl(url)}${url}`, {
    method: 'POST',
    headers,
    data,
    ...options,
  });

  if (result.status !== 0 && result.status !== 1 && result.status !== 200) {
    const error: any = new Error(result.message);
    // message.error(result.message);
    // throw error;
  }

  return result;
}

/**
 * java后台 - get方法
 * @param url
 * @param body
 * @param options
 * @returns
 */
export async function getJavaRequest<T extends API.AdminResponse>(
  url: string,
  params: any = {},
  options?: API.RequestOptions,
) {
  const token = await getToken();

  let requestParams = {
    method: 'GET',
    headers: { Authorization: token },
    params,
    ...options,
  };

  const result: API.AdminResponse = await request<T>(`${generateUrl(url)}${url}`, requestParams);

  if (result.status !== 0 && result.status !== 1) {
    const error: any = new Error(result.message);
    message.error(result.message);
    // throw error;
  }

  return result;
}
