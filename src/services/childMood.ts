import { postJavaRequest } from './comon';

// 示例 区域材料查询 /global/material/queryMaterialInfo
// export async function queryMaterialInfo(params: Record<string, any>) {
//   return await getJavaRequest<API.CaptchaResult>('/api/global/material/queryMaterialInfo', params);
// }

// /business/moodDictionary/pageList 分页列表查询
export async function queryMoodDictionaryPageList(params: Record<string, any>) {
  return await postJavaRequest<API.CaptchaResult>('/api/business/moodDictionary/pageList', params);
}

// /business/moodDictionary/saveOrUpdate 新增或修改心情字典
export async function saveOrUpdateMoodDictionary(params: Record<string, any>) {
  return await postJavaRequest<API.CaptchaResult>(
    '/api/business/moodDictionary/saveOrUpdate',
    params,
  );
}

// /business/moodDictionary/delete/{id} 删除心情
export async function deleteMoodDictionary(id: number) {
  return await postJavaRequest<API.CaptchaResult>(`/api/business/moodDictionary/delete/${id}`, {});
}
