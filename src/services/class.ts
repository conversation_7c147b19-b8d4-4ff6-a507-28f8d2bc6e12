
import { postRequest, getRequest, putRequest } from './api'

/** 新增 - 课程 */
export async function getClass(params: Record<string, any>) {
  return await postRequest<API.CaptchaResult>('pts/business/subject', params);
}

/** 查询 - 课程详情 */
export async function fetchClassDetail(id: string | number) {
  return await getRequest(`pts/business/subject/detail/${id}`);
}

/** 更新 - 课程信息 */
export async function updateSubject(params: Record<string, any>) {
  return await putRequest('pts/business/subject/u', params);
}

/** 新增 - 课程列表 */
export async function getClassList(params: Record<string, any>) {
  return await postRequest<API.CaptchaResult>('pts/business/subject/list', params);
}

