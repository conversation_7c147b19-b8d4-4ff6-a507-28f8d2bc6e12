import { getJavaRequest, postJavaRequest } from './comon';

// 区域材料查询 /global/material/queryMaterialInfo
export async function queryMaterialInfo(params: Record<string, any>) {
  return await getJavaRequest<API.CaptchaResult>('/api/global/material/queryMaterialInfo', params);
}

// 区域材料详情查看 /global/material/queryMaterialDetail
export async function queryMaterialDetail(params: Record<string, any>) {
  return await getJavaRequest<API.CaptchaResult>(
    '/api/global/material/queryMaterialDetail',
    params,
  );
}
// 添加材料-淘宝链接-获取商品数据 /global/material/getMaterialByTaobaoUrl
export async function getMaterialByTaobaoUrl(params: Record<string, any>) {
  return await getJavaRequest<API.CaptchaResult>(
    '/api/global/material/getMaterialByTaobaoUrl',
    params,
  );
}
// 添加材料 -保存-淘宝链接and自行编辑w /global/material/saveMaterialInfo
export async function saveMaterialInfo(params: Record<string, any>) {
  return await postJavaRequest<API.CaptchaResult>('/api/global/material/saveMaterialInfo', params);
}

// /global/material/editMaterialBaseInfo /global/material/editMaterialBaseInfo
export async function editMaterialBaseInfo(params: Record<string, any>) {
  return await postJavaRequest<API.CaptchaResult>(
    '/api/global/material/editMaterialBaseInfo',
    params,
  );
}

// /global/material/deleteMaterialById 根据编号删除区域材料
export async function deleteMaterialById(params: Record<string, any>) {
  // 构建查询参数字符串
  const queryParams = new URLSearchParams();
  if (params.id) queryParams.append('id', params.id.toString());
  if (params.sourceType) queryParams.append('sourceType', params.sourceType);

  const url = `/api/global/material/deleteMaterialById?${queryParams.toString()}`;

  return await postJavaRequest<API.CaptchaResult>(url, {});
}

// /global/material/getMaterialAnalysis 获取材料分析
export async function getMaterialAnalysis(params: Record<string, any>) {
  return await getJavaRequest<API.CaptchaResult>(
    '/api/global/material/getMaterialAnalysis',
    params,
  );
}

// /global/material/generatePlayList 生成组合材料玩法
export async function generatePlayList(params: Record<string, any>) {
  return await postJavaRequest<API.CaptchaResult>('/api/global/material/generatePlayList', params);
}
// /global/material/editBookBaseInfo 材料详情 -基本信息- 编辑 - 图书

export async function editBookBaseInfo(params: Record<string, any>) {
  return await postJavaRequest<API.CaptchaResult>('/api/global/material/editBookBaseInfo', params);
}

// /global/material/queryTeacherMaterial 教师材料获取-首页数据 pageNo pageSize
export async function queryTeacherMaterial(params: Record<string, any>) {
  return await getJavaRequest<API.CaptchaResult>(
    '/api/global/material/queryTeacherMaterial',
    params,
  );
}

// 学校-班级-区域三级 /global/material/querySchoolClassArea
export async function querySchoolClassArea(params: Record<string, any>) {
  return await getJavaRequest<API.CaptchaResult>(
    '/api/global/material/querySchoolClassArea',
    params,
  );
}

// /global/material/queryMergeData 教师材料导入-重复材料合并数据及保存
export async function queryMergeData(params: Record<string, any>) {
  return await getJavaRequest<API.CaptchaResult>('/api/global/material/queryMergeData', params);
}

// /global/material/saveTeacherMaterialDetail 教师材料导入-材料保存

export async function saveTeacherMaterialDetail(params: Record<string, any>) {
  return await postJavaRequest<API.CaptchaResult>(
    '/api/global/material/saveTeacherMaterialDetail',
    params,
  );
}

// /global/material/importMaterial  添加材料-批量导入
// Query 参数 area  string  Body 参数multipart/form-data
export async function importMaterial(params: Record<string, any>) {
  const { area, file } = params;

  // 构建查询参数
  const queryParams = new URLSearchParams();
  if (area) queryParams.append('area', area);

  const url = `/api/global/material/importMaterial?${queryParams.toString()}`;

  // 创建 FormData
  const formData = new FormData();
  if (file) {
    formData.append('file', file);
  }

  // 使用 postJavaRequest，传递 FormData
  return await postJavaRequest<API.CaptchaResult>(url, formData);
}

// /global/material/getMaterialPlayList 获取组合材料玩法
export async function getMaterialPlayList(params: Record<string, any>) {
  return await getJavaRequest<API.CaptchaResult>(
    '/api/global/material/getMaterialPlayList',
    params,
  );
}

// /global/material/getTeacherMaterialPlayList 获取老师组合材料玩法
export async function getTeacherMaterialPlayList(params: Record<string, any>) {
  return await getJavaRequest<API.CaptchaResult>(
    '/api/global/material/getTeacherMaterialPlayList',
    params,
  );
}

// /global/material/deletePlay 删除玩法
export async function deletePlay(params: Record<string, any>) {
  // 添加默认参数
  const requestParams = {
    ...params,
    dataType: 'base',
    combinedId: params.combinedId || params.materialId,
    classId: params.classId,
    schoolId: params.schoolId,
  };

  return await getJavaRequest<API.CaptchaResult>('/api/global/material/deletePlay', requestParams);
}

// 玩法采纳/不采纳 /business/material/adoptPlay
export async function adoptPlay(params: Record<string, any>) {
  // 添加默认参数
  const requestParams = {
    ...params,
    dataType: 'base',
    combinedId: params.combinedId || params.materialId,
    classId: params.classId,
    schoolId: params.schoolId,
  };

  return await getJavaRequest<API.CaptchaResult>('/api/global/material/adoptPlay', requestParams);
}

// 老师玩法导入到基础玩法 /business/material/saveTeacherPlayToBasePlay
export async function saveTeacherPlayToBasePlay(params: Record<string, any>) {
  const { playIds, combinedId } = params;

  // 构建查询参数
  const queryParams = new URLSearchParams();
  if (combinedId) queryParams.append('combinedId', combinedId.toString());

  // 处理 playIds 数组参数
  if (playIds && Array.isArray(playIds)) {
    playIds.forEach((id) => queryParams.append('playIds', id.toString()));
  }

  const url = `/api/global/material/saveTeacherPlayToBasePlay?${queryParams.toString()}`;

  return await postJavaRequest<API.CaptchaResult>(url, {});
}

// 发布材料 /global/material/publishMaterial combinedIds array[integer]
export async function publishMaterial(params: Record<string, any>) {
  const { combinedIds } = params;

  // 构建查询参数
  const queryParams = new URLSearchParams();

  // 处理 combinedIds 数组参数
  if (combinedIds && Array.isArray(combinedIds)) {
    combinedIds.forEach((id) => queryParams.append('combinedIds', id.toString()));
  }

  const url = `/api/global/material/publishMaterial?${queryParams.toString()}`;

  return await postJavaRequest<API.CaptchaResult>(url, {});
}

// 材料属性 查询材料属性列表 /global/material/listAddAttributes  可选参数parentId
export async function listAddAttributes(params: Record<string, any>) {
  return await getJavaRequest<API.CaptchaResult>('/api/global/material/listAddAttributes', params);
}

// 新增材料属性 /global/material/addAttributes
export async function addAttributes(params: Record<string, any>) {
  return await postJavaRequest<API.CaptchaResult>('/api/global/material/addAttributes', params);
}

// 编辑材料属性  /global/material/editAttributes
export async function editAttributes(params: Record<string, any>) {
  return await postJavaRequest<API.CaptchaResult>('/api/global/material/editAttributes', params);
}

// 通过id删除材料属性 /global/material/deleteAttributes Query 参数 ids
export async function deleteAttributes(params: Record<string, any>) {
  // 构建查询参数字符串
  const queryParams = new URLSearchParams();
  if (params.ids) queryParams.append('ids', params.ids.toString());

  const url = `/api/global/material/deleteAttributes?${queryParams.toString()}`;

  return await getJavaRequest<API.CaptchaResult>(url);
}

// 查询材料属性字段 /global/material/listAddAttributesDict
export async function listAddAttributesDict(params: Record<string, any>) {
  return await getJavaRequest<API.CaptchaResult>(
    '/api/global/material/listAddAttributesDict',
    params,
  );
}

// 更新材料分析 /global/material/editMaterialAnalysis
export async function updateMaterialAnalysis(params: Record<string, any>) {
  return await postJavaRequest<API.CaptchaResult>(
    '/api/global/material/editMaterialAnalysis',
    params,
  );
}
