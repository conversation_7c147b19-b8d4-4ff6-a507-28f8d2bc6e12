import { getRequest, postRequest, putRequest } from './comon';

/** 获取 - 用户列表 */
export async function fetchUserManagerList(params: {
  current: number;
  pageSize: number;
  offset?: number;
  title?: string;
  state?: API.StateStatus;
}) {
  return await getRequest('/pts/admin/userManager/list', params);
}

/** 修改 - 用户信息 */
export async function updateUserManager(params: { id: number; value: string; sort: number }) {
  return await putRequest('/pts/admin/userManager/u', params);
}

/** 修改 - 用户状态 */
export async function updateUserManagerState(params: { id: number; value: string; sort: number }) {
  return await putRequest('/pts/admin/userManager/s', params);
}

/** 获取 - 用户详情 */
export async function fetchUserManagerDetail(id: number) {
  return await getRequest(`/pts/admin/userManager/detail/${id}`);
}

export async function addUserManager(resource: API.ResourceItem) {
  return await postRequest('/pts/admin/userManager', resource);
}

/** 修改 - 用户角色 */
export async function updateUserRole(params: { id: number; value: string; sort: number }) {
  return await putRequest('/pts/admin/userRole/save', params);
}

/** 查询 - 用户角色 */
export async function getUserRoleDetail(id: number) {
  return await getRequest(`/pts/admin/userRole/detail/${id}`);
}

/** 查询 - 用户角色 */
export async function getClassByScholl(params: { schoolIds: string[] }) {
  return await getRequest(`/pts/admin/class/list_v2`, params);
}

/** 查询 - 操作日志 */
export async function getoPerateLog(params: {
  current: number;
  pageSize: number;
  operateUserId?: number;
  startAt?: string;
  endAt?: string;
  state?: API.StateStatus;
}) {
  return await getRequest(`/pts/admin/operate_log/list`, params);
}

/** 重置 - 用户密码 */
export async function resetUserPassWord(params: { userId: number }) {
  return await postRequest(`/pts/admin/userManager/reset_pwd`, params);
}
