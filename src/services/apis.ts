import { deleteRequest, getRequest, postRequest, putRequest } from './comon';
/** 发送验证码 POST /pts/sms */
export async function getCaptcha(body: {
  /** 手机号 */
  mobile?: string;
  /** 国别号 */
  countryCode?: string;
}) {
  return await postRequest<API.CaptchaResult>('/pts/sms', body);
}

/** 登录 /pts/admin/user/phone */
export async function adminLogin(body: {
  /** 手机号 */
  mobile?: string;
  /** 国别号 */
  countryCode?: string;
  /** 验证码 */
  code?: string;
}) {
  return await postRequest<API.LoginResult>('/pts/admin/user/phone', body);
}

/** 获取 - 用户信息 */
export async function fetchCurrentUser() {
  return await getRequest('/pts/admin/user/me');
}

/** 获取 - 当前用户已绑定的学校 */
export async function fetchUserSchools() {
  return await getRequest('/pts/business/school/my_list');
}
/** 获取 - 当前用户已绑定的班级 */
export async function fetchUserClasses() {
  return await getRequest('/pts/business/class/my_list');
}

/** 获取 - 当前用户 选择学校+班级 操作 */
export async function selectUserSchoolsAndClasses(body: {
  currentSchoolId?: number | null;
  currentClassId?: number | null;
}) {
  return await putRequest('/pts/business/user/change', body);
}

/** 获取 - 当前用户 已选择的学习+班级 查询 */
export async function fetchUserSelectedSchoolsAndClasses() {
  return await getRequest('/pts/business/user/me');
}

/** 获取 - 资源列表 */
export async function fetchSourceList(params: {
  current: number;
  pageSize: number;
  offset?: number;
  category?: API.SourceType;
  fileCategory?: API.SourceBusinessType;
}) {
  return await getRequest('/pts/admin/resource/list', params);
}

/** ==== 图片上传 */
export async function fetchUploadToken() {
  return await getRequest('/pts/admin/resource/auth');
}
/** 删除 - 资源信息 */
export async function delResource(options?: { [key: string]: any }) {
  return deleteRequest<Record<string, any>>(`/pts/admin/resource/${options?.id}`);
}

export async function createResource(resource: API.ResourceDTO) {
  return await postRequest('/pts/admin/resource/sync', resource);
}

export async function fetchResourceByHash(body: { hash: string; filename: string }) {
  return await postRequest(`/pts/admin/resource/hash`, body);
}

export async function updateResource(resource: API.ResourceItem) {
  return await postRequest('/api/oss/editImgInfo', resource);
}

/** 获取 - 通用枚举值 */
export async function fetchEnum() {
  return await getRequest('/pts/enum');
}

/** ==== 配置项 */
/** 获取 - 配置项列表 */
export async function fetchDictionaryList(params: {
  current: number;
  pageSize: number;
  offset?: number;
  category?: API.DictionaryCategory;
  state?: API.StateStatus;
}) {
  return await getRequest('/pts/admin/dictionary/list', params);
}

/** 新增 - 配置项 */
export async function addDictionary(params: { category: number; value: string; sort: number }) {
  return await postRequest('/pts/admin/dictionary', params);
}

/** 修改 - 配置项 */
export async function updateDictionary(params: { id: number; value: string; sort: number }) {
  return await putRequest('/pts/admin/dictionary/u', params);
}

/** 停用 - 配置项 */
export async function updateDictionaryState(params: { id: number; state: number }) {
  return await putRequest('/pts/admin/dictionary/s', params);
}

/** 获取 - 地区数据 */
export async function fetchArea(params: {
  current: number;
  pageSize: number;
  offset?: number;
  state?: API.StateStatus;
  pid?: number;
  areaType: API.AreaType;
}) {
  return await getRequest('/pts/admin/dictionary/area', params);
}

/** ==== 商品管理 */
/** 获取 - 商品列表 */
export async function fetchProductList(params: {
  current: number;
  pageSize: number;
  offset?: number;
  title?: string;
  state?: API.StateStatus;
}) {
  return await getRequest('/pts/admin/product/list', params);
}

/** 新增 - 商品 */
export async function addProduct(params: any) {
  return await postRequest('/pts/admin/product', params);
}

/** 更新 - 商品 */
export async function updateProduct(params: any) {
  return await putRequest('/pts/admin/product/u', params);
}

/** 停用 - 商品 */
export async function updateProductState(params: { id: number; state: number }) {
  return await putRequest('/pts/admin/product/s', params);
}

/** 获取 - 商品详情 */
export async function fetchProductDetail(id: number) {
  return await getRequest(`/pts/admin/product/detail/${id}`);
}

/** 导入 - 淘宝商品 */
export async function importProductFromTaobao(params: any) {
  return await postRequest('/pts/admin/product/import-products-from-taobao', params);
}
/** 商品导出excel */
export async function fetchProductExport(params: any) {
  return await getRequest('/pts/admin/excel/product', params);
}

/** ==== 学校管理 */
/** 获取 - 学校列表 */
export async function fetchSchoolList(params: {
  current: number;
  pageSize: number;
  offset?: number;
  title?: string;
  state?: API.StateStatus;
}) {
  return await getRequest('/pts/admin/school/list', params);
}

/** 新增 - 学校 */
export async function addSchool(params: any) {
  return await postRequest('/pts/admin/school', params);
}

/** 更新 - 学校 */
export async function updateSchool(params: any) {
  return await putRequest('/pts/admin/school/u', params);
}

/** 停用 - 学校 */
export async function updateSchoolState(params: { id: number; state: number }) {
  return await putRequest('/pts/admin/school/s', params);
}

/** 获取 - 学校详情 */
export async function fetchSchoolDetail(id: number) {
  return await getRequest(`/pts/admin/school/detail/${id}`);
}

/** ==== 班级管理 */
/** 获取 - 班级列表 */
export async function fetchClassList(params: {
  current: number;
  pageSize: number;
  offset?: number;
  title?: string;
  state?: API.StateStatus;
}) {
  return await getRequest('/pts/admin/class/list', params);
}

/** 新增 - 班级 */
export async function addClass(params: any) {
  return await postRequest('/pts/admin/class', params);
}

/** 更新 - 班级 */
export async function updateClass(params: any) {
  return await putRequest('/pts/admin/class/u', params);
}

/** 停用 - 班级 */
export async function updateClassState(params: { id: number; state: number }) {
  return await putRequest('/pts/admin/class/s', params);
}

/** 获取 - 班级详情 */
export async function fetchClassDetail(id: number) {
  return await getRequest(`/pts/admin/class/detail/${id}`);
}

/** 获取 - 班级统计 */
export async function fetchClassSta(params: {
  current: number;
  pageSize: number;
  offset?: number;
  state?: API.StateStatus;
  id: number;
  category: number;
}) {
  return await getRequest('/pts/admin/class/sta', params);
}

/** ==== 班级商品管理 */
/** 获取 - 班级商品列表 */
export async function fetchClassProductList(params: {
  current: number;
  pageSize: number;
  offset?: number;
  schoolClassId: number;
  title?: string;
  state?: API.StateStatus;
}) {
  return await getRequest('/pts/admin/class_product/list', params);
}

/** 新增 - 班级商品 */
export async function addClassProduct(params: any) {
  return await postRequest('/pts/admin/class_product', params);
}

/** 更新 - 班级商品 */
export async function updateClassProduct(params: any) {
  return await putRequest('/pts/admin/class_product/u', params);
}

/** 停用 - 班级商品 */
export async function updateClassProductState(params: { id: number; state: number }) {
  return await putRequest('/pts/admin/class_product/s', params);
}

/** 获取 - 班级商品详情 */
export async function fetchClassProductDetail(id: number) {
  return await getRequest(`/pts/admin/class_product/detail/${id}`);
}

/** ==== 矩阵管理 */
/** 获取 - 矩阵树形结构 */
export async function fetchMatrixTree(params: { state?: API.StateStatus }) {
  return await getRequest('/pts/admin/matrix/tree', params);
}

/** 获取 - 根据pid查询矩阵列表 */
export async function fetchMatrixListByPid(params: { pid: number }) {
  return await getRequest('/pts/admin/matrix/list', params);
}

/** 获取 - 查询平铺的矩阵列表 */
export async function fetchMatrixListV2(params: { title?: string }) {
  return await getRequest('/pts/admin/matrix/list_v2', params);
}

/** 新增 */
export async function addMatrix(params: { title: string; pid: number; grade: API.GradeEnum }) {
  return await postRequest('/pts/admin/matrix', params);
}

/** 更新 */
export async function updateMatrix(params: {
  id: number;
  title: string;
  pid: number;
  grade: API.GradeEnum;
}) {
  return await putRequest('/pts/admin/matrix/u', params);
}

/** 停用 */
export async function updateMatrixState(params: { id: number; state: number }) {
  return await putRequest('/pts/admin/matrix/s', params);
}

/** 获取详情 */
export async function fetchMatrixDetail(id: number) {
  return await getRequest(`/pts/admin/matrix/detail/${id}`);
}

/** ==== 指标管理 */
/** 获取 - 指标列表，其中title的查询包含指标名称和矩阵名称 */
export async function fetchTargetList(params: {
  current: number;
  pageSize: number;
  offset?: number;
  title?: string;
  state?: API.StateStatus;
}) {
  return await getRequest('/pts/admin/target/list', params);
}

/** 获取 - 指标列表，其中title的查询为指标名称 */
export async function fetchTargetListV2(params: { title?: string }) {
  return await getRequest('/pts/admin/target/list_v2', params);
}

/** 新增 - 指标 */
export async function addTarget(params: { title: string; matrix3Id: number; gradeId: number }) {
  return await postRequest('/pts/admin/target', params);
}

/** 更新 - 指标 */
export async function updateTarget(params: {
  title: string;
  matrix3Id: number;
  gradeId: number;
  id: number;
}) {
  return await putRequest('/pts/admin/target/u', params);
}

/** 停用 - 指标 */
export async function updateTargetState(params: { id: number; state: number }) {
  return await putRequest('/pts/admin/target/s', params);
}

/** 获取 - 指标详情 */
export async function fetchTargetDetail(id: number) {
  return await getRequest(`/pts/admin/target/detail/${id}`);
}

/** 指标关联商品 */
export async function postRelateProduct(params: { targetId: number; productIds: any }) {
  return await postRequest('/pts/admin/target/product', params);
}

/** 关联指标 */
export async function postRelateTarget(params: { targetId: number; productIds: any }) {
  return await postRequest('/pts/admin/product/tp', params);
}

/** 根据商品ID查询指标 */
export async function fetchTargetByProduct(id: number) {
  return await getRequest(`/pts/admin/target/product/${id}`);
}

/** 获取当前的用户 GET /api/currentUser */
export async function currentUser(options?: { [key: string]: any }) {
  return request<{
    data: API.CurrentUser;
  }>('/api/currentUser', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 退出登录接口 POST /api/login/outLogin */
export async function outLogin(options?: { [key: string]: any }) {
  return request<Record<string, any>>('/api/login/outLogin', {
    method: 'POST',
    ...(options || {}),
  });
}

/** 登录接口 POST /api/login/account */
export async function login(body: API.LoginParams, options?: { [key: string]: any }) {
  return request<API.LoginResult>('/api/login/account', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 此处后端没有提供注释 GET /api/notices */
export async function getNotices(options?: { [key: string]: any }) {
  return request<API.NoticeIconList>('/api/notices', {
    method: 'GET',
    ...(options || {}),
  });
}

/** 获取规则列表 GET /api/rule */
export async function rule(
  params: {
    // query
    /** 当前的页码 */
    current?: number;
    /** 页面的容量 */
    pageSize?: number;
  },
  options?: { [key: string]: any },
) {
  return request<API.RuleList>('/api/rule', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 更新规则 PUT /api/rule */
export async function updateRule(options?: { [key: string]: any }) {
  return request<API.RuleListItem>('/api/rule', {
    method: 'POST',
    data: {
      method: 'update',
      ...(options || {}),
    },
  });
}

/** 新建规则 POST /api/rule */
export async function addRule(options?: { [key: string]: any }) {
  return request<API.RuleListItem>('/api/rule', {
    method: 'POST',
    data: {
      method: 'post',
      ...(options || {}),
    },
  });
}

/** 删除规则 DELETE /api/rule */
export async function removeRule(options?: { [key: string]: any }) {
  return request<Record<string, any>>('/api/rule', {
    method: 'POST',
    data: {
      method: 'delete',
      ...(options || {}),
    },
  });
}

/** 权限管理列表 GET /pts/admin/permission/api/list */
export async function getPermission() {
  return getRequest<Record<string, any>>('/pts/admin/permission/api/list');
}

/** 更新权限管理 GET /pts/admin/permission/detail */
export async function updatePermission(params: { id: string }) {
  return postRequest<API.RuleListItem>('/pts/admin/permission/save', params);
}

/** 查询权限详情 GET /pts/admin/permission/detail */
export async function getPermissionDetail(params: { id: string }) {
  return getRequest<Record<string, any>>(`/pts/admin/permission/detail/${params.id}`);
}
