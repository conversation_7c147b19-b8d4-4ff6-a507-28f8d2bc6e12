import { deleteRequest, getRequest, postRequest, putRequest } from './comon';

export type CreateDocumentTemplateParams = {
  documentTemplateCategory: string;
  subjectType: string;
  subjectId: number;
  name: string;
  state?: number;
  description?: string;
  templateResourceId: number;
  subjectName?: string;
};
export type DocumentTemplateParams = {
  current?: number;
  pageSize?: number;
  state?: number;
  name?: string;
  subjectName?: string;
  subjectId?: number;
  subjectType?: string;
};

export const getDocumentTemplateList = async (params: DocumentTemplateParams) => {
  return await getRequest<any>('/pts/admin/document-template/list', params);
};

export const createDocumentTemplate = async (params: CreateDocumentTemplateParams) => {
  return await postRequest<API.AdminResponse>('/pts/admin/document-template', params);
};

export const updateDocumentTemplate = async (params: Record<string, any>) => {
  return await putRequest<API.AdminResponse>('/pts/admin/document-template/u', params);
};

export const deleteDocumentTemplate = async (id: number) => {
  return await deleteRequest<API.AdminResponse>(`/pts/admin/document-template/${id}`);
};

// /pts/admin/document-template/list-group-by-subject

export const getDocumentTemplateGroupList = async (params: DocumentTemplateParams) => {
  return await getRequest<any>('/pts/admin/document-template/list-group-by-subject', params);
};
