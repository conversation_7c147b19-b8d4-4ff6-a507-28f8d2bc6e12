/*
 * @Description: 文件上传相关方法
 * @Author: SaraSL
 * @Date: 2024-02-27 12:03:09
 */
import OSS from 'ali-oss';
import * as _ from 'lodash';

import { message } from 'antd';

import { createResource, fetchResourceByHash, fetchUploadToken } from '@/services/api';
import { file2Md5, str2Md5 } from '@/services/utils';

export const fetchClient = async (callback: any) => {
  const res = await fetchUploadToken();
  if (!res?.data) {
    return;
  }
  const __client = new OSS({ ...res?.data });
  callback(__client);
};

/**
 * @see 简单上传 https://help.aliyun.com/document_detail/383950.htm?spm=a2c4g.11186623.0.0.28de7dbbJbmY7j#concept-2161572
 * @param client
 * @param link
 * @param {File|Blob|Buffer} file
 * @returns
 */
export const upload2OSS = async (client: any, filepath: string, file: any) => {
  if (client) {
    try {
      const headers = { 'Content-Encoding': 'UTF-8' };
      // 查不到数据，则上传到阿里云
      const result = await client.put(filepath, file, headers);
      // 判断 - 阿里云结果
      const res = _.get(result, 'res');

      if (
        _.get(res, 'aborted') ||
        (_.get(res, 'status') !== 200 && _.get(res, 'statusCode') !== 200)
      ) {
        message.error('图片上传出错，请重试');
      }
    } catch (error: any) {
      console.log('error', error);

      message.error(`图片上传出错:${error.message}`);
    }
    return;
  }
  message.error('图片上传出错，请刷新页面后重试');
};

/**
 * 字符串上传
 * @param client
 * @param str
 * @param goodId
 * @returns
 */
export const handleStrUpload = async (
  client: any,
  str: string,
  filename: string,
  filepath: string,
) => {
  const hide = message.loading('上传中...', 0);
  const md5 = str2Md5(str);

  const __resource = {
    hash: md5,
    filename: filename,
    filepath: filepath,
    fileCategory: 0,
    category: 1,
  };
  await upload2OSS(client, filepath, new Blob([str]));
  const { data } = await createResource(__resource);
  hide();
  return data;
};

/**
 * 文件上传
 * @param client
 * @param file
 * @returns
 */
export const handleUpload = async (client: any, file: any) => {
  const hide = message.loading('上传中...', 0);
  const md5 = await file2Md5(file);
  const filename = file.name;

  const result = await fetchResourceByHash({ hash: md5, filename });

  const filepath = result?.data?.filepath;

  if (!result?.data?.id) {
    // 无此资源，则上传到OSS
    await upload2OSS(client, filepath, file);
  }

  const __resource = {
    hash: md5,
    filename,
    filepath,
    fileCategory: 0,
    category: 1,
  };

  const { data } = await createResource(__resource);

  hide();
  return data;
};
