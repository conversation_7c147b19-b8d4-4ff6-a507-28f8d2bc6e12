/* eslint-disable guard-for-in */
import { timeColumn } from '@/components/ColumnRender';
import {
  fetchDictionaryList,
  fetchMatrixListV2,
  fetchProductList,
  fetchTargetListV2,
  importProductFromTaobao,
  updateProductState,
} from '@/services/api';
import { DictionaryCategory, Grade, ProductCategory } from '@/services/constants';
import { exportFile, formatOption } from '@/services/utils';
import { DeleteOutlined, ExportOutlined, PlusOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import {
  PageContainer,
  ProFormDigit,
  ProFormSelect,
  ProFormText,
  ProTable,
} from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { Button, Col, Form, Image, Modal, Row, Tag, Tooltip, message } from 'antd';
import TextArea from 'antd/es/input/TextArea';
import { notification } from 'antd/lib';
import { debounce, isEmpty } from 'lodash';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import RelateTargetModal from './components/relateTarget';

const ProductList: React.FC = () => {
  const actionRef = useRef<ActionType>();

  const [relateModalOpen, setRelateModalOpen] = useState<boolean>(false);

  const [form] = Form.useForm();

  const [currentRow, setCurrentRow] = useState<any>({});

  const [selectedRows, setSelectedRows] = useState<any>([]);

  const { initialState } = useModel('@@initialState');
  const { dictionaryList } = initialState || {};

  // 区域列表
  const [areas, setAreas] = useState<any>([]);

  // 品质
  const [qualitys, setQualitys] = useState<any>([]);

  // 安全性
  const [safetys, setSafetys] = useState<any>([]);

  // 年级列表
  const [grades, setGrades] = useState<any>([]);

  // 淘宝商品录入
  const [isProductTextShow, setIsProductTextShow] = useState(false);
  const [productTexts, setProductTexts] = useState<string[]>(['']);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (!isEmpty(dictionaryList)) {
      const result = dictionaryList?.map((item) => {
        return {
          value: item?.id,
          label: item?.value,
          category: item?.category,
        };
      });

      const _areas = result.filter((item) => item.category === DictionaryCategory.Area);
      const _qualitys = result.filter((item) => item.category === DictionaryCategory.Quality);
      const _safetys = result.filter((item) => item.category === DictionaryCategory.Safety);
      const _grades = result.filter((item) => item.category === DictionaryCategory.Grade);

      setAreas(_areas);
      setQualitys(_qualitys);
      setSafetys(_safetys);
      setGrades(_grades);
    }
  }, []);

  // 删除配置项
  const handleDelete = async (record: any) => {
    const hide = message.loading('正在更新状态');

    try {
      await updateProductState({
        id: record?.id,
        state: record?.state === 0 ? 1 : 0,
      });
      hide();
      message.success('Update successfully and will refresh soon');
      if (actionRef.current) {
        actionRef.current.reload();
      }
      return true;
    } catch (error) {
      hide();
      message.error(error?.message || 'Update failed, please try again');
      return false;
    }
  };

  const handleRelateModalOpen = ({ record }) => {
    form.resetFields();
    setCurrentRow({});

    setTimeout(() => {
      setCurrentRow(record);
      setRelateModalOpen(true);
    }, 0);
  };

  // 商品导出
  const handleExport = async () => {
    message.open({
      type: 'loading',
      content: '正在导出中...',
      duration: 60,
    });
    const ids = selectedRows.map((item) => item.id);
    await exportFile({ ids });
  };

  /**
   * 搜索防抖处理
   */
  const debounceSearchBrand = debounce(async (value, func, callback, params) => {
    const { data } = await func({ ...params, value: value?.keyWords });
    callback(formatOption(data));
  }, 300); // 设置防抖时间间隔为300毫秒

  const debounceSearchTag = debounce(async (value, func, callback, params) => {
    const { data } = await func({ ...params, value: value?.keyWords });
    callback(formatOption(data));
  }, 300); // 设置防抖时间间隔为300毫秒

  const debounceSearchMaterial = debounce(async (value, func, callback, params) => {
    const { data } = await func({ ...params, value: value?.keyWords });
    callback(formatOption(data));
  }, 300); // 设置防抖时间间隔为300毫秒

  const debounceSearchTarget = debounce(async (value, func, callback) => {
    const { data } = await func({ title: value?.keyWords });
    const options = data.map((item) => {
      return {
        ...item,
        label: item.title,
        value: item.id,
      };
    });
    callback(options);
  }, 300); // 设置防抖时间间隔为300毫秒

  const debounceSearchMatrix = debounce(async (value, func, callback) => {
    const { data } = await func({ title: value?.keyWords });
    const options = data.map((item) => {
      return {
        ...item,
        label: item.title,
        value: item.id,
      };
    });
    callback(options);
  }, 300); // 设置防抖时间间隔为300毫秒

  const handleImport = useCallback(() => {
    if (!productTexts.length) {
      message.warning('输入内容为空');
      return;
    }
    setIsLoading(true);
    importProductFromTaobao({
      userInputs: productTexts,
    })
      .then((response) => {
        if (response.status === 0) {
          const { products, failedToImportUserInputs } = response.data;
          // 有成功商品就刷新列表
          if (products.length) {
            actionRef.current?.reload();
          }
          let messageText = `已导入成功${products.length}条商品`;
          if (failedToImportUserInputs.length) {
            messageText += `,有${failedToImportUserInputs.length}条商品导入失败`;
          }
          notification.info({
            message: messageText,
            // 如果导入成功的商品为1条 显示跳转编辑按钮 用户选择是否跳转
            btn:
              products.length === 1 ? (
                <Button type="primary" key={'ok'}>
                  {'跳转编辑'}
                </Button>
              ) : null,
            onClick: () => {
              // 跳转ID 取成功的商品  products 的id
              window.open(`/product/add?id=${products[0]?.id}`, '_blank');
            },
          });
          setProductTexts(['']);
          setIsProductTextShow(false);
        }
      })
      .catch((error) => {
        console.log(error);
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, [productTexts]);

  const onProductTextChange = useCallback(
    (text: string, index: number) => {
      const texts = [...productTexts];

      texts[index] = text;
      setProductTexts(texts);
    },
    [productTexts],
  );
  const columns: ProColumns<API.RuleListItem>[] = [
    {
      title: '操作',
      ellipsis: true,
      dataIndex: 'action',
      key: 'action',
      valueType: 'option',
      width: '14em',
      search: false,
      render: (_, record) => [
        <Button
          size="small"
          type="primary"
          key="edit"
          onClick={() => {
            window.open(`/product/add?id=${record?.id}`, '_blank');
          }}
        >
          编辑
        </Button>,
        <Button
          size="small"
          type="primary"
          key="delete"
          danger={!!record?.state}
          onClick={async () => {
            await handleDelete(record);
          }}
        >
          {record?.state === 0 ? '上架' : '下架'}
        </Button>,
        <Button
          size="small"
          type="primary"
          key="relate"
          onClick={() => {
            handleRelateModalOpen({ record });
          }}
        >
          关联指标
        </Button>,
        <Button
          size="small"
          type="link"
          key="detail"
          href={`/product/detail/${record?.id}`}
          target="_blank"
        >
          查看详情
        </Button>,
      ],
    },
    {
      title: '商品名称',
      dataIndex: 'title',
      valueType: 'text',
      width: 120,
      render: (result) => {
        return (
          <Tooltip title={result}>
            <div className="text-ellipsis-three">{result}</div>
          </Tooltip>
        );
      },
      search: false,
    },
    {
      title: '淘宝名称',
      dataIndex: 'taobaoTitle',
      valueType: 'text',
      width: 120,
      render: (result) => {
        return (
          <Tooltip title={result}>
            <div className="text-ellipsis-three">{result}</div>
          </Tooltip>
        );
      },
      search: false,
    },
    {
      title: '英文名称',
      dataIndex: 'englishTitle',
      valueType: 'text',
      width: 120,
      render: (result) => {
        return (
          <Tooltip title={result}>
            <div className="text-ellipsis-three">{result}</div>
          </Tooltip>
        );
      },
      search: false,
    },
    {
      title: '编号ID',
      dataIndex: 'id',
      hideInTable: true,
      renderFormItem: () => {
        return <ProFormText name="id" />;
      },
    },
    {
      title: <span>名称</span>,
      tooltip: '可搜索商品名称、淘宝名称、英文名称',
      dataIndex: 'keyword',
      hideInTable: true,
      renderFormItem: () => {
        return <ProFormText name="keyword" />;
      },
    },
    {
      title: '品牌',
      dataIndex: 'brand',
      valueType: 'text',
      width: 120,
      render: (result) => {
        return result?.value;
      },
      search: false,
    },
    {
      title: '品牌',
      dataIndex: 'brandId',
      hideInTable: true,
      renderFormItem: () => {
        return (
          <ProFormSelect
            name="brandId"
            showSearch
            request={async (value) => {
              return new Promise((resolve) => {
                debounceSearchBrand(value, fetchDictionaryList, resolve, {
                  current: 1,
                  pageSize: 50,
                  category: 5,
                  state: 1,
                });
              });
            }}
          />
        );
      },
    },
    {
      title: '标签',
      dataIndex: 'tagsTitles',
      valueType: 'text',
      width: 120,
      search: false,
      render: (result) => {
        return !isEmpty(result) && result !== '-'
          ? result.map((item, index) => (
              <Tag key={index} color="blue">
                {item}
              </Tag>
            ))
          : '-';
      },
    },
    {
      title: '标签',
      dataIndex: 'tagIds',
      hideInTable: true,
      renderFormItem: () => {
        return (
          <ProFormSelect
            name="tagIds"
            showSearch
            fieldProps={{
              mode: 'multiple',
            }}
            request={async (value) => {
              return new Promise((resolve) => {
                debounceSearchTag(value, fetchDictionaryList, resolve, {
                  current: 1,
                  pageSize: 50,
                  category: 7,
                  state: 1,
                });
              });
            }}
          />
        );
      },
    },
    {
      title: '预览图',
      dataIndex: 'header',
      valueType: 'text',
      width: 120,
      search: false,
      render: (result) => {
        return <Image src={result?.uri} height={50} />;
      },
    },
    {
      title: '淘宝售价',
      dataIndex: 'taobaoPrice',
      valueType: 'text',
      width: 80,
      render: (value) => {
        return `${value}元`;
      },
      search: false,
    },
    {
      title: '淘宝售价上限',
      dataIndex: 'taobaoPriceStart',
      hideInTable: true,
      renderFormItem: () => {
        return <ProFormDigit name="taobaoPriceStart" />;
      },
    },
    {
      title: '淘宝售价下限',
      dataIndex: 'taobaoPriceEnd',
      hideInTable: true,
      renderFormItem: () => {
        return <ProFormDigit name="taobaoPriceEnd" />;
      },
    },
    {
      title: '划线价',
      dataIndex: 'listPrice',
      valueType: 'text',
      width: 80,
      render: (value) => {
        return `${value}元`;
      },
      search: false,
    },
    {
      title: '划线价上限',
      dataIndex: 'listPriceStart',
      hideInTable: true,
      renderFormItem: () => {
        return <ProFormDigit name="listPriceStart" />;
      },
    },
    {
      title: '划线价下限',
      dataIndex: 'listPriceEnd',
      hideInTable: true,
      renderFormItem: () => {
        return <ProFormDigit name="listPriceEnd" />;
      },
    },
    {
      title: '年龄范围',
      dataIndex: 'barcode',
      valueType: 'text',
      width: 80,
      render: (_, record) => {
        return `${record?.minAge} ~ ${record?.maxAge}`;
      },
      search: false,
    },
    {
      title: '淘宝链接',
      dataIndex: 'taobaoLink',
      valueType: 'text',
      width: 160,
      render: (result) => {
        return (
          <Tooltip title={result}>
            <div className="text-ellipsis-three">
              <a href={result} target="_blank" rel="noreferrer">
                {result}
              </a>
            </div>
          </Tooltip>
        );
      },
      search: false,
    },
    {
      title: '编号',
      dataIndex: 'id',
      valueType: 'text',
      width: 80,
      search: false,
    },
    {
      title: '分类',
      dataIndex: 'category',
      valueType: 'text',
      width: 120,
      render: (result) => {
        return ProductCategory[result];
      },
      search: false,
    },
    {
      title: '材质',
      dataIndex: 'material',
      valueType: 'text',
      width: 80,
      search: false,
      render: (result) => {
        return dictionaryList?.find((item) => item?.id === Number(result))?.value;
      },
    },
    {
      title: '材质',
      dataIndex: 'material',
      hideInTable: true,
      renderFormItem: () => {
        return (
          <ProFormSelect
            name="material"
            showSearch
            request={async (value) => {
              return new Promise((resolve) => {
                debounceSearchMaterial(value, fetchDictionaryList, resolve, {
                  current: 1,
                  pageSize: 50,
                  category: 17,
                  state: 1,
                });
              });
            }}
          />
        );
      },
    },
    {
      title: '单位',
      dataIndex: 'unit',
      valueType: 'text',
      width: 80,
      search: false,
    },
    {
      title: '规格',
      dataIndex: 'specification',
      valueType: 'text',
      width: 80,
      search: false,
    },
    {
      title: '售价',
      dataIndex: 'salePrice',
      valueType: 'text',
      width: 80,
      render: (value) => {
        return `${value}元`;
      },
      search: false,
    },
    {
      title: '售价上限',
      dataIndex: 'salePriceStart',
      hideInTable: true,
      renderFormItem: () => {
        return <ProFormDigit name="salePriceStart" />;
      },
    },
    {
      title: '售价下限',
      dataIndex: 'salePriceEnd',
      hideInTable: true,
      renderFormItem: () => {
        return <ProFormDigit name="salePriceEnd" />;
      },
    },
    {
      title: '品质',
      dataIndex: 'quality',
      valueType: 'text',
      width: 80,
      search: false,
      render: (result) => {
        return dictionaryList?.find((item) => item?.id === result)?.value;
      },
    },
    {
      title: '品质',
      dataIndex: 'quality',
      hideInTable: true,
      renderFormItem: () => {
        return <ProFormSelect name="quality" options={qualitys} />;
      },
    },
    {
      title: '安全性',
      dataIndex: 'safety',
      valueType: 'text',
      width: 80,
      search: false,
      render: (result) => {
        return dictionaryList?.find((item) => item?.id === result)?.value;
      },
    },
    {
      title: '安全性',
      dataIndex: 'safety',
      hideInTable: true,
      renderFormItem: () => {
        return <ProFormSelect name="safety" options={safetys} />;
      },
    },
    {
      title: '重量',
      dataIndex: 'weight',
      valueType: 'text',
      width: 80,
      render: (value) => {
        return `${value}克`;
      },
      search: false,
    },
    {
      title: '权重',
      dataIndex: 'factor',
      valueType: 'text',
      width: 80,
      search: false,
    },
    {
      title: '权重',
      dataIndex: 'factor',
      hideInTable: true,
      renderFormItem: () => {
        return <ProFormDigit name="factor" />;
      },
    },
    {
      title: '区域',
      dataIndex: 'areas',
      valueType: 'text',
      width: 120,
      render: (result) => {
        return !isEmpty(result) && result !== '-'
          ? result.map((item) => <span key={item?.id}>{item?.value}、</span>)
          : '-';
      },
      search: false,
    },
    {
      title: '区域',
      dataIndex: 'areaIds',
      hideInTable: true,
      renderFormItem: () => {
        return (
          <ProFormSelect
            name="areaIds"
            fieldProps={{
              mode: 'multiple',
            }}
            options={areas}
          />
        );
      },
    },
    {
      title: '幼儿兴趣',
      dataIndex: 'interest',
      valueType: 'text',
      width: 80,
      search: false,
    },
    {
      title: '幼儿兴趣',
      dataIndex: 'interest',
      hideInTable: true,
      renderFormItem: () => {
        return <ProFormDigit name="interest" />;
      },
    },
    {
      title: '商品69码',
      dataIndex: 'barcode',
      valueType: 'text',
      width: 160,
      search: false,
    },
    {
      title: '商品69码',
      dataIndex: 'barcode',
      hideInTable: true,
      renderFormItem: () => {
        return <ProFormText name="barcode" />;
      },
    },
    {
      title: '产品描述',
      dataIndex: 'info',
      valueType: 'text',
      width: 160,
      search: false,
      render: (result) => {
        return (
          <Tooltip title={result}>
            <div className="text-ellipsis-three">{result}</div>
          </Tooltip>
        );
      },
    },
    {
      title: '产品玩法',
      dataIndex: 'gameplay',
      valueType: 'text',
      width: 160,
      search: false,
      render: (result) => {
        return (
          <Tooltip title={result}>
            <div className="text-ellipsis-three">{result}</div>
          </Tooltip>
        );
      },
    },
    {
      title: '备注',
      dataIndex: 'note',
      valueType: 'text',
      width: 160,
      search: false,
      render: (result) => {
        return (
          <Tooltip title={result}>
            <div className="text-ellipsis-three">{result}</div>
          </Tooltip>
        );
      },
    },
    {
      title: '状态',
      dataIndex: 'state',
      // hideInForm: true,
      // search: false,
      width: 80,
      valueEnum: {
        0: { color: 'red', text: '已下架', status: 'Default' },
        1: { color: 'green', text: '已上架', status: 'Processing' },
      },
    },
    timeColumn,
    {
      title: '年级',
      dataIndex: 'gradeIds',
      hideInTable: true,
      renderFormItem: () => {
        return (
          <ProFormSelect
            name="gradeIds"
            fieldProps={{
              mode: 'multiple',
            }}
            options={grades}
          />
        );
      },
    },
    {
      title: '指标名称',
      dataIndex: 'targetIds',
      hideInTable: true,
      tooltip: '指标、年级为互斥搜索，同时搜索仅指标有效',
      renderFormItem: () => {
        return (
          <ProFormSelect
            name="targetIds"
            showSearch
            fieldProps={{
              mode: 'multiple',
            }}
            request={async (value) => {
              return new Promise((resolve) => {
                debounceSearchTarget(value, fetchTargetListV2, resolve);
              });
            }}
          />
        );
      },
    },
    {
      title: '矩阵名称',
      dataIndex: 'matrixIds',
      hideInTable: true,
      tooltip: '矩阵、年级可合并搜索，同时搜索均有效',
      renderFormItem: () => {
        return (
          <ProFormSelect
            name="matrixIds"
            showSearch
            fieldProps={{
              mode: 'multiple',
              optionLabelProp: 'label',
              optionItemRender: (item) => `${Grade[item.grade]}： ${item.label}`,
            }}
            request={async (value) => {
              return new Promise((resolve) => {
                debounceSearchMatrix(value, fetchMatrixListV2, resolve);
              });
            }}
          />
        );
      },
    },
  ];

  return (
    <PageContainer>
      <ProTable<API.RuleListItem, API.PageParams>
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 'auto',
          defaultCollapsed: false,
          collapsed: false,
          collapseRender: false,
        }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="add"
            onClick={() => {
              window.open('/product/add', '_blank');
            }}
          >
            <PlusOutlined /> 新建
          </Button>,
          <Button type="primary" key="export" onClick={() => setIsProductTextShow(true)}>
            <ExportOutlined /> 导入淘宝商品
          </Button>,
          <Button
            type="primary"
            key="export"
            disabled={isEmpty(selectedRows)}
            onClick={handleExport}
          >
            <ExportOutlined /> 导出
          </Button>,
        ]}
        request={fetchProductList}
        columns={columns}
        scroll={{ x: 4600 }}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
      />
      <RelateTargetModal
        form={form}
        modalOpen={relateModalOpen}
        setModalOpen={setRelateModalOpen}
        currentRow={currentRow}
        actionRef={actionRef}
      />
      <Modal
        onClose={() => {
          setProductTexts(['']);
        }}
        loading={isLoading}
        onOk={handleImport}
        onCancel={() => setIsProductTextShow(false)}
        width={'80%'}
        open={isProductTextShow}
        title="导入淘宝商品"
      >
        <Row>
          <Button type="primary" onClick={() => setProductTexts(['', ...productTexts])}>
            新增
          </Button>
        </Row>
        {productTexts.map((productText, index) => {
          return (
            <Row
              style={{
                marginTop: 12,
              }}
              justify={'space-between'}
              wrap={false}
              gutter={16}
              align={'top'}
              key={index}
            >
              <Col span={20}>
                <TextArea
                  rows={3}
                  value={productText}
                  onChange={(e) => onProductTextChange(e.target.value, index)}
                />
              </Col>
              <Col span={4}>
                {' '}
                {productTexts.length === 1 ? null : (
                  <DeleteOutlined
                    onClick={() => setProductTexts((texts) => texts.filter((_, i) => i !== index))}
                  />
                )}
              </Col>
            </Row>
          );
        })}
      </Modal>
    </PageContainer>
  );
};

export default ProductList;
