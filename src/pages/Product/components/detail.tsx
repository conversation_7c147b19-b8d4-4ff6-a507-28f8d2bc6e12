/* eslint-disable guard-for-in */
import { fetchProductDetail, fetchTargetByProduct } from '@/services/api';
import { ProductCategory } from '@/services/constants';
import { isImageFile } from '@/services/utils';
import { FileTextOutlined } from '@ant-design/icons';
import { PageContainer, ProTable } from '@ant-design/pro-components';
import { history, useModel } from '@umijs/max';
import { Button, Card, Descriptions, Divider, Image, Tag } from 'antd';
import React, { useEffect, useState } from 'react';
import { useParams } from 'umi';

const ProductDetail: React.FC = () => {
  const { id } = useParams();

  const [productDetail, setProductDetail] = useState<any>({});
  const [targets, setTargets] = useState<any>([]);

  const { initialState } = useModel('@@initialState');
  const { dictionaryList } = initialState || {};

  useEffect(() => {
    fetchProductDetail(Number(id)).then((res) => {
      if (res?.status === 0) {
        setProductDetail(res?.data);
      }
    });
  }, []);

  useEffect(() => {
    fetchTargetByProduct(Number(id)).then((res) => {
      if (res?.status === 0) {
        setTargets(res?.data);
      }
    });
  }, []);

  const goodsColumns = [
    {
      title: '指标名称',
      dataIndex: 'title',
      key: 'title',
    },
    {
      title: '年级',
      dataIndex: 'gradeTitle',
      key: 'gradeTitle',
    },
    {
      title: '领域',
      dataIndex: 'matrix1',
      key: 'matrix1',
      render: (result) => {
        return result?.title;
      },
    },
    {
      title: '维度',
      dataIndex: 'matrix2',
      key: 'matrix2',
      render: (result) => {
        return result?.title;
      },
    },
    {
      title: '子维度',
      dataIndex: 'matrix3',
      key: 'matrix3',
      render: (result) => {
        return result?.title;
      },
    },
  ];

  return (
    <PageContainer
      content={
        <Button
          type="primary"
          onClick={() => {
            history.push(`/product/add?id=${id}`);
          }}
        >
          编辑
        </Button>
      }
    >
      <Card bordered={false}>
        <Descriptions
          title="商品信息"
          style={{
            marginBottom: 32,
          }}
        >
          <Descriptions.Item label="商品名称">{productDetail?.title}</Descriptions.Item>
          <Descriptions.Item label="淘宝名称">{productDetail?.taobaoTitle}</Descriptions.Item>
          <Descriptions.Item label="英文名称">{productDetail?.englishTitle}</Descriptions.Item>
          <Descriptions.Item label="淘宝链接">
            <a href={productDetail?.taobaoLink} target="_blank" rel="noreferrer">
              {productDetail?.taobaoLink}
            </a>
          </Descriptions.Item>
          <Descriptions.Item label="分类">
            {ProductCategory[productDetail?.category]}
          </Descriptions.Item>
          <Descriptions.Item label="区域">
            {productDetail?.areas?.map((item: any) => (
              <span key={item?.id}>{item?.value}、</span>
            ))}
          </Descriptions.Item>
          <Descriptions.Item label="品牌">{productDetail?.brand?.value}</Descriptions.Item>
          <Descriptions.Item label="标签">
            {productDetail?.tags?.map((tag: any) => (
              <Tag key={tag} color="blue">
                {tag?.value}
              </Tag>
            ))}
          </Descriptions.Item>
          <Descriptions.Item label="材质">
            {dictionaryList?.find((item) => item?.id === Number(productDetail?.material))?.value}
          </Descriptions.Item>
          <Descriptions.Item label="单位">{productDetail?.unit}</Descriptions.Item>
          <Descriptions.Item label="规格">{productDetail?.specification}</Descriptions.Item>
          <Descriptions.Item label="售价">{productDetail?.salePrice}元</Descriptions.Item>
          <Descriptions.Item label="淘宝售价">{productDetail?.taobaoPrice}元</Descriptions.Item>
          <Descriptions.Item label="划线价">{productDetail?.listPrice}元</Descriptions.Item>
          <Descriptions.Item label="年龄范围">
            {productDetail?.minAge} ~ {productDetail?.maxAge}
          </Descriptions.Item>
          <Descriptions.Item label="品质">
            {dictionaryList?.find((item) => item?.id === productDetail?.quality)?.value}
          </Descriptions.Item>
          <Descriptions.Item label="安全性">
            {dictionaryList?.find((item) => item?.id === productDetail?.safety)?.value}
          </Descriptions.Item>
          <Descriptions.Item label="重量">{productDetail?.weight}克</Descriptions.Item>
          <Descriptions.Item label="权重">{productDetail?.factor}</Descriptions.Item>
          <Descriptions.Item label="幼儿兴趣">{productDetail?.interest}</Descriptions.Item>
          <Descriptions.Item label="商品69码">{productDetail?.barcode}</Descriptions.Item>
          <Descriptions.Item label="产品描述">{productDetail?.info}</Descriptions.Item>
          <Descriptions.Item label="产品玩法">{productDetail?.gameplay}</Descriptions.Item>
          <Descriptions.Item label="备注">{productDetail?.note}</Descriptions.Item>
        </Descriptions>
        <Divider
          style={{
            marginBottom: 32,
          }}
        />
        <div style={{ fontWeight: 500, fontSize: '16px', marginBottom: '16px' }}>关联指标</div>
        <ProTable
          style={{
            marginBottom: 32,
          }}
          pagination={false}
          search={false}
          options={false}
          toolBarRender={false}
          dataSource={targets}
          columns={goodsColumns}
          rowKey="id"
        />
        <Descriptions
          title="预览图"
          style={{
            marginBottom: 32,
          }}
        >
          <Descriptions.Item label="">
            <Image width={100} src={productDetail?.header?.uri} preview />
          </Descriptions.Item>
        </Descriptions>
        <Descriptions
          title="banner"
          style={{
            marginBottom: 32,
          }}
        >
          <Descriptions.Item label="">
            {productDetail?.banners?.map((item: any) => (
              <div key={item?.id} style={{ marginRight: '20px', marginBottom: '20px' }}>
                <Image width={100} src={item?.uri} preview />
              </div>
            ))}
          </Descriptions.Item>
        </Descriptions>
        <Descriptions
          title="详情图"
          style={{
            marginBottom: 32,
          }}
        >
          <Descriptions.Item label="">
            {productDetail?.sections?.map((item: any) => (
              <div key={item?.id} style={{ marginRight: '20px', marginBottom: '20px' }}>
                <Image width={100} src={item?.uri} preview />
              </div>
            ))}
          </Descriptions.Item>
        </Descriptions>
        <Descriptions
          title="使用说明"
          style={{
            marginBottom: 32,
          }}
        >
          <Descriptions.Item label="">
            {productDetail?.instructions?.map((item: any) =>
              isImageFile(item?.filename) ? (
                <div key={item?.id} style={{ marginRight: '20px', marginBottom: '20px' }}>
                  <Image width={100} src={item?.uri} preview />
                </div>
              ) : (
                <div
                  key={item?.id}
                  style={{
                    width: '200px',
                    overflow: 'hidden',
                    whiteSpace: 'nowrap',
                    textOverflow: 'ellipsis',
                    marginRight: '20px',
                    marginBottom: '20px',
                  }}
                >
                  <FileTextOutlined />
                  <a href={item?.uri}>{item?.filename}</a>
                </div>
              ),
            )}
          </Descriptions.Item>
        </Descriptions>
        <Descriptions
          title="用户上传图"
          style={{
            marginBottom: 32,
          }}
        >
          <Descriptions.Item label="">
            {productDetail?.ugcs?.map((item: any) => (
              <div key={item?.id} style={{ marginRight: '20px', marginBottom: '20px' }}>
                <Image width={100} src={item?.uri} preview />
              </div>
            ))}
          </Descriptions.Item>
        </Descriptions>
      </Card>
    </PageContainer>
  );
};

export default ProductDetail;
