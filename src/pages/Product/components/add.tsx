/* eslint-disable guard-for-in */
import UploadImageComponent from '@/components/UploadImage';
import {
  addProduct,
  fetchDictionaryList,
  fetchEnum,
  fetchProductDetail,
  updateProduct,
} from '@/services/api';
import { DictionaryCategory } from '@/services/constants';
import { fetchClient } from '@/services/fileUpload';
import { checkPositiveInteger, checkPrice, checkWeight, formatOption } from '@/services/utils';
import {
  PageContainer,
  ProForm,
  ProFormDigit,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { history, useLocation, useModel } from '@umijs/max';
import { Button, Card, Col, Form, Row, message } from 'antd';
import { debounce, isEmpty } from 'lodash';
import React, { useEffect, useState } from 'react';

const AddProductModal: React.FC = () => {
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const id = searchParams.get('id');

  const [form] = Form.useForm();

  const [client, setClient] = useState<any>();

  const [headerList, setHeaderList] = useState<API.FileItem[]>([]);
  const [bannerList, setBannerList] = useState<API.FileItem[]>([]);
  const [sectionList, setSectionList] = useState<API.FileItem[]>([]);
  const [instructionList, setInstructionList] = useState<API.FileItem[]>([]);
  const [ugcList, setUgcList] = useState<API.FileItem[]>([]);

  const { initialState } = useModel('@@initialState');
  const { dictionaryList } = initialState || {};

  // 分类
  const [categorys, setCategorys] = useState<any>([]);

  // 区域列表
  const [areas, setAreas] = useState<any>([]);

  // 品质
  const [qualitys, setQualitys] = useState<any>([]);

  // 安全性
  const [safetys, setSafetys] = useState<any>([]);

  useEffect(() => {
    if (!isEmpty(dictionaryList)) {
      const result = dictionaryList?.map((item) => {
        return {
          value: item?.id,
          label: item?.value,
          category: item?.category,
        };
      });

      const _areas = result.filter((item) => item.category === DictionaryCategory.Area);
      const _qualitys = result.filter((item) => item.category === DictionaryCategory.Quality);
      const _safetys = result.filter((item) => item.category === DictionaryCategory.Safety);

      setAreas(_areas);
      setQualitys(_qualitys);
      setSafetys(_safetys);
    }
  }, []);

  useEffect(() => {
    fetchEnum().then((res) => {
      if (res?.status === 0) {
        const { ProductCategoryEnum } = res?.data;
        const result = Object.keys(ProductCategoryEnum).map((key) => {
          return { value: parseInt(key), label: ProductCategoryEnum[key] };
        });
        setCategorys(result);
      }
    });
  }, []);

  useEffect(() => {
    if (id) {
      fetchProductDetail(id).then((res) => {
        if (res?.status === 0) {
          const { data } = res;

          if (data?.header?.id) {
            const file = {
              id: data.header.id,
              uri: data.header.uri,
              filename: data.header.filename,
            };
            setHeaderList([file]);
          } else {
            setHeaderList([]);
          }

          if (data?.banners?.length) {
            const files = data.banners.map((item) => {
              return {
                id: item.id,
                uri: item.uri,
                filename: item.filename,
              };
            });
            setBannerList(files);
          } else {
            setBannerList([]);
          }

          if (data?.sections?.length) {
            const files = data.sections.map((item) => {
              return {
                id: item.id,
                uri: item.uri,
                filename: item.filename,
              };
            });
            setSectionList(files);
          } else {
            setSectionList([]);
          }

          if (data?.ugcs?.length) {
            const files = data.ugcs.map((item) => {
              return {
                id: item.id,
                uri: item.uri,
                filename: item.filename,
              };
            });
            setUgcList(files);
          } else {
            setUgcList([]);
          }

          form.setFieldsValue({
            title: data?.title,
            taobaoTitle: data?.taobaoTitle,
            englishTitle: data?.englishTitle,
            taobaoLink: data?.taobaoLink,
            sort: data?.sort,
            category: data?.category,
            areaIds: data?.areas?.map((item) => item?.id),
            brandId: { label: data?.brand?.value, value: data?.brand?.id },
            tagIds: data?.tags?.map((item) => {
              return {
                label: item?.value,
                value: item?.id,
              };
            }), //
            material: {
              label: dictionaryList?.find((item) => item?.id === Number(data?.material))?.value,
              value: Number(data?.material),
            },
            unit: data?.unit,
            specification: data?.specification,
            salePrice: data?.salePrice,
            listPrice: data?.listPrice,
            taobaoPrice: data?.taobaoPrice,
            quality: data?.quality,
            safety: data?.safety,
            weight: data?.weight,
            factor: data?.factor,
            minAge: data?.minAge,
            maxAge: data?.maxAge,
            interest: data?.interest,
            barcode: data?.barcode,
            info: data?.info,
            gameplay: data?.gameplay,
            note: data?.note,
          });
        }
      });
    } else {
      setHeaderList([]);
      setBannerList([]);
      setSectionList([]);
      setInstructionList([]);
      setUgcList([]);
    }
  }, [id]);

  useEffect(() => {
    fetchClient(setClient);
  }, []);

  const handleFinish = async (value, type) => {
    const isValid = await form.validateFields();

    if (isValid) {
      // 表单校验通过后的处理逻辑
      const result = {
        ...value,
        brandId: value?.brandId?.value,
        tagIds: value?.tagIds?.map((item) => item?.value) || [],
        material: value?.material?.value,
      };

      if (headerList?.length) {
        result['headerId'] = headerList?.[0]?.id;
      }

      if (bannerList?.length) {
        result['bannerIds'] = bannerList?.map((item) => item?.id);
      }

      if (sectionList?.length) {
        result['sectionIds'] = sectionList?.map((item) => item?.id);
      }

      if (instructionList?.length) {
        result['instructionIds'] = instructionList?.map((item) => item?.id);
      }

      if (ugcList?.length) {
        result['ugcIds'] = ugcList?.map((item) => item?.id);
      }

      try {
        let res: API.AdminResponse = {};

        if (id) {
          res = await updateProduct({ ...result, id });
        } else {
          res = await addProduct({ ...result });
        }

        if (res?.status === 0) {
          message.success(!id ? '新建成功' : '更新成功');

          if (type === 2) {
            // 执行提交并新建的逻辑
            setTimeout(() => {
              window.location.reload();
            }, 500);
          } else {
            // 其他类型的处理逻辑
            history.replace({
              pathname: `/product/detail/${id || res?.data?.id}`,
            });
          }
        }
      } catch (error) {
        console.log(error);
        message.error(
          !id ? error?.message || '新建失败，请重试' : error?.message || '更新失败，请重试',
        );
      }
    }
  };

  const onFinishFailed = () => {
    message.error('表单校验失败，请检查输入');
  };

  /**
   * 搜索防抖处理
   */
  const debounceSearchBrand = debounce(async (value, func, callback, params) => {
    const { data } = await func({ ...params, value: value?.keyWords });
    callback(formatOption(data));
  }, 300); // 设置防抖时间间隔为300毫秒

  const debounceSearchMaterial = debounce(async (value, func, callback, params) => {
    const { data } = await func({ ...params, value: value?.keyWords });
    callback(formatOption(data));
  }, 300); // 设置防抖时间间隔为300毫秒

  const debounceSearchTag = debounce(async (value, func, callback, params) => {
    const { data } = await func({ ...params, value: value?.keyWords });
    callback(formatOption(data));
  }, 300); // 设置防抖时间间隔为300毫秒

  return (
    // <DrawerForm
    //   title={!currentRow?.id ? '新建商品' : '编辑商品'}
    //   width="800px"
    //   form={form}
    //   open={modalOpen}
    //   onOpenChange={setModalOpen}
    //   onFinish={handleFinish}
    //   onFinishFailed={onFinishFailed}
    // >
    <PageContainer>
      <Card bordered={false}>
        <ProForm
          style={{
            margin: 'auto',
            marginTop: 8,
            maxWidth: 800,
          }}
          submitter={{
            render: (props, doms) => {
              return [
                ...doms,
                !id && (
                  <Button
                    type="primary"
                    htmlType="button"
                    onClick={() => {
                      const formValues = form.getFieldsValue(); // 获取表单字段的值
                      handleFinish(formValues, 2); // 将表单字段的值传递给 handleFinish 函数
                    }}
                    key="add"
                  >
                    提交并新建
                  </Button>
                ),
              ];
            },
          }}
          form={form}
          name="basic"
          layout="vertical"
          onFinish={(value) => handleFinish(value, 1)}
          onFinishFailed={onFinishFailed}
        >
          <Row gutter={8}>
            <Col span={12}>
              <ProFormText
                rules={[
                  {
                    required: true,
                    message: '请填写商品名称',
                  },
                ]}
                label="商品名称"
                name="title"
              />
            </Col>
            <Col span={12}>
              <ProFormText
                rules={[
                  {
                    required: true,
                    message: '请填写淘宝名称',
                  },
                ]}
                label="淘宝名称"
                name="taobaoTitle"
              />
            </Col>
            <Col span={12}>
              <ProFormText
                rules={[
                  {
                    required: true,
                    message: '请填写淘宝链接',
                  },
                ]}
                label="淘宝链接"
                name="taobaoLink"
              />
            </Col>
            <Col span={12}>
              <ProFormText label="英文名称" name="englishTitle" />
            </Col>
            <Col span={12}>
              <ProFormDigit
                rules={[{ validator: checkPositiveInteger }]}
                fieldProps={{
                  min: 1, // 最小值
                  max: 9999, // 最大值
                }}
                label="排序权重"
                name="sort"
              />
            </Col>
            <Col span={12}>
              <ProFormSelect
                label="分类"
                name="category"
                rules={[
                  {
                    required: true,
                    message: '请选择分类',
                  },
                ]}
                options={categorys}
              />
            </Col>
            <Col span={12}>
              <ProFormSelect
                label="区域"
                name="areaIds"
                fieldProps={{
                  mode: 'multiple',
                }}
                options={areas}
              />
            </Col>
            <Col span={12}>
              <ProFormSelect
                rules={[
                  {
                    required: true,
                    message: '请选择品牌',
                  },
                ]}
                name="brandId"
                label="品牌"
                showSearch
                fieldProps={{
                  labelInValue: true,
                }}
                request={async (value) => {
                  return new Promise((resolve) => {
                    debounceSearchBrand(value, fetchDictionaryList, resolve, {
                      current: 1,
                      pageSize: 50,
                      category: 5,
                      state: 1,
                    });
                  });
                }}
              />
            </Col>
            <Col span={12}>
              <ProFormSelect
                label="标签"
                name="tagIds"
                rules={[
                  {
                    required: true,
                    message: '请选择标签',
                  },
                ]}
                fieldProps={{
                  mode: 'multiple',
                  labelInValue: true,
                }}
                showSearch
                request={async (value) => {
                  return new Promise((resolve) => {
                    debounceSearchTag(value, fetchDictionaryList, resolve, {
                      current: 1,
                      pageSize: 50,
                      category: 7,
                      state: 1,
                    });
                  });
                }}
              />
            </Col>
            <Col span={12}>
              <ProFormSelect
                name="material"
                label="材质"
                rules={[
                  {
                    required: true,
                    message: '请选择材质',
                  },
                ]}
                showSearch
                fieldProps={{
                  labelInValue: true,
                }}
                request={async (value) => {
                  return new Promise((resolve) => {
                    debounceSearchMaterial(value, fetchDictionaryList, resolve, {
                      current: 1,
                      pageSize: 50,
                      category: 17,
                      state: 1,
                    });
                  });
                }}
              />
            </Col>
            <Col span={12}>
              <ProFormText label="单位" name="unit" />
            </Col>
            <Col span={12}>
              <ProFormText label="规格" name="specification" />
            </Col>
            <Col span={12}>
              <ProFormDigit
                rules={[{ validator: checkPrice }]}
                label="售价（元）"
                name="salePrice"
              />
            </Col>
            <Col span={12}>
              <ProFormDigit
                rules={[
                  {
                    required: true,
                    message: '请填写淘宝售价',
                  },
                  { validator: checkPrice },
                ]}
                label="淘宝售价（元）"
                name="taobaoPrice"
              />
            </Col>
            <Col span={12}>
              <ProFormDigit
                rules={[{ validator: checkPrice }]}
                label="划线价（元）"
                name="listPrice"
              />
            </Col>
            <Col span={12}>
              <ProFormSelect label="品质" name="quality" options={qualitys} />
            </Col>
            <Col span={12}>
              <ProFormSelect label="安全性" name="safety" options={safetys} />
            </Col>
            <Col span={12}>
              <ProFormDigit rules={[{ validator: checkWeight }]} label="重量（克）" name="weight" />
            </Col>
            <Col span={12}>
              <ProFormDigit
                rules={[{ validator: checkPositiveInteger }]}
                fieldProps={{
                  min: 1, // 最小值
                  max: 9999, // 最大值
                }}
                label="权重"
                name="factor"
              />
            </Col>
            <Col span={12}>
              <ProFormDigit
                rules={[{ validator: checkPositiveInteger }]}
                fieldProps={{
                  min: 1, // 最小值
                  max: 99, // 最大值
                }}
                label="最小年龄"
                name="minAge"
              />
            </Col>
            <Col span={12}>
              <ProFormDigit
                rules={[{ validator: checkPositiveInteger }]}
                fieldProps={{
                  min: 1, // 最小值
                  max: 99, // 最大值
                }}
                label="最大年龄"
                name="maxAge"
              />
            </Col>
            <Col span={12}>
              <ProFormDigit
                rules={[{ validator: checkPositiveInteger }]}
                fieldProps={{
                  min: 1, // 最小值
                  max: 10, // 最大值
                }}
                label="幼儿兴趣"
                name="interest"
              />
            </Col>
            <Col span={12}>
              <ProFormText label="商品69码" name="barcode" />
            </Col>
          </Row>

          <Row>
            <Col span={24}>
              <ProFormTextArea
                // rules={[
                //   {
                //     required: true,
                //     message: '请填写产品描述',
                //   },
                // ]}
                label="产品描述"
                name="info"
                placeholder="请输入产品描述，最多200个字"
                fieldProps={{ maxLength: 200 }}
              />
            </Col>
            <Col span={24}>
              <ProFormTextArea
                // rules={[
                //   {
                //     required: true,
                //     message: '请填写产品玩法',
                //   },
                // ]}
                label="产品玩法"
                name="gameplay"
                placeholder="请输入产品玩法，最多200个字"
                fieldProps={{ maxLength: 200 }}
              />
            </Col>
          </Row>

          <Row>
            <UploadImageComponent
              key="upload"
              fileName="headerId"
              label="预览图"
              max={1}
              client={client}
              fileList={headerList}
              setClient={setClient}
              setFileList={setHeaderList}
              accept=".png,.jpg,.jpeg,.gif"
            />
          </Row>
          <Row>
            <UploadImageComponent
              key="upload"
              fileName="bannerIds"
              label="轮播图"
              max={5}
              client={client}
              fileList={bannerList}
              setClient={setClient}
              setFileList={setBannerList}
              accept=".png,.jpg,.jpeg,.gif"
            />
          </Row>
          <Row>
            <UploadImageComponent
              key="upload"
              fileName="sectionIds"
              label="详情图"
              max={10}
              client={client}
              fileList={sectionList}
              setClient={setClient}
              setFileList={setSectionList}
              accept=".png,.jpg,.jpeg,.gif"
            />
          </Row>
          <Row>
            <UploadImageComponent
              key="upload"
              fileName="instructionIds"
              label="使用说明"
              max={10}
              client={client}
              fileList={instructionList}
              setClient={setClient}
              setFileList={setInstructionList}
            />
          </Row>
          <Row>
            <UploadImageComponent
              key="upload"
              fileName="ugcIds"
              label="用户上传图"
              max={10}
              client={client}
              fileList={ugcList}
              setClient={setClient}
              setFileList={setUgcList}
            />
          </Row>

          <Row>
            <Col span={24}>
              <ProFormTextArea
                label="备注"
                name="note"
                placeholder="请输入备注，最多200个字"
                fieldProps={{ maxLength: 200 }}
              />
            </Col>
          </Row>
        </ProForm>
      </Card>
    </PageContainer>
    // {/* </DrawerForm> */}
  );
};

export default AddProductModal;
