/* eslint-disable guard-for-in */
import { fetchTargetByProduct, fetchTargetList, postRelateTarget } from '@/services/api';
import { debounceSearch } from '@/services/utils';
import { ModalForm, ProFormSelect } from '@ant-design/pro-components';
import { message } from 'antd';
import React, { useEffect } from 'react';

const RelateTargetModal: React.FC = ({ form, modalOpen, setModalOpen, currentRow, actionRef }) => {
  useEffect(() => {
    if (!!currentRow?.id && modalOpen) {
      fetchTargetByProduct(currentRow?.id).then((res) => {
        if (res?.status === 0) {
          const { data } = res;

          form.setFieldsValue({
            targetIds: data?.map((item) => {
              return {
                ...item,
                label: item?.title,
                value: item?.id,
              };
            }),
          });
        }
      });
    }
  }, [modalOpen]);

  const handleFinish = async (value) => {
    const targetIds = value?.targetIds?.map((item) => item?.value) || [];

    try {
      const res = await postRelateTarget({
        targetIds,
        productId: currentRow?.id,
      });

      if (res?.status === 0) {
        message.success('关联指标成功');
        setModalOpen(false);
        if (actionRef.current) {
          actionRef.current.reload();
        }
      }
    } catch (error) {
      console.log(error);
      message.error(error?.message || '关联指标失败，请重试');
    }
  };

  const onFinishFailed = () => {
    message.error('表单校验失败，请检查输入');
  };

  return (
    <ModalForm
      title="关联指标"
      width="1000px"
      form={form}
      open={modalOpen}
      modalProps={{
        maskClosable: false,
      }}
      onOpenChange={setModalOpen}
      onFinish={handleFinish}
      onFinishFailed={onFinishFailed}
    >
      <ProFormSelect
        rules={[
          {
            required: true,
            message: '请选择关联指标',
          },
        ]}
        name="targetIds"
        label="关联指标"
        showSearch
        fieldProps={{
          mode: 'multiple',
          labelInValue: true,
          optionLabelProp: 'label',
          optionItemRender: (item) =>
            `${item.matrix1.title}/${item.matrix2.title}/${item.matrix3.title} - ${item.gradeTitle} - ${item.label}`,
        }}
        request={async (value) => {
          return new Promise((resolve) => {
            debounceSearch(value, fetchTargetList, resolve);
          });
        }}
      />
    </ModalForm>
  );
};

export default RelateTargetModal;
