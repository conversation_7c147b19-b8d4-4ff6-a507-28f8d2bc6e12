/* eslint-disable guard-for-in */
import { getPermission, getPermissionDetail } from '@/services/api';
import { PageContainer } from '@ant-design/pro-components';
import { Card, Descriptions } from 'antd';
import React, { useEffect, useState } from 'react';
import { useParams, useSearchParams } from 'umi';

const SchoolDetail: React.FC = () => {
  const { id } = useParams();
  const [searchParams] = useSearchParams();
  const title = searchParams.get('title');

  const [detailData, setDetailData] = useState<any>({});

  useEffect(() => {
    if (id) {
      Promise.all([getPermission(), getPermissionDetail({ id })]).then((result) => {
        const permissions: {
          [field: string]: {
            permissionName: string;
            description: string;
          }[];
        } = result?.[0]?.data;
        const permissionsDetail = result[1]?.data?.permissions;
        if (result[1]?.status === 0 && permissionsDetail?.length) {
          const data: Record<string, string[]> = {};
          Object.keys(permissions || {})?.forEach((key) => {
            const list = permissions[key];
            const _list = list?.filter((item) => {
              return permissionsDetail.includes(item.permissionName);
            });
            if (_list?.length) {
              data[key] = _list?.map((item) => item.description);
            }
          });
          setDetailData(data);
        }
      });
    }
  }, []);

  return (
    <PageContainer>
      <Card bordered={false}>
        <Descriptions
          title="角色权限信息"
          style={{
            marginBottom: 32,
          }}
        >
          <Descriptions.Item span={24} label="角色名称">
            {title || ''}
          </Descriptions.Item>
          {Object.keys(detailData || {})?.map((key) => {
            const list = detailData[key];
            return (
              <Descriptions.Item key={key} span={24} label={key}>
                {list?.join(',')}
              </Descriptions.Item>
            );
          })}
        </Descriptions>
      </Card>
    </PageContainer>
  );
};

export default SchoolDetail;
