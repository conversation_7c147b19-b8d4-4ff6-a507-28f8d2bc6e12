/* eslint-disable guard-for-in */
import { DrawerForm, FormInstance, ProFormText } from '@ant-design/pro-components';
import { Col, Row, message } from 'antd';
import React, { useEffect } from 'react';

interface IProps {
  modalOpen: boolean;
  setModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
  form: FormInstance;
  currentRow?: {
    value: string;
    id: number;
  };
  handleAdd: (params: { value: string; id?: number }) => Promise<void>;
}
const AddRole = ({ form, modalOpen, setModalOpen, currentRow, handleAdd }: IProps) => {
  const handleFinish = async (params: { value: string }) => {
    await handleAdd(params);
  };

  useEffect(() => {
    if (modalOpen) {
      form.setFieldsValue({
        value: currentRow?.value,
      });
    }
  }, [modalOpen]);

  const onFinishFailed = () => {
    message.error('表单校验失败，请检查输入');
  };

  return (
    <DrawerForm
      title={!currentRow?.id ? '新建角色' : '编辑角色'}
      width="800px"
      form={form}
      open={modalOpen}
      drawerProps={{
        maskClosable: false,
      }}
      initialValues={{
        ...currentRow,
      }}
      onOpenChange={setModalOpen}
      onFinish={handleFinish}
      onFinishFailed={onFinishFailed}
    >
      <Row gutter={8}>
        <Col span={12}>
          <ProFormText
            rules={[
              {
                required: true,
                message: '请填写角色名称',
              },
            ]}
            label="角色名称"
            name="value"
          />
        </Col>
      </Row>
    </DrawerForm>
  );
};

export default AddRole;
