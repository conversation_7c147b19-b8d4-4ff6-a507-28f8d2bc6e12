/* eslint-disable guard-for-in */
import { getPermission, getPermissionDetail } from '@/services/api';
import { DrawerForm, FormInstance, ProFormCheckbox, ProFormText } from '@ant-design/pro-components';
import { Checkbox, Col, Form, Row, message } from 'antd';
import React, { useEffect, useState } from 'react';

interface IProps {
  modalOpen: boolean;
  setModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
  form: FormInstance;
  currentRow?: {
    value: string;
    id: number;
  };
  handleAdd: (params: { permissions: string[] }) => Promise<void>;
}

const Add = ({ form, modalOpen, setModalOpen, currentRow, handleAdd }: IProps) => {
  const [checkboxData, setCheckboxData] = useState<{ [field: string]: Record<string, string>[] }>(
    {},
  );

  useEffect(() => {
    if (!!currentRow?.id && modalOpen) {
      Promise.all([getPermission(), getPermissionDetail({ id: currentRow.id })]).then((result) => {
        const permissions: {
          [field: string]: {
            permissionName: string;
          }[];
        } = result?.[0]?.data;
        if (result?.[0]?.status === 0 && Object.keys(result?.[0]?.data)) {
          setCheckboxData(result[0].data);
        }
        const permissionsDetail = result[1]?.data?.permissions;
        if (result[1]?.status === 0 && permissionsDetail?.length) {
          const formValue: {
            [field: string]: string[] | boolean;
          } = {};
          Object.values(permissions || {})?.forEach(
            (
              list: {
                permissionName: string;
              }[],
              index,
            ) => {
              const selectList = list?.filter((item) => {
                if (permissionsDetail.includes(item.permissionName)) {
                  formValue[`permissions-${index}`] = [
                    // @ts-ignore
                    ...(formValue[`permissions-${index}`] || []),
                    item.permissionName,
                  ];
                  return item;
                }
                return false;
              });
              if (selectList?.length === list?.length) {
                formValue[`all-permissions-${index}`] = true;
              }
            },
          );
          form.setFieldsValue({
            ...formValue,
          });
        }
      });
      form.setFieldsValue({
        value: currentRow?.value,
      });
    }
  }, [modalOpen]);

  const handleFinish = async (value: { [field: string]: string[] }) => {
    const result: { permissions: string[] } = {
      permissions: [],
    };
    Object.keys(value || {})?.forEach((key) => {
      const currentValue = value[key];
      if (key.split('all-permissions-').length > 1) {
        console.log('%c', 'color:#b03734', key);
      } else if (key.split('permissions-').length > 1) {
        result.permissions = [...result.permissions, ...currentValue];
      }
    });
    if (result.permissions.length) {
      await handleAdd(result);
    } else {
      message.error('表单校验失败，请至少选择一项权限');
    }
  };

  const onFinishFailed = () => {
    message.error('表单校验失败，请检查输入');
  };

  return (
    <DrawerForm
      title={'编辑权限'}
      width="800px"
      form={form}
      open={modalOpen}
      initialValues={{
        ...currentRow,
      }}
      drawerProps={{
        maskClosable: false,
      }}
      onOpenChange={setModalOpen}
      onFinish={handleFinish}
      onFinishFailed={onFinishFailed}
    >
      <Row gutter={8}>
        <Col span={12}>
          <ProFormText disabled label="角色名称" name="value" />
        </Col>
      </Row>

      {Object.keys(checkboxData || {})?.map((title, index) => {
        const item = checkboxData?.[title] || [];
        const options =
          item?.map((option) => ({
            label: option.description,
            value: option.permissionName || '',
          })) || [];
        const curValues = options?.map((item) => item.value);
        const name = `permissions-${index}`;
        const totleName = `all-${name}`;
        const checkAllDom = (
          <>
            <span style={{ marginRight: 24 }}>{title}</span>
            <Form.Item
              valuePropName="checked"
              name={totleName}
              style={{
                marginBottom: 0,
              }}
            >
              <Checkbox
                onChange={(e) => {
                  if (e.target.checked) {
                    form.setFieldsValue({
                      [name]: curValues,
                    });
                  } else {
                    form.setFieldsValue({
                      [name]: [],
                    });
                  }
                }}
              >
                全选
              </Checkbox>
            </Form.Item>
          </>
        );

        return (
          <ProFormCheckbox.Group
            label={checkAllDom}
            key={title}
            fieldProps={{
              onChange: (e) => {
                if (e.length === curValues.length) {
                  form.setFieldsValue({
                    [totleName]: true,
                  });
                } else {
                  form.setFieldsValue({
                    [totleName]: false,
                  });
                }
              },
            }}
            name={name}
            options={options}
          />
        );
      })}
    </DrawerForm>
  );
};

export default Add;
