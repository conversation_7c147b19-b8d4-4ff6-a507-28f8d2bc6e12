/* eslint-disable guard-for-in */
import {
  addDictionary,
  fetchDictionaryList,
  updateDictionary,
  updatePermission,
} from '@/services/api';
import { PlusOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { PageContainer, ProTable } from '@ant-design/pro-components';
import { Button, Form, message } from 'antd';
import React, { useRef, useState } from 'react';
import AddPrivilege from './components/add';
import AddRoleModal from './components/addRole';

const School: React.FC = () => {
  /**
   * @en-US Pop-up window of new window
   * @zh-CN 新建窗口的弹窗
   *  */
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const [modalPrivilegeOpen, setModalPrivilegeOpen] = useState<boolean>(false);

  const [currentRow, setCurrentRow] = useState<any>({});

  const actionRef = useRef<ActionType>();

  const [form] = Form.useForm();
  const privilege = Form.useForm();
  const privilegeForm = privilege[0];

  const handleModalPrivilegeOpen = ({ type, record }) => {
    privilegeForm.resetFields();
    setCurrentRow({});
    if (type === 'edit') {
      setCurrentRow(record);
    }
    setModalPrivilegeOpen(true);
  };

  const handleModalOpen = ({ type, record }) => {
    form.resetFields();
    setCurrentRow({});

    if (type === 'edit') {
      setCurrentRow(record);
    }

    setModalOpen(true);
  };

  // 更新角色权限
  const handleAddPrivilege = async (value: { permissions: string[] }) => {
    try {
      let res: API.AdminResponse = await updatePermission({
        ...value,
        roleId: currentRow?.id,
      });
      if (res?.status === 0) {
        message.success('更新成功');
        setModalPrivilegeOpen(false);
        if (actionRef.current) {
          actionRef.current.reload();
        }
      }
    } catch (error) {
      message.error(error?.message || '更新失败，请重试');
    }
  };

  // 新增角色配置项
  const handleAdd = async (value: { value: string }) => {
    try {
      let res: API.AdminResponse = {};

      if (!!currentRow?.id) {
        res = await updateDictionary({
          ...value,
          id: currentRow?.id,
          category: 21,
          key: '',
          sort: 0,
        });
      } else {
        res = await addDictionary({
          ...value,
          category: 21,
          key: '',
          sort: 0,
        });
      }

      if (res?.status === 0) {
        message.success(!currentRow?.id ? '新建成功' : '更新成功');
        setModalOpen(false);
        if (actionRef.current) {
          actionRef.current.reload();
        }
      }
    } catch (error) {
      console.log(error);
      message.error(
        !currentRow?.id
          ? error?.message || '新建失败，请重试'
          : error?.message || '更新失败，请重试',
      );
    }
  };

  const columns: ProColumns<API.TPrivilege>[] = [
    {
      title: '编号',
      dataIndex: 'id',
      valueType: 'text',
      width: 80,
      search: false,
    },
    {
      title: '名称',
      dataIndex: 'value',
      valueType: 'text',
      hideInForm: true,
      width: 120,
    },
    {
      title: '操作',
      ellipsis: true,
      dataIndex: 'action',
      key: 'action',
      valueType: 'option',
      width: '14em',
      search: false,
      render: (_, record) => [
        <Button
          size="small"
          type="primary"
          key="edit"
          onClick={() => {
            handleModalOpen({ type: 'edit', record });
          }}
        >
          编辑角色
        </Button>,
        <Button
          size="small"
          type="dashed"
          key="edit"
          onClick={() => {
            handleModalPrivilegeOpen({ type: 'edit', record });
          }}
        >
          编辑权限
        </Button>,
        <Button
          size="small"
          type="link"
          key="detail"
          href={`/privilege/role/detail/${record?.id}?title=${record.value}`}
          target="_blank"
        >
          查看角色详情
        </Button>,
      ],
    },
  ];

  return (
    <PageContainer>
      <ProTable<API.RuleListItem, API.PageParams>
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 'auto',
          defaultCollapsed: false,
          collapsed: false,
          collapseRender: false,
        }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              handleModalOpen({ type: 'add' });
            }}
          >
            <PlusOutlined /> 新建
          </Button>,
        ]}
        request={(params) => {
          // //
          // return {
          //   data: [{
          //     value: '1234',
          //     id: 1
          //   }]
          // }
          return fetchDictionaryList({ ...params, category: 21 });
        }}
        columns={columns}
        // scroll={{ x: 2000 }}
      />
      <AddRoleModal
        form={form}
        modalOpen={modalOpen}
        setModalOpen={setModalOpen}
        currentRow={currentRow}
        // actionRef={actionRef}
        handleAdd={handleAdd}
      />
      <AddPrivilege
        form={privilegeForm}
        modalOpen={modalPrivilegeOpen}
        setModalOpen={setModalPrivilegeOpen}
        currentRow={currentRow}
        // actionRef={actionRef}
        handleAdd={handleAddPrivilege}
      />
    </PageContainer>
  );
};

export default School;
