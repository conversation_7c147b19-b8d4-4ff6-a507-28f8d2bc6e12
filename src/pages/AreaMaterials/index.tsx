import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { PageContainer, ProFormSelect, ProFormText, ProTable } from '@ant-design/pro-components';
import { Button, Tooltip, Modal, message, Image } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { queryMaterialInfo, deleteMaterialById } from '@/services/areaMaterials';
import MaterialDetailTabs from './components/MaterialDetailTabs';
import AnalyzeMaterial from './components/analyzeMaterial';
import BatchAnalyzeModal from './components/BatchAnalyzeModal';
import TeacherMaterialModal from './teacher';
import { useModel } from '@umijs/max';
import { DictionaryCategory } from '@/services/constants';
import { isEmpty } from 'lodash';

const ProductList: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [selectedMaterial, setSelectedMaterial] = useState<any>(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [analyzeModalOpen, setAnalyzeModalOpen] = useState(false);
  const [batchAnalyzeModalOpen, setBatchAnalyzeModalOpen] = useState(false);
  const [teacherMaterialModalOpen, setTeacherMaterialModalOpen] = useState(false);

  // 区域列表
  const [areas, setAreas] = useState<any>([]);

  const { initialState } = useModel('@@initialState');
  const { dictionaryList } = initialState || {};

  // 获取区域数据
  useEffect(() => {
    if (!isEmpty(dictionaryList)) {
      const result: any = dictionaryList?.map((item) => {
        return {
          value: item?.id,
          label: item?.value,
          category: item?.category,
        };
      });

      const _areas = result.filter((item: any) => item.category === DictionaryCategory.Area);
      setAreas(_areas);
    }
  }, [dictionaryList]);

  // 删除材料
  const handleDelete = async (record: any) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除材料"${record.name}"吗？此操作不可恢复。`,
      okText: '确认删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          const response = await deleteMaterialById({
            id: record.id,
            sourceType: record.sourceType,
          });

          if ((response as any)?.status === 0) {
            message.success('删除成功');
            // 刷新表格数据
            actionRef.current?.reload();
          } else {
            message.error((response as any)?.message || '删除失败');
          }
        } catch (error) {
          console.error('删除失败:', error);
          message.error('删除失败，请重试');
        }
      },
    });
  };

  // 处理导入教师材料
  const handleImportTeacherMaterials = (selectedMaterials: any[]) => {
    console.log('导入的教师材料:', selectedMaterials);
    // 刷新表格数据
    actionRef.current?.reload();
  };

  const columns: ProColumns<any>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      valueType: 'text',
      width: 80,
      search: false,
    },
    {
      title: '材料首图',
      dataIndex: 'selectedImg',
      valueType: 'text',
      width: 100,
      search: false,
      render: (_, record) => {
        const imageUrl = record.selectedImg;
        if (!imageUrl) return '-';

        // 处理可能的多张图片，取第一张
        const firstImage =
          typeof imageUrl === 'string'
            ? imageUrl.includes(',')
              ? imageUrl.split(',')[0]
              : imageUrl
            : Array.isArray(imageUrl)
            ? imageUrl[0]
            : imageUrl;

        return (
          <Image
            src={firstImage}
            alt="材料首图"
            width={60}
            height={60}
            style={{ objectFit: 'cover', borderRadius: 4 }}
            fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
          />
        );
      },
    },
    {
      title: '材料名称',
      dataIndex: 'name',
      valueType: 'text',
      width: 300,
      render: (result) => {
        return (
          <Tooltip title={result}>
            <div className="text-ellipsis-three">{result}</div>
          </Tooltip>
        );
      },
      renderFormItem: () => {
        return <ProFormText name="name" placeholder="请输入材料名称" />;
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      valueType: 'text',
      width: 150,
      search: false,
    },
    {
      title: '玩法数量',
      dataIndex: 'playNum',
      valueType: 'text',
      width: 100,
      search: false,
    },
    {
      title: '材料类型',
      dataIndex: 'sourceType',
      valueType: 'text',
      width: 120,
      render: (result) => {
        const typeMap: Record<string, string> = {
          toy: '玩具',
          book: '书籍',
        };
        return typeMap[result as string] || result;
      },
      search: false,
    },
    {
      title: '区域',
      dataIndex: 'playArea',
      valueType: 'text',
      width: 120,
      render: (result) => {
        if (!result) return '-';

        // 处理多个区域的拼接显示
        if (typeof result === 'string') {
          // 如果是逗号分隔的字符串，直接显示
          return result;
        } else if (Array.isArray(result)) {
          // 如果是数组，用逗号拼接
          return result.join(',');
        }

        return result || '-';
      },
      search: false,
    },
    {
      title: '区域',
      dataIndex: 'area',
      hideInTable: true,
      renderFormItem: () => {
        return <ProFormSelect name="area" placeholder="请选择区域" options={areas} />;
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      valueType: 'option',
      width: 180,
      fixed: 'right',
      render: (_, record) => [
        <Button
          key="view"
          type="link"
          size="small"
          onClick={() => {
            setSelectedMaterial(record);
            setIsEditMode(false);
            setDrawerOpen(true);
          }}
        >
          查看
        </Button>,
        <Button
          key="edit"
          type="link"
          size="small"
          onClick={() => {
            setSelectedMaterial(record);
            setIsEditMode(true);
            setDrawerOpen(true);
          }}
        >
          编辑
        </Button>,
        <Button
          key="delete"
          type="link"
          size="small"
          danger
          onClick={() => {
            handleDelete(record);
          }}
        >
          删除
        </Button>,
      ],
    },
  ];

  return (
    <PageContainer>
      <ProTable<API.RuleListItem, API.PageParams>
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 'auto',
          defaultCollapsed: false,
          collapsed: false,
          collapseRender: false,
        }}
        toolBarRender={() => [
          <Button
            key="analyze"
            type="primary"
            style={{ marginRight: 8 }}
            onClick={() => {
              setAnalyzeModalOpen(true);
            }}
          >
            分析材料
          </Button>,
          <Button
            key="batchAnalyze"
            type="primary"
            style={{ marginRight: 8 }}
            onClick={() => {
              setBatchAnalyzeModalOpen(true);
            }}
          >
            批量分析
          </Button>,
          <Button
            key="import"
            type="primary"
            onClick={() => {
              setTeacherMaterialModalOpen(true);
            }}
          >
            导入教师材料
          </Button>,
        ]}
        request={async (params) => {
          const response = await queryMaterialInfo({
            ...params,
            isReviewed: 1,
          });
          return {
            data: Array.isArray(response?.data) ? response.data : [],
            success: (response as any)?.status === 0,
            total: Array.isArray(response?.data) ? response.data.length : 0,
          };
        }}
        columns={columns}
        scroll={{ x: 1000 }}
      />

      <MaterialDetailTabs
        open={drawerOpen}
        onClose={() => {
          setDrawerOpen(false);
          setSelectedMaterial(null);
          setIsEditMode(false);
        }}
        materialId={selectedMaterial?.id}
        sourceType={selectedMaterial?.sourceType}
        initialEditMode={isEditMode}
        materialData={selectedMaterial}
      />

      <AnalyzeMaterial
        open={analyzeModalOpen}
        onClose={() => setAnalyzeModalOpen(false)}
        onSuccess={() => {
          // 刷新表格数据
          actionRef.current?.reload();
        }}
      />

      <BatchAnalyzeModal
        open={batchAnalyzeModalOpen}
        onClose={() => setBatchAnalyzeModalOpen(false)}
        onSuccess={() => {
          // 刷新表格数据
          actionRef.current?.reload();
        }}
      />

      <TeacherMaterialModal
        open={teacherMaterialModalOpen}
        onClose={() => setTeacherMaterialModalOpen(false)}
        onImport={handleImportTeacherMaterials}
      />
    </PageContainer>
  );
};

export default ProductList;
