import React, { useState } from 'react';
import { Card, Row, Col, Button } from 'antd';

// 测试数据 - 基于您提供的实际数据
const testMergeData = {
  data: [
    // 第一个材料：只有 saveData
    {
      saveData: {
        adminMaterialDetailDTO: {
          brand: '无品牌/无注册商标',
          isAudited: 0,
          isDamaged: 0,
          linePrice: 2680,
          name: '德国蒙氏数学教具数字启蒙学习算数感神器加减法婴幼儿童益智玩具',
          price: 0,
          quantity: 1,
          taobaoId: '906625212627',
          taobaoLink:
            'https://pages-g.m.taobao.com/wow/z/app/detail-next/item/index?ut_sk=1.Y3xynMu0HZ4DAGgUma0L0kh%2B_21380790_1748934803493.Copy.1&x-ssr=true&id=906625212627&un=029db3c95ffabc483dd3af49e2902fce&share_crt_v=1&un_site=0&spm=a2159r.13376460.0.0&wxsign=tbwtRvoeUfWLYspnLrs6ypdTh7Yty4maqHEZOQL6KlrGh8x4aujb7Ng81kIn48hNMn7sE0XgQhDwHbKm4PRXLJXC3bxmfHvlERUZqFXgU7s2P7WQxQffU06rv4yV-5M5NjT&tbSocialPopKey=shareItem&sp_tk=SElra1ZvelI2WlU%3D&cpp=1&shareurl=true&short_name=h.6yC9DMVLHsVJLmG&bxsign=scdRdEhx1yI2GphUvPwyZlZEATIYUnjU7TLBHjfUM_HpaA5wR9h4EHL1PSjYK-U89JHLWNr3bOWGXRgEAaQX27SXbUAPDWoQpMX2TI8llWzpxVv_s_c9mJ3Bigo0-XptkkF',
          taobaoName: '德国蒙氏数学教具数字启蒙学习算数感神器加减法婴幼儿童益智玩具',
          taobaoPrice: 2680,
          weightScore: 50,
        },
      },
    },
    // 第二个材料：有 originalDTO 和 pendingList
    {
      originalDTO: {
        adminMaterialDetailDTO: {
          area: '建构区',
          baseMaterialId: 640,
          brand: '博思善学',
          combinedMaterialComponentId: 17,
          combinedMaterialId: 102,
          imageText: '小鸭、胡萝卜、破壳的小鸡、狗、蜗牛',
          isAudited: 0,
          isDamaged: 0,
          linePrice: 156,
          materialDesc:
            '一套幼儿涂色玩具材料，包含一本涂色画画本，里面有各种动物、植物等图案轮廓待填色，搭配一盒多色的蜡笔。蜡笔外观颜色多样，笔杆有方便幼儿抓握的设计。',
          materialType: '纸张,蜡质',
          name: '幼儿涂色画',
          price: 0,
          quantity: 0,
          selectedImg:
            'https://img.alicdn.com/imgextra/i2/2211697617862/O1CN01XbJpoJ27wqezwXmZ7_!!0-item_pic.jpg,https://img.alicdn.com/imgextra/i3/2211697617862/O1CN01c1hYqm27wqfD8brH6_!!2211697617862.jpg,https://img.alicdn.com/imgextra/i1/2211697617862/O1CN01jqHtK727wqev9LoVj_!!2211697617862.jpg,https://img.alicdn.com/imgextra/i3/2211697617862/O1CN01RvK5ZB27wqf4N3m3E_!!2211697617862.jpg,https://img.alicdn.com/imgextra/i1/2211697617862/O1CN01U28Q6327wqf7eMP3h_!!2211697617862.jpg',
          tags: '儿童绘画,涂色玩具,启蒙教育',
          taobaoId: '************',
          taobaoLink:
            'https://item.taobao.com/item.htm?ut_sk=1.YwnGdm7qWu4DAPk4ZQpuAL/8_21380790_1748944454638.Copy.1&id=************&sourceType=item&price=17.8&suid=DA2165D7-8234-4FA5-9AD6-CF029FAACD46&shareUniqueId=31867139909&un=329bb68e138d3c747a811649f8da3e26&share_crt_v=1&un_site=0&spm=a2159r.13376460.0.0&wxsign=tbwbfv5tjrkge_20tllgN5d7vHAl2dKqRUOZ91xUo2QMcdIQhZGfs_4rv5liiyGCStprrbQ8CCv_eggs0juSP6yRZ1XOVlwhxzjRVEE3nlATsUyMIx3w4JJLWpXecAJmvxQ&tbSocialPopKey=shareItem&sp_tk=NHplTlZMWVFHRE4%3D&cpp=1&shareurl=true&short_name=h.6BfqpwUhpPv0YwO&bxsign=scdXPZ_x9q7toAXjsXujXbVutK7UUfyyv-pWnTri4ajyPfwIfLkBQLaZekW5JQpaTOn3ncTIA6QBlKLJ5j3ZfepxH9NxlQf_lf55ZTVs52oL1b0HkyMuW0pkyj1X7EeaBrh',
          taobaoName: '幼儿涂色画儿童绘画启蒙填色涂鸦3-6岁幼儿园宝宝填充涂色画画本5',
          taobaoPrice: 156,
          trashImg: '',
          weightScore: 50,
        },
        materialAnalysis: {
          applicableAreas: '美工区',
          combinedId: 102,
          createTime: '2025-06-24 12:17:04',
          id: 57,
          isDeleted: 0,
          materialAttributes: '["美术/纸类/素描纸","美术/笔类/蜡笔"]',
          materialAttributesId: '119,139',
          materialId: 640,
          materialSummary: '涂色画画本1本，多色蜡笔1盒',
          maxAge: 6,
          minAge: 3,
          purpose:
            '投放这份玩具材料旨在通过涂色活动，首先锻炼幼儿的手部精细动作发展，让他们能够更灵活地控制手部肌肉完成涂色。其次，借助对不同图案的认识和涂色，增强幼儿对色彩的认知和搭配能力，激发其审美意识。同时，幼儿在独立或合作完成涂色任务中，也可促进专注能力和社交能力的提升。',
          suitablePeopleMax: 4,
          suitablePeopleMin: 1,
        },
      },
      pendingList: [
        {
          adminMaterialDetailDTO: {
            area: '建构区',
            brand: '博思善学',
            imageText: '小鸭、胡萝卜、破壳的小鸡、狗、蜗牛',
            isAudited: 0,
            isDamaged: 0,
            linePrice: 156,
            materialDesc:
              '一套幼儿涂色玩具材料，包含一本涂色画画本，里面有各种动物、植物等图案轮廓待填色，搭配一盒多色的蜡笔。蜡笔外观颜色多样，笔杆有方便幼儿抓握的设计。',
            materialType: '纸张,蜡质',
            name: '幼儿涂色画',
            price: 0,
            quantity: 1, // 这里不同：原始数据是 0，待处理数据是 1
            selectedImg:
              'https://img.alicdn.com/imgextra/i2/2211697617862/O1CN01XbJpoJ27wqezwXmZ7_!!0-item_pic.jpg,https://img.alicdn.com/imgextra/i3/2211697617862/O1CN01c1hYqm27wqfD8brH6_!!2211697617862.jpg,https://img.alicdn.com/imgextra/i1/2211697617862/O1CN01jqHtK727wqev9LoVj_!!2211697617862.jpg,https://img.alicdn.com/imgextra/i3/2211697617862/O1CN01RvK5ZB27wqf4N3m3E_!!2211697617862.jpg,https://img.alicdn.com/imgextra/i1/2211697617862/O1CN01U28Q6327wqf7eMP3h_!!2211697617862.jpg',
            tags: '儿童绘画,涂色玩具,启蒙教育',
            taobaoId: '************',
            taobaoLink:
              'https://item.taobao.com/item.htm?ut_sk=1.YwnGdm7qWu4DAPk4ZQpuAL/8_21380790_1748944454638.Copy.1&id=************&sourceType=item&price=17.8&suid=DA2165D7-8234-4FA5-9AD6-CF029FAACD46&shareUniqueId=31867139909&un=329bb68e138d3c747a811649f8da3e26&share_crt_v=1&un_site=0&spm=a2159r.13376460.0.0&wxsign=tbwbfv5tjrkge_20tllgN5d7vHAl2dKqRUOZ91xUo2QMcdIQhZGfs_4rv5liiyGCStprrbQ8CCv_eggs0juSP6yRZ1XOVlwhxzjRVEE3nlATsUyMIx3w4JJLWpXecAJmvxQ&tbSocialPopKey=shareItem&sp_tk=NHplTlZMWVFHRE4%3D&cpp=1&shareurl=true&short_name=h.6BfqpwUhpPv0YwO&bxsign=scdXPZ_x9q7toAXjsXujXbVutK7UUfyyv-pWnTri4ajyPfwIfLkBQLaZekW5JQpaTOn3ncTIA6QBlKLJ5j3ZfepxH9NxlQf_lf55ZTVs52oL1b0HkyMuW0pkyj1X7EeaBrh',
            taobaoName: '幼儿涂色画儿童绘画启蒙填色涂鸦3-6岁幼儿园宝宝填充涂色画画本5',
            taobaoPrice: 156,
            trashImg: '',
            weightScore: 50,
          },
          materialAnalysis: {
            applicableAreas: '美工区',
            createTime: '2025-06-11 08:38:06',
            isDeleted: 0,
            materialAttributes: '["美术/纸类/素描纸","美术/笔类/蜡笔"]',
            materialAttributesId: '119,139',
            materialSummary: '涂色画画本1本，多色蜡笔1盒',
            maxAge: 6,
            minAge: 3,
            purpose:
              '投放这份玩具材料旨在通过涂色活动，首先锻炼幼儿的手部精细动作发展，让他们能够更灵活地控制手部肌肉完成涂色。其次，借助对不同图案的认识和涂色，增强幼儿对色彩的认知和搭配能力，激发其审美意识。同时，幼儿在独立或合作完成涂色任务中，也可促进专注能力和社交能力的提升。',
            suitablePeopleMax: 4,
            suitablePeopleMin: 1,
            updateTime: '2025-06-12 02:00:04',
          },
        },
      ],
    },
  ],
  message: '',
  metadata: {
    endpoint: '/global/material/queryMergeData',
    method: 'GET',
    timestamp: '2025-06-24 12:35:47',
  },
  status: 0,
};

const TestMergeDataPage: React.FC = () => {
  const [baseData, setBaseData] = useState<any>(testMergeData.data[1].originalDTO); // 基础数据（原始数据）

  const mergeItem1 = testMergeData.data[0]; // 只有 saveData
  const mergeItem2 = testMergeData.data[1]; // 有 originalDTO 和 pendingList

  // 渲染材料详情卡片（只展示，不可选择）
  const renderMaterialDetailCard = (data: any, title: string, color: string) => {
    if (!data) return null;

    const adminDetail = data.adminMaterialDetailDTO;
    const materialAnalysis = data.materialAnalysis;

    return (
      <Card
        style={{
          height: '100%',
          border: `1px solid ${color}`,
        }}
        title={
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <span style={{ color, fontWeight: 'bold' }}>{title}</span>
          </div>
        }
        size="small"
      >
        {/* 材料基础信息 */}
        <div style={{ marginBottom: '16px' }}>
          <div style={{ fontWeight: 'bold', marginBottom: '8px', color: '#1890ff' }}>材料详情</div>
          <div style={{ fontSize: '12px', lineHeight: '1.6' }}>
            <div>
              <strong>名称:</strong> {adminDetail?.name || '未知'}
            </div>
            <div>
              <strong>区域:</strong> {adminDetail?.area || '未知'}
            </div>
            <div>
              <strong>品牌:</strong> {adminDetail?.brand || '未知'}
            </div>
            <div>
              <strong>材料类型:</strong> {adminDetail?.materialType || '未知'}
            </div>
            <div>
              <strong>数量:</strong> {adminDetail?.quantity || 0}
            </div>
            <div>
              <strong>线下价格:</strong> ¥{adminDetail?.linePrice || 0}
            </div>
            <div>
              <strong>淘宝价格:</strong> ¥{adminDetail?.taobaoPrice || 0}
            </div>
            <div>
              <strong>权重分数:</strong> {adminDetail?.weightScore || 0}
            </div>
            <div>
              <strong>标签:</strong> {adminDetail?.tags || '无'}
            </div>
            {adminDetail?.selectedImg && (
              <div>
                <strong>图片:</strong> {adminDetail.selectedImg.split(',').length}张
              </div>
            )}
            {adminDetail?.materialDesc && (
              <div style={{ marginTop: '8px' }}>
                <strong>描述:</strong>
                <div
                  style={{
                    marginTop: '4px',
                    padding: '8px',
                    backgroundColor: '#f5f5f5',
                    borderRadius: '4px',
                  }}
                >
                  {adminDetail.materialDesc.length > 100
                    ? `${adminDetail.materialDesc.substring(0, 100)}...`
                    : adminDetail.materialDesc}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* 玩法分析信息 */}
        {materialAnalysis && (
          <div>
            <div style={{ fontWeight: 'bold', marginBottom: '8px', color: '#52c41a' }}>
              玩法分析
            </div>
            <div style={{ fontSize: '12px', lineHeight: '1.6' }}>
              <div>
                <strong>适用区域:</strong> {materialAnalysis.applicableAreas || '未知'}
              </div>
              <div>
                <strong>适用年龄:</strong> {materialAnalysis.minAge || 0}-
                {materialAnalysis.maxAge || 0}岁
              </div>
              <div>
                <strong>适用人数:</strong> {materialAnalysis.suitablePeopleMin || 0}-
                {materialAnalysis.suitablePeopleMax || 0}人
              </div>
              <div>
                <strong>材料摘要:</strong> {materialAnalysis.materialSummary || '无'}
              </div>
              {materialAnalysis.materialAttributes && (
                <div>
                  <strong>材料属性:</strong> {materialAnalysis.materialAttributes}
                </div>
              )}
              {materialAnalysis.purpose && (
                <div style={{ marginTop: '8px' }}>
                  <strong>投放目的:</strong>
                  <div
                    style={{
                      marginTop: '4px',
                      padding: '8px',
                      backgroundColor: '#f6ffed',
                      borderRadius: '4px',
                    }}
                  >
                    {materialAnalysis.purpose.length > 150
                      ? `${materialAnalysis.purpose.substring(0, 150)}...`
                      : materialAnalysis.purpose}
                  </div>
                </div>
              )}
              {materialAnalysis.createTime && (
                <div style={{ marginTop: '8px', color: '#999' }}>
                  <strong>创建时间:</strong> {materialAnalysis.createTime}
                </div>
              )}
            </div>
          </div>
        )}
      </Card>
    );
  };

  // 渲染待处理数据的合并按钮卡片
  const renderPendingDataCard = (pendingData: any, pendingIndex: number) => {
    const adminDetail = pendingData.adminMaterialDetailDTO;
    const materialAnalysis = pendingData.materialAnalysis;

    const handleMerge = () => {
      // 模拟合并操作
      const newBaseData = { ...baseData };

      // 将待处理数据的字段合并到基础数据中
      Object.keys(pendingData.adminMaterialDetailDTO).forEach((key) => {
        if (
          pendingData.adminMaterialDetailDTO[key] !== null &&
          pendingData.adminMaterialDetailDTO[key] !== undefined &&
          pendingData.adminMaterialDetailDTO[key] !== ''
        ) {
          newBaseData.adminMaterialDetailDTO[key] = pendingData.adminMaterialDetailDTO[key];
        }
      });

      setBaseData(newBaseData);
    };

    return (
      <Card
        style={{ marginBottom: '16px', border: '1px solid #fa8c16' }}
        title={
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <span style={{ color: '#fa8c16', fontWeight: 'bold' }}>待处理数据 {pendingIndex}</span>
            <Button type="primary" size="small" onClick={handleMerge}>
              合并到基础数据
            </Button>
          </div>
        }
        size="small"
      >
        {/* 材料基础信息 */}
        <div style={{ marginBottom: '16px' }}>
          <div style={{ fontWeight: 'bold', marginBottom: '8px', color: '#1890ff' }}>材料详情</div>
          <div style={{ fontSize: '12px', lineHeight: '1.6' }}>
            <div>
              <strong>名称:</strong> {adminDetail?.name || '未知'}
            </div>
            <div>
              <strong>区域:</strong> {adminDetail?.area || '未知'}
            </div>
            <div>
              <strong>品牌:</strong> {adminDetail?.brand || '未知'}
            </div>
            <div>
              <strong>材料类型:</strong> {adminDetail?.materialType || '未知'}
            </div>
            <div>
              <strong>数量:</strong> {adminDetail?.quantity || 0}
            </div>
            <div>
              <strong>线下价格:</strong> ¥{adminDetail?.linePrice || 0}
            </div>
            <div>
              <strong>淘宝价格:</strong> ¥{adminDetail?.taobaoPrice || 0}
            </div>
            <div>
              <strong>权重分数:</strong> {adminDetail?.weightScore || 0}
            </div>
            <div>
              <strong>标签:</strong> {adminDetail?.tags || '无'}
            </div>
          </div>
        </div>

        {/* 玩法分析信息 */}
        {materialAnalysis && (
          <div>
            <div style={{ fontWeight: 'bold', marginBottom: '8px', color: '#52c41a' }}>
              玩法分析
            </div>
            <div style={{ fontSize: '12px', lineHeight: '1.6' }}>
              <div>
                <strong>适用区域:</strong> {materialAnalysis.applicableAreas || '未知'}
              </div>
              <div>
                <strong>适用年龄:</strong> {materialAnalysis.minAge || 0}-
                {materialAnalysis.maxAge || 0}岁
              </div>
              <div>
                <strong>适用人数:</strong> {materialAnalysis.suitablePeopleMin || 0}-
                {materialAnalysis.suitablePeopleMax || 0}人
              </div>
              <div>
                <strong>材料摘要:</strong> {materialAnalysis.materialSummary || '无'}
              </div>
            </div>
          </div>
        )}
      </Card>
    );
  };

  return (
    <div style={{ padding: '20px' }}>
      <h2>queryMergeData 新合并逻辑展示</h2>

      {/* 情况1：只有 saveData */}
      <Card style={{ marginBottom: '24px' }} size="small">
        <div style={{ marginBottom: '16px', fontSize: '14px', fontWeight: 'bold' }}>
          情况1 - 只有待保存数据: {mergeItem1.saveData?.adminMaterialDetailDTO?.name || '未知材料'}
        </div>
        <div style={{ marginBottom: '16px', color: '#666' }}>以下是待保存的数据，将直接导入：</div>

        <div style={{ display: 'flex', justifyContent: 'center' }}>
          <div style={{ width: '60%' }}>
            {renderMaterialDetailCard(mergeItem1.saveData, '待保存数据', '#1890ff')}
          </div>
        </div>
      </Card>

      {/* 情况2：有 originalDTO 和 pendingList */}
      <Card style={{ marginBottom: '24px' }} size="small">
        <div style={{ marginBottom: '16px', fontSize: '14px', fontWeight: 'bold' }}>
          情况2 - 有原始数据和待处理数据:{' '}
          {mergeItem2.originalDTO?.adminMaterialDetailDTO?.name || '未知材料'}
        </div>
        <div style={{ marginBottom: '16px', color: '#666' }}>
          以下是基础数据（原始数据），您可以选择将待处理数据合并到基础数据中：
        </div>

        <Row gutter={16}>
          {/* 左侧：基础数据（原始数据，可能已合并） */}
          <Col span={12}>
            {renderMaterialDetailCard(baseData, '基础数据（原始数据）', '#52c41a')}
          </Col>

          {/* 右侧：待处理数据列表 */}
          <Col span={12}>
            <div style={{ marginBottom: '8px', fontWeight: 'bold', color: '#fa8c16' }}>
              待处理数据 ({mergeItem2.pendingList?.length || 0} 个)
            </div>
            {mergeItem2.pendingList && mergeItem2.pendingList.length > 0 ? (
              mergeItem2.pendingList.map((pendingItem: any, pendingIndex: number) =>
                renderPendingDataCard(pendingItem, pendingIndex + 1),
              )
            ) : (
              <Card
                style={{
                  height: '100%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <div style={{ textAlign: 'center', color: '#999' }}>暂无待处理数据</div>
              </Card>
            )}
          </Col>
        </Row>

        <div
          style={{
            marginTop: '16px',
            padding: '12px',
            backgroundColor: '#f6ffed',
            borderRadius: '6px',
            border: '1px solid #b7eb8f',
          }}
        >
          <div style={{ fontWeight: 'bold', color: '#52c41a', marginBottom: '8px' }}>合并说明</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            • 左侧显示基础数据（原始数据），这是最终会保存的数据
            <br />
            •右侧显示待处理数据，点击合并到基础数据按钮可以将其字段合并到左侧
            <br />
            • 合并后左侧的基础数据会实时更新，显示合并后的结果
            <br />• 最终保存时会以左侧的基础数据为准
          </div>
        </div>
      </Card>
    </div>
  );
};

export default TestMergeDataPage;
