import React, { useState, useEffect, useRef } from 'react';
import { ProTable, ProFormText } from '@ant-design/pro-components';
import {
  Card,
  Tree,
  Row,
  Col,
  Button,
  message,
  Spin,
  Tooltip,
  Modal,
  Input,
  InputNumber,
} from 'antd';
import {
  querySchoolClassArea,
  queryTeacherMaterial,
  queryMergeData,
  saveTeacherMaterialDetail,
} from '@/services/areaMaterials';
import type { ActionType, ProColumns } from '@ant-design/pro-components';

interface AreaNode {
  key: string;
  title: string;
  children?: AreaNode[];
  [key: string]: any;
}

interface TeacherMaterial {
  id: number;
  combinedId: string;
  name: string;
  taobaoLink: string;
  taobaoName: string;
  [key: string]: any;
}

interface TeacherMaterialModalProps {
  open: boolean;
  onClose: () => void;
  onImport: (selectedMaterials: TeacherMaterial[]) => void;
}

// 转换数据格式为树形结构 (学校-班级-区域)
const convertToTreeData = (data: any[]): AreaNode[] => {
  return data.map((school) => ({
    key: `school-${school.schoolId}`,
    title: school.schoolName,
    schoolId: school.schoolId,
    children: school.classList?.map((classItem: any) => ({
      key: `class-${classItem.classId}`,
      title: classItem.className,
      classId: classItem.classId,
      schoolId: school.schoolId,
      children: classItem.areaList?.map((area: any, areaIndex: number) => ({
        key: `area-${classItem.classId}-${areaIndex}`,
        title: area.areaAlias ? `${area.area}(${area.areaAlias})` : area.area,
        area: area.area,
        areaAlias: area.areaAlias,
        classId: classItem.classId,
        schoolId: school.schoolId,
        isLeaf: true, // 区域是叶子节点
      })),
    })),
  }));
};

const TeacherMaterialModal: React.FC<TeacherMaterialModalProps> = ({ open, onClose, onImport }) => {
  // 状态管理
  const [treeLoading, setTreeLoading] = useState(false);
  const [areaTreeData, setAreaTreeData] = useState<AreaNode[]>([]);
  const [selectedAreaKeys, setSelectedAreaKeys] = useState<string[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [selectedMaterials, setSelectedMaterials] = useState<TeacherMaterial[]>([]);
  const [importing, setImporting] = useState(false);
  const [showMergeData, setShowMergeData] = useState(false);
  const [mergeDataList, setMergeDataList] = useState<any[]>([]);
  const [finalSaveDataList, setFinalSaveDataList] = useState<any[]>([]);

  // 编辑状态
  const [editingMaterial, setEditingMaterial] = useState<number | null>(null); // 正在编辑的材料索引
  const [editFormData, setEditFormData] = useState<any>({}); // 编辑表单数据
  const [mergedFields, setMergedFields] = useState<Record<number, string[]>>({}); // 已合并的字段

  const actionRef = useRef<ActionType>();

  // 当前选中的筛选条件
  const [currentFilters, setCurrentFilters] = useState({
    area: '',
    schoolId: '',
    classId: '',
  });

  // 获取区域树数据
  const fetchAreaTree = async () => {
    try {
      setTreeLoading(true);
      const response = await querySchoolClassArea({});

      if ((response as any)?.status === 0) {
        const data = (response as any)?.data || [];
        console.log('区域树数据:', data);

        // 转换数据格式为 Tree 组件需要的格式
        const treeData = convertToTreeData(data);
        setAreaTreeData(treeData);

        // 默认展开第一级
        if (treeData.length > 0) {
          const firstLevelKeys = treeData.map((item) => item.key);
          setExpandedKeys(firstLevelKeys);
        }
      } else {
        message.error((response as any)?.message || '获取区域数据失败');
      }
    } catch (error) {
      console.error('获取区域数据失败:', error);
      message.error('获取区域数据失败');
    } finally {
      setTreeLoading(false);
    }
  };

  // 初始化数据
  useEffect(() => {
    fetchAreaTree();
  }, []);

  // 监听弹窗打开状态，每次打开时重新获取数据
  useEffect(() => {
    if (open) {
      // 重置所有状态
      setSelectedAreaKeys([]);
      setSelectedRowKeys([]);
      setSelectedMaterials([]);
      setShowMergeData(false);
      setMergeDataList([]);
      setFinalSaveDataList([]);
      setEditingMaterial(null);
      setEditFormData({});
      setMergedFields({});
      setCurrentFilters({
        area: '',
        schoolId: '',
        classId: '',
      });

      // 重新获取区域树数据
      fetchAreaTree();

      // 如果有表格引用，重新加载表格数据
      if (actionRef.current) {
        actionRef.current.reload();
      }
    }
  }, [open]);

  // 区域树选择
  const handleAreaSelect = (selectedKeys: React.Key[], info: any) => {
    console.log('选中的区域:', selectedKeys, info);
    const stringKeys = selectedKeys.map((key) => String(key));
    setSelectedAreaKeys(stringKeys);

    // 解析选中的节点信息
    let areaParam = '';
    let schoolId = '';
    let classId = '';

    if (info.selectedNodes && info.selectedNodes.length > 0) {
      const selectedNode = info.selectedNodes[0];

      if (selectedNode.isLeaf) {
        // 选中的是区域节点
        areaParam = selectedNode.area;
        schoolId = selectedNode.schoolId;
        classId = selectedNode.classId;
      } else if (selectedNode.classId) {
        // 选中的是班级节点
        schoolId = selectedNode.schoolId;
        classId = selectedNode.classId;
      } else if (selectedNode.schoolId) {
        // 选中的是学校节点
        schoolId = selectedNode.schoolId;
      }
    }

    // 更新筛选条件并刷新表格
    setCurrentFilters({
      area: areaParam,
      schoolId: schoolId,
      classId: classId,
    });

    // 刷新表格数据并重置分页
    actionRef.current?.reload();
    actionRef.current?.reset?.();
  };

  // 处理行选择
  const handleRowSelectionChange = (
    selectedRowKeys: React.Key[],
    selectedRows: TeacherMaterial[],
  ) => {
    setSelectedRowKeys(selectedRowKeys);
    setSelectedMaterials(selectedRows);
  };

  // 关闭模态框时重置所有状态
  const handleModalClose = () => {
    // 重置合并数据相关状态
    setShowMergeData(false);
    setMergeDataList([]);
    setFinalSaveDataList([]);

    // 重置选择状态
    setSelectedRowKeys([]);
    setSelectedMaterials([]);

    // 重置导入状态
    setImporting(false);

    // 调用父组件的关闭回调
    onClose();
  };

  // 处理合并确认弹窗的关闭（返回到导入教师材料弹窗）
  const handleMergeModalClose = () => {
    // 只重置合并数据相关状态，保持材料选择状态
    setShowMergeData(false);
    setMergeDataList([]);
    setFinalSaveDataList([]);

    // 重置导入状态
    setImporting(false);

    // 不调用 onClose()，保持模态框打开状态
  };

  // 直接导入数据（不需要合并确认）
  const handleDirectImport = async (saveDataList: any[]) => {
    try {
      setImporting(true);

      // 调用 saveTeacherMaterialDetail 接口完成导入
      const saveResponse = await saveTeacherMaterialDetail({
        successCount: saveDataList.length,
        saveDataList: saveDataList,
      });

      if ((saveResponse as any)?.status === 0) {
        message.success(`成功导入 ${saveDataList.length} 个教师材料`);
        onImport(selectedMaterials);
        handleModalClose();
      } else {
        message.error((saveResponse as any)?.message || '导入失败');
      }
    } catch (error) {
      console.error('导入失败:', error);
      message.error('导入失败，请重试');
    } finally {
      setImporting(false);
    }
  };

  // 处理导入 - 第一步：获取合并数据
  const handleImport = async () => {
    if (selectedMaterials.length === 0) {
      message.warning('请选择要导入的材料');
      return;
    }

    try {
      setImporting(true);

      // 1. 获取选中材料的 combinedIds
      const combinedIds = selectedMaterials.map((material) => material.combinedId);
      console.log('准备导入的 combinedIds:', combinedIds);

      // 2. 调用 queryMergeData 接口获取合并数据
      // 根据后端接口要求，可能需要不同的参数格式
      // 方式1: 直接传递数组 (会转换为 combinedIds=1&combinedIds=2&combinedIds=3)
      // 方式2: 传递逗号分隔的字符串 (combinedIds=1,2,3)

      console.log('准备调用 queryMergeData，combinedIds:', combinedIds);

      // 先尝试直接传递数组，如果不工作可以改为逗号分隔格式
      // const mergeResponse = await queryMergeData({ combinedIds });

      // 如果上面不工作，可以尝试这种格式：
      const mergeResponse = await queryMergeData({ combinedIds: combinedIds.join(',') });

      if ((mergeResponse as any)?.status !== 0) {
        message.error((mergeResponse as any)?.message || '获取合并数据失败');
        return;
      }

      const mergeData = (mergeResponse as any)?.data || [];
      console.log('合并数据:', mergeData);
      console.log('合并数据长度:', mergeData.length);

      // 检查数据结构
      if (mergeData.length > 0) {
        console.log('第一个合并项:', mergeData[0]);
        console.log('saveData结构:', mergeData[0].saveData);
        console.log('材料名称:', mergeData[0].saveData?.adminMaterialDetailDTO?.name);
      }

      // 3. 筛选需要合并确认的数据和直接导入的数据
      const needMergeData = mergeData.filter(
        (item: any) => item.originalDTO && item.pendingList && item.pendingList.length > 0,
      );

      const directImportData = mergeData.filter(
        (item: any) =>
          item.saveData &&
          !item.originalDTO &&
          (!item.pendingList || item.pendingList.length === 0),
      );

      console.log('需要合并确认的数据:', needMergeData);
      console.log('直接导入的数据:', directImportData);

      // 4. 处理数据
      if (needMergeData.length > 0) {
        // 有需要合并确认的数据，显示合并界面
        setMergeDataList(needMergeData);

        // 初始化最终保存数据列表（包含需要合并的数据和直接导入的数据）
        const mergeInitialData = needMergeData.map((item: any) => item.originalDTO);
        const directImportSaveData = directImportData.map((item: any) => item.saveData);
        const allInitialData = [...mergeInitialData, ...directImportSaveData];

        setFinalSaveDataList(allInitialData);
        setShowMergeData(true);
      } else if (directImportData.length > 0) {
        // 只有直接导入的数据，直接导入
        const allSaveData = directImportData.map((item: any) => item.saveData);
        await handleDirectImport(allSaveData);
      }
    } catch (error) {
      console.error('获取合并数据失败:', error);
      message.error('获取合并数据失败，请重试');
    } finally {
      setImporting(false);
    }
  };

  // 处理保存 - 第二步：保存最终数据
  const handleSave = async () => {
    if (finalSaveDataList.length === 0) {
      message.warning('没有可保存的数据');
      return;
    }

    try {
      setImporting(true);

      // 调用 saveTeacherMaterialDetail 接口完成导入
      const saveResponse = await saveTeacherMaterialDetail({
        successCount: finalSaveDataList.length,
        saveDataList: finalSaveDataList,
      });

      if ((saveResponse as any)?.status === 0) {
        message.success(`成功导入 ${finalSaveDataList.length} 个教师材料`);
        onImport(selectedMaterials);
        handleModalClose(); // 使用统一的关闭函数
      } else {
        message.error((saveResponse as any)?.message || '导入失败');
      }
    } catch (error) {
      console.error('导入失败:', error);
      message.error('导入失败，请重试');
    } finally {
      setImporting(false);
    }
  };

  // 返回到材料选择界面（与 handleMergeModalClose 功能相同）
  const handleBackToSelection = () => {
    handleMergeModalClose();
  };

  // 处理字段合并 - 将待处理数据的字段合并到原始数据中
  const handleFieldMerge = (materialIndex: number, fieldName: string, pendingValue: any) => {
    const newSaveDataList = [...finalSaveDataList];
    const currentMaterial = newSaveDataList[materialIndex];

    if (currentMaterial) {
      // 玩法分析字段
      const analysisFields = [
        'applicableAreas',
        'minAge',
        'maxAge',
        'suitablePeopleMin',
        'suitablePeopleMax',
        'materialSummary',
        'purpose',
      ];

      if (analysisFields.includes(fieldName)) {
        // 更新玩法分析字段
        if (!currentMaterial.materialAnalysis) {
          currentMaterial.materialAnalysis = {};
        }
        currentMaterial.materialAnalysis[fieldName] = pendingValue;
      } else {
        // 更新材料详情字段
        if (currentMaterial.adminMaterialDetailDTO) {
          currentMaterial.adminMaterialDetailDTO[fieldName] = pendingValue;
        }
      }

      setFinalSaveDataList(newSaveDataList);

      // 记录已合并的字段
      setMergedFields((prev) => ({
        ...prev,
        [materialIndex]: [...(prev[materialIndex] || []), fieldName],
      }));
    }
  };

  // 批量合并待处理数据到原始数据
  const handleBatchMerge = (materialIndex: number, pendingData: any) => {
    const newSaveDataList = [...finalSaveDataList];
    const currentMaterial = newSaveDataList[materialIndex];

    if (currentMaterial && pendingData && pendingData.adminMaterialDetailDTO) {
      // 将待处理数据的所有字段合并到原始数据中
      Object.keys(pendingData.adminMaterialDetailDTO).forEach((key) => {
        if (
          pendingData.adminMaterialDetailDTO[key] !== null &&
          pendingData.adminMaterialDetailDTO[key] !== undefined &&
          pendingData.adminMaterialDetailDTO[key] !== ''
        ) {
          currentMaterial.adminMaterialDetailDTO[key] = pendingData.adminMaterialDetailDTO[key];
        }
      });

      // 如果有玩法分析数据，也进行合并
      if (pendingData.materialAnalysis && currentMaterial.materialAnalysis) {
        Object.keys(pendingData.materialAnalysis).forEach((key) => {
          if (
            pendingData.materialAnalysis[key] !== null &&
            pendingData.materialAnalysis[key] !== undefined &&
            pendingData.materialAnalysis[key] !== ''
          ) {
            currentMaterial.materialAnalysis[key] = pendingData.materialAnalysis[key];
          }
        });
      }

      setFinalSaveDataList(newSaveDataList);
    }
  };

  // 全部合并功能
  const handleMergeAll = (materialIndex: number) => {
    const mergeItem = mergeDataList[materialIndex];
    if (mergeItem && mergeItem.pendingList && mergeItem.pendingList.length > 0) {
      // 依次合并所有待处理数据
      mergeItem.pendingList.forEach((pendingData: any) => {
        handleBatchMerge(materialIndex, pendingData);
      });
    }
  };

  // 开始编辑材料
  const handleStartEdit = (materialIndex: number) => {
    const material = finalSaveDataList[materialIndex];
    setEditingMaterial(materialIndex);
    setEditFormData({
      ...material.adminMaterialDetailDTO,
      materialAnalysis: material.materialAnalysis || {},
    });
  };

  // 保存编辑
  const handleSaveEdit = () => {
    if (editingMaterial !== null) {
      const newSaveDataList = [...finalSaveDataList];
      const currentMaterial = newSaveDataList[editingMaterial];

      // 更新材料详情
      Object.keys(editFormData).forEach((key) => {
        if (key !== 'materialAnalysis') {
          currentMaterial.adminMaterialDetailDTO[key] = editFormData[key];
        }
      });

      // 更新玩法分析
      if (editFormData.materialAnalysis && currentMaterial.materialAnalysis) {
        Object.keys(editFormData.materialAnalysis).forEach((key) => {
          currentMaterial.materialAnalysis[key] = editFormData.materialAnalysis[key];
        });
      }

      setFinalSaveDataList(newSaveDataList);
      setEditingMaterial(null);
      setEditFormData({});
    }
  };

  // 取消编辑
  const handleCancelEdit = () => {
    setEditingMaterial(null);
    setEditFormData({});
  };

  // 更新编辑表单数据
  const handleEditFormChange = (field: string, value: any) => {
    setEditFormData((prev: any) => ({
      ...prev,
      [field]: value,
    }));
  };

  // 更新玩法分析数据
  // const handleMaterialAnalysisChange = (field: string, value: any) => {
  //   setEditFormData((prev: any) => ({
  //     ...prev,
  //     materialAnalysis: {
  //       ...prev.materialAnalysis,
  //       [field]: value,
  //     },
  //   }));
  // };

  // 渲染材料详情卡片（支持编辑）
  const renderMaterialDetailCard = (
    data: any,
    title: string,
    color: string,
    materialIndex?: number,
    showEditButton?: boolean,
    showMergeAllButton?: boolean,
  ) => {
    if (!data) return null;

    const adminDetail = data.adminMaterialDetailDTO;
    const materialAnalysis = data.materialAnalysis;
    const isEditing = editingMaterial === materialIndex;

    return (
      <Card
        style={{
          height: '100%',
          border: `1px solid ${color}`,
        }}
        title={
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <span style={{ color, fontWeight: 'bold' }}>{title}</span>
            <div>
              {showMergeAllButton && materialIndex !== undefined && (
                <Button
                  size="small"
                  style={{ marginRight: '8px' }}
                  onClick={() => handleMergeAll(materialIndex)}
                >
                  全部合并
                </Button>
              )}
              {showEditButton && materialIndex !== undefined && !isEditing && (
                <Button size="small" type="primary" onClick={() => handleStartEdit(materialIndex)}>
                  编辑
                </Button>
              )}
              {isEditing && (
                <div>
                  <Button
                    size="small"
                    type="primary"
                    style={{ marginRight: '8px' }}
                    onClick={handleSaveEdit}
                  >
                    保存
                  </Button>
                  <Button size="small" onClick={handleCancelEdit}>
                    取消
                  </Button>
                </div>
              )}
            </div>
          </div>
        }
        size="small"
      >
        {/* 材料基础信息 */}
        <div style={{ marginBottom: '16px' }}>
          <div style={{ fontWeight: 'bold', marginBottom: '8px', color: '#1890ff' }}>材料详情</div>
          <div style={{ fontSize: '12px', lineHeight: '1.6' }}>
            {isEditing ? (
              // 编辑模式
              <div>
                <div style={{ marginBottom: '8px' }}>
                  <strong>名称:</strong>
                  <Input
                    size="small"
                    value={editFormData.name || ''}
                    onChange={(e) => handleEditFormChange('name', e.target.value)}
                    style={{ marginLeft: '8px', width: '200px' }}
                  />
                </div>
                <div style={{ marginBottom: '8px' }}>
                  <strong>区域:</strong>
                  <Input
                    size="small"
                    value={editFormData.area || ''}
                    onChange={(e) => handleEditFormChange('area', e.target.value)}
                    style={{ marginLeft: '8px', width: '150px' }}
                  />
                </div>
                <div style={{ marginBottom: '8px' }}>
                  <strong>品牌:</strong>
                  <Input
                    size="small"
                    value={editFormData.brand || ''}
                    onChange={(e) => handleEditFormChange('brand', e.target.value)}
                    style={{ marginLeft: '8px', width: '150px' }}
                  />
                </div>
                <div style={{ marginBottom: '8px' }}>
                  <strong>材料类型:</strong>
                  <Input
                    size="small"
                    value={editFormData.materialType || ''}
                    onChange={(e) => handleEditFormChange('materialType', e.target.value)}
                    style={{ marginLeft: '8px', width: '150px' }}
                  />
                </div>
                <div style={{ marginBottom: '8px' }}>
                  <strong>数量:</strong>
                  <InputNumber
                    size="small"
                    value={editFormData.quantity || 0}
                    onChange={(value) => handleEditFormChange('quantity', value)}
                    style={{ marginLeft: '8px', width: '100px' }}
                    min={0}
                  />
                </div>
                <div style={{ marginBottom: '8px' }}>
                  <strong>线下价格:</strong>
                  <InputNumber
                    size="small"
                    value={editFormData.linePrice || 0}
                    onChange={(value) => handleEditFormChange('linePrice', value)}
                    style={{ marginLeft: '8px', width: '120px' }}
                    min={0}
                    precision={2}
                  />
                </div>
                <div style={{ marginBottom: '8px' }}>
                  <strong>淘宝价格:</strong>
                  <InputNumber
                    size="small"
                    value={editFormData.taobaoPrice || 0}
                    onChange={(value) => handleEditFormChange('taobaoPrice', value)}
                    style={{ marginLeft: '8px', width: '120px' }}
                    min={0}
                    precision={2}
                  />
                </div>
                <div style={{ marginBottom: '8px' }}>
                  <strong>权重分数:</strong>
                  <InputNumber
                    size="small"
                    value={editFormData.weightScore || 0}
                    onChange={(value) => handleEditFormChange('weightScore', value)}
                    style={{ marginLeft: '8px', width: '100px' }}
                    min={0}
                    max={100}
                  />
                </div>
                <div style={{ marginBottom: '8px' }}>
                  <strong>标签:</strong>
                  <Input
                    size="small"
                    value={editFormData.tags || ''}
                    onChange={(e) => handleEditFormChange('tags', e.target.value)}
                    style={{ marginLeft: '8px', width: '200px' }}
                    placeholder="用逗号分隔多个标签"
                  />
                </div>
              </div>
            ) : (
              // 显示模式，合并过来的字段可以编辑
              <div>
                {/* 渲染可编辑字段的函数 */}
                {(() => {
                  const renderEditableField = (label: string, value: any, fieldName: string) => {
                    const isMerged =
                      materialIndex !== undefined &&
                      mergedFields[materialIndex]?.includes(fieldName);

                    if (isMerged) {
                      // 已合并的字段，可以编辑
                      if (
                        fieldName === 'quantity' ||
                        fieldName === 'linePrice' ||
                        fieldName === 'taobaoPrice' ||
                        fieldName === 'weightScore'
                      ) {
                        return (
                          <div
                            style={{ marginBottom: '4px', display: 'flex', alignItems: 'center' }}
                          >
                            <strong>{label}:</strong>
                            <InputNumber
                              size="small"
                              value={adminDetail?.[fieldName] || 0}
                              onChange={(val) => {
                                const newSaveDataList = [...finalSaveDataList];
                                if (
                                  newSaveDataList[materialIndex] &&
                                  newSaveDataList[materialIndex].adminMaterialDetailDTO
                                ) {
                                  newSaveDataList[materialIndex].adminMaterialDetailDTO[fieldName] =
                                    val;
                                  setFinalSaveDataList(newSaveDataList);
                                }
                              }}
                              style={{ marginLeft: '8px', width: '100px' }}
                              min={0}
                              precision={fieldName.includes('Price') ? 2 : 0}
                            />
                            <span style={{ marginLeft: '8px', color: '#1890ff', fontSize: '10px' }}>
                              (已合并)
                            </span>
                          </div>
                        );
                      } else {
                        return (
                          <div
                            style={{ marginBottom: '4px', display: 'flex', alignItems: 'center' }}
                          >
                            <strong>{label}:</strong>
                            <Input
                              size="small"
                              value={adminDetail?.[fieldName] || ''}
                              onChange={(e) => {
                                const newSaveDataList = [...finalSaveDataList];
                                if (
                                  newSaveDataList[materialIndex] &&
                                  newSaveDataList[materialIndex].adminMaterialDetailDTO
                                ) {
                                  newSaveDataList[materialIndex].adminMaterialDetailDTO[fieldName] =
                                    e.target.value;
                                  setFinalSaveDataList(newSaveDataList);
                                }
                              }}
                              style={{ marginLeft: '8px', width: '200px' }}
                            />
                            <span style={{ marginLeft: '8px', color: '#1890ff', fontSize: '10px' }}>
                              (已合并)
                            </span>
                          </div>
                        );
                      }
                    } else {
                      // 未合并的字段，只显示
                      return (
                        <div style={{ marginBottom: '4px' }}>
                          <strong>{label}:</strong> {value || '未知'}
                        </div>
                      );
                    }
                  };

                  return (
                    <>
                      {renderEditableField('名称', adminDetail?.name, 'name')}
                      {renderEditableField('区域', adminDetail?.area, 'area')}
                      {renderEditableField('品牌', adminDetail?.brand, 'brand')}
                      {renderEditableField('材料类型', adminDetail?.materialType, 'materialType')}
                      {renderEditableField('数量', adminDetail?.quantity, 'quantity')}
                      {renderEditableField(
                        '线下价格',
                        `¥${adminDetail?.linePrice || 0}`,
                        'linePrice',
                      )}
                      {renderEditableField(
                        '淘宝价格',
                        `¥${adminDetail?.taobaoPrice || 0}`,
                        'taobaoPrice',
                      )}
                      {renderEditableField('权重分数', adminDetail?.weightScore, 'weightScore')}
                      {renderEditableField('标签', adminDetail?.tags, 'tags')}
                      {adminDetail?.selectedImg &&
                        renderEditableField(
                          '图片',
                          `${adminDetail.selectedImg.split(',').length}张`,
                          'selectedImg',
                        )}
                    </>
                  );
                })()}
              </div>
            )}
            {!isEditing && adminDetail?.materialDesc && (
              <div style={{ marginTop: '8px' }}>
                <strong>描述:</strong>
                <div
                  style={{
                    marginTop: '4px',
                    padding: '8px',
                    backgroundColor: '#f5f5f5',
                    borderRadius: '4px',
                  }}
                >
                  {adminDetail.materialDesc.length > 100
                    ? `${adminDetail.materialDesc.substring(0, 100)}...`
                    : adminDetail.materialDesc}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* 玩法分析信息 */}
        {materialAnalysis && (
          <div>
            <div style={{ fontWeight: 'bold', marginBottom: '8px', color: '#52c41a' }}>
              玩法分析
            </div>
            <div style={{ fontSize: '12px', lineHeight: '1.6' }}>
              {/* 渲染玩法分析可编辑字段的函数 */}
              {(() => {
                const renderAnalysisField = (label: string, value: any, fieldName: string) => {
                  const isMerged =
                    materialIndex !== undefined && mergedFields[materialIndex]?.includes(fieldName);

                  if (isMerged) {
                    // 已合并的字段，可以编辑
                    if (
                      fieldName === 'minAge' ||
                      fieldName === 'maxAge' ||
                      fieldName === 'suitablePeopleMin' ||
                      fieldName === 'suitablePeopleMax'
                    ) {
                      return (
                        <div style={{ marginBottom: '4px', display: 'flex', alignItems: 'center' }}>
                          <strong>{label}:</strong>
                          <InputNumber
                            size="small"
                            value={materialAnalysis?.[fieldName] || 0}
                            onChange={(val) => {
                              const newSaveDataList = [...finalSaveDataList];
                              if (
                                newSaveDataList[materialIndex] &&
                                newSaveDataList[materialIndex].materialAnalysis
                              ) {
                                newSaveDataList[materialIndex].materialAnalysis[fieldName] = val;
                                setFinalSaveDataList(newSaveDataList);
                              }
                            }}
                            style={{ marginLeft: '8px', width: '80px' }}
                            min={0}
                          />
                          <span style={{ marginLeft: '8px', color: '#1890ff', fontSize: '10px' }}>
                            (已合并)
                          </span>
                        </div>
                      );
                    } else {
                      return (
                        <div style={{ marginBottom: '4px', display: 'flex', alignItems: 'center' }}>
                          <strong>{label}:</strong>
                          <Input
                            size="small"
                            value={materialAnalysis?.[fieldName] || ''}
                            onChange={(e) => {
                              const newSaveDataList = [...finalSaveDataList];
                              if (
                                newSaveDataList[materialIndex] &&
                                newSaveDataList[materialIndex].materialAnalysis
                              ) {
                                newSaveDataList[materialIndex].materialAnalysis[fieldName] =
                                  e.target.value;
                                setFinalSaveDataList(newSaveDataList);
                              }
                            }}
                            style={{ marginLeft: '8px', width: '200px' }}
                          />
                          <span style={{ marginLeft: '8px', color: '#1890ff', fontSize: '10px' }}>
                            (已合并)
                          </span>
                        </div>
                      );
                    }
                  } else {
                    // 未合并的字段，只显示
                    return (
                      <div style={{ marginBottom: '4px' }}>
                        <strong>{label}:</strong> {value || '未知'}
                      </div>
                    );
                  }
                };

                return (
                  <>
                    {renderAnalysisField(
                      '适用区域',
                      materialAnalysis.applicableAreas,
                      'applicableAreas',
                    )}
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                      {renderAnalysisField('最小年龄', materialAnalysis.minAge, 'minAge')}
                      {renderAnalysisField('最大年龄', materialAnalysis.maxAge, 'maxAge')}
                    </div>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                      {renderAnalysisField(
                        '最少人数',
                        materialAnalysis.suitablePeopleMin,
                        'suitablePeopleMin',
                      )}
                      {renderAnalysisField(
                        '最多人数',
                        materialAnalysis.suitablePeopleMax,
                        'suitablePeopleMax',
                      )}
                    </div>
                    {renderAnalysisField(
                      '材料摘要',
                      materialAnalysis.materialSummary,
                      'materialSummary',
                    )}
                    {materialAnalysis.materialAttributes &&
                      renderAnalysisField(
                        '材料属性',
                        materialAnalysis.materialAttributes,
                        'materialAttributes',
                      )}
                    {materialAnalysis.purpose &&
                      renderAnalysisField('投放目的', materialAnalysis.purpose, 'purpose')}
                  </>
                );
              })()}
            </div>
          </div>
        )}
      </Card>
    );
  };

  // 渲染待处理数据的合并按钮卡片
  const renderPendingDataCard = (pendingData: any, pendingIndex: number, materialIndex: number) => {
    const adminDetail = pendingData.adminMaterialDetailDTO;
    const materialAnalysis = pendingData.materialAnalysis;

    return (
      <Card
        style={{ marginBottom: '16px', border: '1px solid #fa8c16' }}
        title={
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <span style={{ color: '#fa8c16', fontWeight: 'bold' }}>
              待处理数据 {pendingIndex + 1}
            </span>
            <Button
              type="primary"
              size="small"
              onClick={() => handleBatchMerge(materialIndex, pendingData)}
            >
              合并到原始数据
            </Button>
          </div>
        }
        size="small"
      >
        {/* 材料基础信息 */}
        <div style={{ marginBottom: '16px' }}>
          <div style={{ fontWeight: 'bold', marginBottom: '8px', color: '#1890ff' }}>材料详情</div>
          <div style={{ fontSize: '12px', lineHeight: '1.6' }}>
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                marginBottom: '4px',
              }}
            >
              <span>
                <strong>名称:</strong> {adminDetail?.name || '未知'}
              </span>
              <Button
                size="small"
                type="link"
                onClick={() => handleFieldMerge(materialIndex, 'name', adminDetail?.name)}
                style={{ padding: '0 4px', fontSize: '10px' }}
              >
                合并
              </Button>
            </div>
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                marginBottom: '4px',
              }}
            >
              <span>
                <strong>区域:</strong> {adminDetail?.area || '未知'}
              </span>
              <Button
                size="small"
                type="link"
                onClick={() => handleFieldMerge(materialIndex, 'area', adminDetail?.area)}
                style={{ padding: '0 4px', fontSize: '10px' }}
              >
                合并
              </Button>
            </div>
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                marginBottom: '4px',
              }}
            >
              <span>
                <strong>品牌:</strong> {adminDetail?.brand || '未知'}
              </span>
              <Button
                size="small"
                type="link"
                onClick={() => handleFieldMerge(materialIndex, 'brand', adminDetail?.brand)}
                style={{ padding: '0 4px', fontSize: '10px' }}
              >
                合并
              </Button>
            </div>
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                marginBottom: '4px',
              }}
            >
              <span>
                <strong>材料类型:</strong> {adminDetail?.materialType || '未知'}
              </span>
              <Button
                size="small"
                type="link"
                onClick={() =>
                  handleFieldMerge(materialIndex, 'materialType', adminDetail?.materialType)
                }
                style={{ padding: '0 4px', fontSize: '10px' }}
              >
                合并
              </Button>
            </div>
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                marginBottom: '4px',
              }}
            >
              <span>
                <strong>数量:</strong> {adminDetail?.quantity || 0}
              </span>
              <Button
                size="small"
                type="link"
                onClick={() => handleFieldMerge(materialIndex, 'quantity', adminDetail?.quantity)}
                style={{ padding: '0 4px', fontSize: '10px' }}
              >
                合并
              </Button>
            </div>
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                marginBottom: '4px',
              }}
            >
              <span>
                <strong>线下价格:</strong> ¥{adminDetail?.linePrice || 0}
              </span>
              <Button
                size="small"
                type="link"
                onClick={() => handleFieldMerge(materialIndex, 'linePrice', adminDetail?.linePrice)}
                style={{ padding: '0 4px', fontSize: '10px' }}
              >
                合并
              </Button>
            </div>
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                marginBottom: '4px',
              }}
            >
              <span>
                <strong>淘宝价格:</strong> ¥{adminDetail?.taobaoPrice || 0}
              </span>
              <Button
                size="small"
                type="link"
                onClick={() =>
                  handleFieldMerge(materialIndex, 'taobaoPrice', adminDetail?.taobaoPrice)
                }
                style={{ padding: '0 4px', fontSize: '10px' }}
              >
                合并
              </Button>
            </div>
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                marginBottom: '4px',
              }}
            >
              <span>
                <strong>权重分数:</strong> {adminDetail?.weightScore || 0}
              </span>
              <Button
                size="small"
                type="link"
                onClick={() =>
                  handleFieldMerge(materialIndex, 'weightScore', adminDetail?.weightScore)
                }
                style={{ padding: '0 4px', fontSize: '10px' }}
              >
                合并
              </Button>
            </div>
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                marginBottom: '4px',
              }}
            >
              <span>
                <strong>标签:</strong> {adminDetail?.tags || '无'}
              </span>
              <Button
                size="small"
                type="link"
                onClick={() => handleFieldMerge(materialIndex, 'tags', adminDetail?.tags)}
                style={{ padding: '0 4px', fontSize: '10px' }}
              >
                合并
              </Button>
            </div>
            {adminDetail?.selectedImg && (
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  marginBottom: '4px',
                }}
              >
                <span>
                  <strong>图片:</strong> {adminDetail.selectedImg.split(',').length}张
                </span>
                <Button
                  size="small"
                  type="link"
                  onClick={() =>
                    handleFieldMerge(materialIndex, 'selectedImg', adminDetail?.selectedImg)
                  }
                  style={{ padding: '0 4px', fontSize: '10px' }}
                >
                  合并
                </Button>
              </div>
            )}
          </div>
        </div>

        {/* 玩法分析信息 */}
        {materialAnalysis && (
          <div>
            <div style={{ fontWeight: 'bold', marginBottom: '8px', color: '#52c41a' }}>
              玩法分析
            </div>
            <div style={{ fontSize: '12px', lineHeight: '1.6' }}>
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  marginBottom: '4px',
                }}
              >
                <span>
                  <strong>适用区域:</strong> {materialAnalysis.applicableAreas || '未知'}
                </span>
                <Button
                  size="small"
                  type="link"
                  onClick={() =>
                    handleFieldMerge(
                      materialIndex,
                      'applicableAreas',
                      materialAnalysis.applicableAreas,
                    )
                  }
                  style={{ padding: '0 4px', fontSize: '10px' }}
                >
                  合并
                </Button>
              </div>
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  marginBottom: '4px',
                }}
              >
                <span>
                  <strong>适用年龄:</strong> {materialAnalysis.minAge || 0}-
                  {materialAnalysis.maxAge || 0}岁
                </span>
                <Button
                  size="small"
                  type="link"
                  onClick={() => {
                    handleFieldMerge(materialIndex, 'minAge', materialAnalysis.minAge);
                    handleFieldMerge(materialIndex, 'maxAge', materialAnalysis.maxAge);
                  }}
                  style={{ padding: '0 4px', fontSize: '10px' }}
                >
                  合并
                </Button>
              </div>
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  marginBottom: '4px',
                }}
              >
                <span>
                  <strong>适用人数:</strong> {materialAnalysis.suitablePeopleMin || 0}-
                  {materialAnalysis.suitablePeopleMax || 0}人
                </span>
                <Button
                  size="small"
                  type="link"
                  onClick={() => {
                    handleFieldMerge(
                      materialIndex,
                      'suitablePeopleMin',
                      materialAnalysis.suitablePeopleMin,
                    );
                    handleFieldMerge(
                      materialIndex,
                      'suitablePeopleMax',
                      materialAnalysis.suitablePeopleMax,
                    );
                  }}
                  style={{ padding: '0 4px', fontSize: '10px' }}
                >
                  合并
                </Button>
              </div>
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  marginBottom: '4px',
                }}
              >
                <span>
                  <strong>材料摘要:</strong> {materialAnalysis.materialSummary || '无'}
                </span>
                <Button
                  size="small"
                  type="link"
                  onClick={() =>
                    handleFieldMerge(
                      materialIndex,
                      'materialSummary',
                      materialAnalysis.materialSummary,
                    )
                  }
                  style={{ padding: '0 4px', fontSize: '10px' }}
                >
                  合并
                </Button>
              </div>
            </div>
          </div>
        )}
      </Card>
    );
  };

  // 渲染合并数据确认界面
  const renderMergeDataSelection = () => {
    console.log('渲染合并数据确认界面, mergeDataList:', mergeDataList);
    console.log('finalSaveDataList:', finalSaveDataList);

    return (
      <div style={{ padding: '20px' }}>
        <div style={{ marginBottom: '16px', fontSize: '16px', fontWeight: 'bold' }}>
          材料合并确认 ({mergeDataList.length} 个材料)
        </div>

        {mergeDataList.length === 0 ? (
          <div style={{ textAlign: 'center', padding: '40px', color: '#999' }}>暂无合并数据</div>
        ) : (
          mergeDataList.map((mergeItem, index) => {
            return (
              <Card key={index} style={{ marginBottom: '24px' }} size="small">
                <div style={{ marginBottom: '16px', fontSize: '14px', fontWeight: 'bold' }}>
                  材料 {index + 1}:{' '}
                  {mergeItem.originalDTO?.adminMaterialDetailDTO?.name ||
                    mergeItem.saveData?.adminMaterialDetailDTO?.name ||
                    '未知材料'}
                </div>

                {/* 情况1：有原始数据和待处理数据 */}
                {mergeItem.originalDTO &&
                mergeItem.pendingList &&
                mergeItem.pendingList.length > 0 ? (
                  <div style={{ marginBottom: '16px', color: '#666' }}>
                    以下是基础数据（原始数据），您可以选择将待处理数据合并到基础数据中：
                  </div>
                ) : mergeItem.saveData ? (
                  <div style={{ marginBottom: '16px', color: '#666' }}>
                    以下是待保存的数据，将直接导入：
                  </div>
                ) : null}

                {/* 判断数据类型并渲染不同的布局 */}
                {mergeItem.originalDTO &&
                mergeItem.pendingList &&
                mergeItem.pendingList.length > 0 ? (
                  // 情况1：有原始数据和待处理数据时，使用左右布局
                  <Row gutter={16}>
                    {/* 左侧：原始数据（基础数据） */}
                    <Col span={12}>
                      {renderMaterialDetailCard(
                        finalSaveDataList[index], // 显示当前的最终数据（可能已经合并过）
                        '基础数据（原始数据）',
                        '#52c41a',
                        index, // 材料索引
                        false, // 不显示编辑按钮
                        false, // 不显示全部合并按钮
                      )}
                    </Col>

                    {/* 右侧：待处理数据列表 */}
                    <Col span={12}>
                      <div style={{ marginBottom: '8px', fontWeight: 'bold', color: '#fa8c16' }}>
                        待处理数据 ({mergeItem.pendingList.length} 个)
                      </div>
                      {mergeItem.pendingList.map((pendingItem: any, pendingIndex: number) =>
                        renderPendingDataCard(pendingItem, pendingIndex, index),
                      )}
                    </Col>
                  </Row>
                ) : mergeItem.saveData ? (
                  // 情况2：只有 saveData 时，单独展示
                  <div style={{ display: 'flex', justifyContent: 'center' }}>
                    <div style={{ width: '60%' }}>
                      {renderMaterialDetailCard(
                        mergeItem.saveData,
                        '待保存数据',
                        '#1890ff',
                        undefined, // 没有材料索引
                        false, // 不显示编辑按钮
                        false, // 不显示全部合并按钮
                      )}
                    </div>
                  </div>
                ) : (
                  // 没有任何数据时的提示
                  <Card style={{ textAlign: 'center', padding: '40px' }}>
                    <div style={{ color: '#999' }}>暂无可用数据</div>
                  </Card>
                )}
              </Card>
            );
          })
        )}
      </div>
    );
  };

  // 表格列定义
  const columns: ProColumns<TeacherMaterial>[] = [
    {
      title: 'ID',
      dataIndex: 'combinedId',
      valueType: 'text',
      width: 80,
      search: false,
    },
    {
      title: '商品名称',
      dataIndex: 'name',
      valueType: 'text',
      width: 200,
      render: (result) => {
        return (
          <Tooltip title={result}>
            <div className="text-ellipsis-three">{result}</div>
          </Tooltip>
        );
      },
      renderFormItem: () => {
        return <ProFormText name="name" placeholder="请输入商品名称" />;
      },
    },
    {
      title: '淘宝链接',
      dataIndex: 'taobaoLink',
      valueType: 'text',
      width: 200,
      render: (result) => {
        if (!result) return '-';
        return (
          <Tooltip title={result}>
            <a
              href={result as string}
              target="_blank"
              rel="noopener noreferrer"
              style={{
                display: 'block',
                maxWidth: '180px',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
              }}
            >
              {result}
            </a>
          </Tooltip>
        );
      },
      search: false,
    },
    {
      title: '商品标题',
      dataIndex: 'taobaoName',
      valueType: 'text',
      width: 250,
      render: (result) => {
        return (
          <Tooltip title={result}>
            <div className="text-ellipsis-three">{result || '-'}</div>
          </Tooltip>
        );
      },
      search: false,
    },
  ];

  return (
    <Modal
      title={showMergeData ? '材料合并确认' : '导入教师材料'}
      open={open}
      onCancel={showMergeData ? handleMergeModalClose : handleModalClose}
      width={showMergeData ? 1600 : 1400}
      style={{ top: 20 }}
      styles={{
        body: {
          maxHeight: 'calc(100vh - 200px)',
          overflowY: 'auto',
          padding: '16px',
        },
      }}
      footer={
        showMergeData
          ? [
              <Button key="back" onClick={handleBackToSelection} disabled={importing}>
                返回
              </Button>,
              <Button
                key="save"
                type="primary"
                onClick={handleSave}
                loading={importing}
                disabled={finalSaveDataList.length === 0}
              >
                {importing ? '保存中...' : `保存 (${finalSaveDataList.length})`}
              </Button>,
            ]
          : [
              <Button key="cancel" onClick={handleModalClose} disabled={importing}>
                取消
              </Button>,
              <Button
                key="import"
                type="primary"
                onClick={handleImport}
                loading={importing}
                disabled={selectedMaterials.length === 0}
              >
                {importing ? '获取数据中...' : `导入 (${selectedMaterials.length})`}
              </Button>,
            ]
      }
    >
      {showMergeData ? (
        // 显示合并数据选择界面
        renderMergeDataSelection()
      ) : (
        // 显示材料选择界面
        <Row gutter={16}>
          {/* 左侧区域树 */}
          <Col span={6}>
            <Card title="区域筛选" size="small">
              <Spin spinning={treeLoading}>
                {areaTreeData.length > 0 ? (
                  <Tree
                    treeData={areaTreeData}
                    selectedKeys={selectedAreaKeys}
                    expandedKeys={expandedKeys}
                    onSelect={handleAreaSelect}
                    onExpand={(keys) => setExpandedKeys(keys.map((key) => String(key)))}
                    showLine
                    height={500}
                  />
                ) : (
                  <div style={{ textAlign: 'center', padding: '20px', color: '#999' }}>
                    暂无区域数据
                  </div>
                )}
              </Spin>
            </Card>
          </Col>

          {/* 右侧材料列表 */}
          <Col span={18}>
            <ProTable<TeacherMaterial>
              actionRef={actionRef}
              rowKey="combinedId"
              search={{
                labelWidth: 'auto',
                defaultCollapsed: false,
                collapsed: false,
                collapseRender: false,
              }}
              toolBarRender={false}
              rowSelection={{
                selectedRowKeys,
                onChange: handleRowSelectionChange,
                preserveSelectedRowKeys: true,
              }}
              request={async (params) => {
                const response = await queryTeacherMaterial({
                  ...params,
                  ...currentFilters, // 合并筛选条件
                  pageNo: params.current || 1,
                  pageSize: params.pageSize || 10,
                });

                if ((response as any)?.status === 0) {
                  const data = (response as any)?.data || {};
                  const list = data.list || data.records || data || [];
                  const total = data.total || data.totalCount || list.length;

                  return {
                    data: list,
                    success: true,
                    total: total,
                  };
                } else {
                  message.error((response as any)?.message || '获取教师材料失败');
                  return {
                    data: [],
                    success: false,
                    total: 0,
                  };
                }
              }}
              columns={columns}
              scroll={{ x: 1000 }}
              pagination={{
                showSizeChanger: true,
                pageSizeOptions: ['25', '50', '100'],
                defaultPageSize: 25,
                showQuickJumper: true,
                showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`,
              }}
            />
          </Col>
        </Row>
      )}
    </Modal>
  );
};

export default TeacherMaterialModal;
