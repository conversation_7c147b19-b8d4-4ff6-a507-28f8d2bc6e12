import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { PageContainer, ProFormSelect, ProFormText, ProTable } from '@ant-design/pro-components';
import { Button, Tooltip, Modal, message, Image } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { queryMaterialInfo, deleteMaterialById, publishMaterial } from '@/services/areaMaterials';
import MaterialDetailTabs from './components/MaterialDetailTabs';
import AnalyzeMaterial from './components/analyzeMaterial';
import BatchAnalyzeModal from './components/BatchAnalyzeModal';
import TeacherMaterialModal from './teacher';
import { useModel } from '@umijs/max';
import { DictionaryCategory } from '@/services/constants';
import { isEmpty } from 'lodash';

const ProductList: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [selectedMaterial, setSelectedMaterial] = useState<any>(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [analyzeModalOpen, setAnalyzeModalOpen] = useState(false);
  const [batchAnalyzeModalOpen, setBatchAnalyzeModalOpen] = useState(false);
  const [teacherMaterialModalOpen, setTeacherMaterialModalOpen] = useState(false);

  // 批量选择相关状态
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [selectedRows, setSelectedRows] = useState<any[]>([]);
  const [publishing, setPublishing] = useState(false);

  // 区域列表
  const [areas, setAreas] = useState<any>([]);

  const { initialState } = useModel('@@initialState');
  const { dictionaryList } = initialState || {};

  // 获取区域数据
  useEffect(() => {
    if (!isEmpty(dictionaryList)) {
      const result: any = dictionaryList?.map((item) => {
        return {
          value: item?.id,
          label: item?.value,
          category: item?.category,
        };
      });

      const _areas = result.filter((item: any) => item.category === DictionaryCategory.Area);
      setAreas(_areas);
    }
  }, [dictionaryList]);

  // 删除材料
  const handleDelete = async (record: any) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除材料"${record.name}"吗？此操作不可恢复。`,
      okText: '确认删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          const response = await deleteMaterialById({
            id: record.id,
            sourceType: record.sourceType,
          });

          if ((response as any)?.status === 0) {
            message.success('删除成功');
            // 刷新表格数据
            actionRef.current?.reload();
          } else {
            message.error((response as any)?.message || '删除失败');
          }
        } catch (error) {
          console.error('删除失败:', error);
          message.error('删除失败，请重试');
        }
      },
    });
  };

  // 处理导入教师材料
  const handleImportTeacherMaterials = (selectedMaterials: any[]) => {
    console.log('导入的教师材料:', selectedMaterials);
    // 刷新表格数据
    actionRef.current?.reload();
  };

  // 单个发布
  const handlePublishSingle = async (record: any) => {
    Modal.confirm({
      title: '确认发布',
      content: `确定要发布材料"${record.name}"吗？`,
      okText: '确认发布',
      cancelText: '取消',
      onOk: async () => {
        try {
          setPublishing(true);
          const response = await publishMaterial({
            combinedIds: [record.combinedId || record.id],
          });

          if ((response as any)?.status === 0) {
            message.success('发布成功');
            // 刷新表格数据
            actionRef.current?.reload();
          } else {
            message.error((response as any)?.message || '发布失败');
          }
        } catch (error) {
          console.error('发布失败:', error);
          message.error('发布失败，请重试');
        } finally {
          setPublishing(false);
        }
      },
    });
  };

  // 批量发布
  const handleBatchPublish = async () => {
    if (selectedRows.length === 0) {
      message.warning('请选择要发布的材料');
      return;
    }

    Modal.confirm({
      title: '确认批量发布',
      content: `确定要发布选中的 ${selectedRows.length} 个材料吗？`,
      okText: '确认发布',
      cancelText: '取消',
      onOk: async () => {
        try {
          setPublishing(true);
          const combinedIds = selectedRows.map((row) => row.combinedId || row.id);
          const response = await publishMaterial({
            combinedIds: combinedIds,
          });

          if ((response as any)?.status === 0) {
            message.success(`成功发布 ${selectedRows.length} 个材料`);
            // 清空选择
            setSelectedRowKeys([]);
            setSelectedRows([]);
            // 刷新表格数据
            actionRef.current?.reload();
          } else {
            message.error((response as any)?.message || '批量发布失败');
          }
        } catch (error) {
          console.error('批量发布失败:', error);
          message.error('批量发布失败，请重试');
        } finally {
          setPublishing(false);
        }
      },
    });
  };

  const columns: ProColumns<any>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      valueType: 'text',
      width: 80,
      search: false,
    },
    {
      title: '材料首图',
      dataIndex: 'selectedImg',
      valueType: 'text',
      width: 100,
      search: false,
      render: (_, record) => {
        const imageUrl = record.selectedImg;
        if (!imageUrl) return '-';

        // 处理可能的多张图片，取第一张
        const firstImage =
          typeof imageUrl === 'string'
            ? imageUrl.includes(',')
              ? imageUrl.split(',')[0]
              : imageUrl
            : Array.isArray(imageUrl)
            ? imageUrl[0]
            : imageUrl;

        return (
          <Image
            src={firstImage}
            alt="材料首图"
            width={60}
            height={60}
            style={{ objectFit: 'cover', borderRadius: 4 }}
            fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
          />
        );
      },
    },
    {
      title: '材料名称',
      dataIndex: 'name',
      valueType: 'text',
      width: 300,
      render: (result) => {
        return (
          <Tooltip title={result}>
            <div className="text-ellipsis-three">{result}</div>
          </Tooltip>
        );
      },
      renderFormItem: () => {
        return <ProFormText name="name" placeholder="请输入材料名称" />;
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      valueType: 'text',
      width: 150,
      search: false,
    },
    {
      title: '玩法数量',
      dataIndex: 'playNum',
      valueType: 'text',
      width: 100,
      search: false,
    },
    {
      title: '材料类型',
      dataIndex: 'sourceType',
      valueType: 'text',
      width: 120,
      render: (result) => {
        const typeMap: Record<string, string> = {
          toy: '玩具',
          book: '书籍',
        };
        return typeMap[result as string] || result;
      },
      search: false,
    },
    {
      title: '区域',
      dataIndex: 'area',
      valueType: 'text',
      width: 120,
      render: (result) => {
        return result || '-';
      },
      search: false,
    },
    {
      title: '区域',
      dataIndex: 'area',
      hideInTable: true,
      renderFormItem: () => {
        return <ProFormSelect name="area" placeholder="请选择区域" options={areas} />;
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      valueType: 'option',
      width: 240,
      fixed: 'right',
      render: (_, record) => [
        <Button
          key="view"
          type="link"
          size="small"
          onClick={() => {
            setSelectedMaterial(record);
            setIsEditMode(false);
            setDrawerOpen(true);
          }}
        >
          查看
        </Button>,
        <Button
          key="edit"
          type="link"
          size="small"
          onClick={() => {
            setSelectedMaterial(record);
            setIsEditMode(true);
            setDrawerOpen(true);
          }}
        >
          编辑
        </Button>,
        <Button
          key="publish"
          type="link"
          size="small"
          loading={publishing}
          onClick={() => {
            handlePublishSingle(record);
          }}
        >
          发布
        </Button>,
        <Button
          key="delete"
          type="link"
          size="small"
          danger
          onClick={() => {
            handleDelete(record);
          }}
        >
          删除
        </Button>,
      ],
    },
  ];

  return (
    <PageContainer>
      <ProTable<API.RuleListItem, API.PageParams>
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 'auto',
          defaultCollapsed: false,
          collapsed: false,
          collapseRender: false,
        }}
        rowSelection={{
          selectedRowKeys,
          onChange: (keys, rows) => {
            setSelectedRowKeys(keys);
            setSelectedRows(rows);
          },
          preserveSelectedRowKeys: true,
        }}
        toolBarRender={() => [
          <Button
            key="batchPublish"
            type="primary"
            style={{ marginRight: 8 }}
            loading={publishing}
            disabled={selectedRows.length === 0}
            onClick={handleBatchPublish}
          >
            批量发布 {selectedRows.length > 0 && `(${selectedRows.length})`}
          </Button>,
          <Button
            key="analyze"
            type="primary"
            style={{ marginRight: 8 }}
            onClick={() => {
              setAnalyzeModalOpen(true);
            }}
          >
            分析材料
          </Button>,
          <Button
            key="batchAnalyze"
            type="primary"
            style={{ marginRight: 8 }}
            onClick={() => {
              setBatchAnalyzeModalOpen(true);
            }}
          >
            批量分析
          </Button>,
          <Button
            key="import"
            type="primary"
            onClick={() => {
              setTeacherMaterialModalOpen(true);
            }}
          >
            导入教师材料
          </Button>,
        ]}
        request={async (params) => {
          const response = await queryMaterialInfo({
            ...params,
            isReviewed: 0,
          });
          return {
            data: Array.isArray(response?.data) ? response.data : [],
            success: (response as any)?.status === 0,
            total: Array.isArray(response?.data) ? response.data.length : 0,
          };
        }}
        columns={columns}
        scroll={{ x: 1000 }}
      />

      <MaterialDetailTabs
        open={drawerOpen}
        onClose={() => {
          setDrawerOpen(false);
          setSelectedMaterial(null);
          setIsEditMode(false);
        }}
        materialId={selectedMaterial?.id}
        sourceType={selectedMaterial?.sourceType}
        initialEditMode={isEditMode}
        materialData={selectedMaterial}
      />

      <AnalyzeMaterial
        open={analyzeModalOpen}
        onClose={() => setAnalyzeModalOpen(false)}
        onSuccess={() => {
          // 刷新表格数据
          actionRef.current?.reload();
        }}
      />

      <BatchAnalyzeModal
        open={batchAnalyzeModalOpen}
        onClose={() => setBatchAnalyzeModalOpen(false)}
        onSuccess={() => {
          // 刷新表格数据
          actionRef.current?.reload();
        }}
      />

      <TeacherMaterialModal
        open={teacherMaterialModalOpen}
        onClose={() => setTeacherMaterialModalOpen(false)}
        onImport={handleImportTeacherMaterials}
      />
    </PageContainer>
  );
};

export default ProductList;
