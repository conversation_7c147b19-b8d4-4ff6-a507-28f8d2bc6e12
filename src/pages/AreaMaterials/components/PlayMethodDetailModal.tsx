import React from 'react';
import { Modal, Descriptions, Tag, Space } from 'antd';

interface PlayMethodDetailModalProps {
  open: boolean;
  onClose: () => void;
  playMethod: any;
}

const PlayMethodDetailModal: React.FC<PlayMethodDetailModalProps> = ({
  open,
  onClose,
  playMethod,
}) => {
  if (!playMethod) return null;

  // 解析核心经验（如果是JSON字符串）
  const getCoreExperience = () => {
    try {
      if (typeof playMethod.coreExperience === 'string') {
        return JSON.parse(playMethod.coreExperience);
      }
      return playMethod.coreExperience || [];
    } catch {
      return [];
    }
  };

  const coreExperienceList = getCoreExperience();

  return (
    <Modal
      title={`玩法详情 - ${playMethod.playName || '未命名玩法'}`}
      open={open}
      onCancel={onClose}
      footer={null}
      width={800}
      style={{ top: 20 }}
    >
      <Descriptions column={2} bordered size="small">
        <Descriptions.Item label="玩法ID" span={1}>
          {playMethod.id || '-'}
        </Descriptions.Item>
        <Descriptions.Item label="组合材料ID" span={1}>
          {playMethod.combinedId || '-'}
        </Descriptions.Item>

        <Descriptions.Item label="玩法名称" span={2}>
          {playMethod.playName || '-'}
        </Descriptions.Item>

        <Descriptions.Item label="年龄段" span={1}>
          <Tag color="blue">
            {playMethod.ageMin}-{playMethod.ageMax}岁
          </Tag>
        </Descriptions.Item>
        <Descriptions.Item label="投放区域" span={1}>
          <Tag color="green">{playMethod.area || '-'}</Tag>
        </Descriptions.Item>

        <Descriptions.Item label="适宜人数" span={1}>
          <Tag color="orange">
            {playMethod.suitablePeopleMin}-{playMethod.suitablePeopleMax}人
          </Tag>
        </Descriptions.Item>
        <Descriptions.Item label="结构化程度" span={1}>
          <Tag color="purple">{playMethod.structured || '-'}</Tag>
        </Descriptions.Item>

        <Descriptions.Item label="玩法介绍" span={2}>
          <div
            style={{
              maxHeight: '120px',
              overflowY: 'auto',
              lineHeight: '1.6',
              padding: '8px 0',
            }}
          >
            {playMethod.playIntro || '暂无介绍'}
          </div>
        </Descriptions.Item>

        {playMethod.teacherSupport && (
          <Descriptions.Item label="教师支持" span={2}>
            <div
              style={{
                maxHeight: '120px',
                overflowY: 'auto',
                lineHeight: '1.6',
                padding: '8px 0',
              }}
            >
              {playMethod.teacherSupport}
            </div>
          </Descriptions.Item>
        )}

        {coreExperienceList.length > 0 && (
          <Descriptions.Item label="核心经验" span={2}>
            <div style={{ maxHeight: '150px', overflowY: 'auto' }}>
              <Space direction="vertical" size="small" style={{ width: '100%' }}>
                {coreExperienceList.map((experience: string, index: number) => (
                  <Tag key={index} color="cyan" style={{ margin: '2px 0' }}>
                    {experience}
                  </Tag>
                ))}
              </Space>
            </div>
          </Descriptions.Item>
        )}

        <Descriptions.Item label="核心经验ID" span={2}>
          {playMethod.coreExperienceId || '-'}
        </Descriptions.Item>

        <Descriptions.Item label="点赞数" span={1}>
          {playMethod.likeNum || 0}
        </Descriptions.Item>
        <Descriptions.Item label="不喜欢数" span={1}>
          {playMethod.dislikeNum || 0}
        </Descriptions.Item>

        <Descriptions.Item label="是否DIY" span={1}>
          <Tag color={playMethod.isDiy ? 'red' : 'default'}>{playMethod.isDiy ? '是' : '否'}</Tag>
        </Descriptions.Item>
        <Descriptions.Item label="玩法采用" span={1}>
          <Tag
            color={
              playMethod.playAdopt === 1 ? 'green' : playMethod.playAdopt === -1 ? 'red' : 'orange'
            }
          >
            {playMethod.playAdopt === 1
              ? '已采用'
              : playMethod.playAdopt === -1
              ? '未采用'
              : '新生成'}
          </Tag>
        </Descriptions.Item>

        <Descriptions.Item label="是否删除" span={1}>
          <Tag color={playMethod.isDeleted ? 'red' : 'green'}>
            {playMethod.isDeleted ? '已删除' : '正常'}
          </Tag>
        </Descriptions.Item>
        <Descriptions.Item label="更新时间" span={1}>
          {playMethod.updateTime || '-'}
        </Descriptions.Item>
      </Descriptions>
    </Modal>
  );
};

export default PlayMethodDetailModal;
