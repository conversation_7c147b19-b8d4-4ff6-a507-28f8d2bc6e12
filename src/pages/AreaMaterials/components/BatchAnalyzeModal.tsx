import React, { useState, useEffect } from 'react';
import { Modal, Form, Select, Upload, Button, message, Row, Col } from 'antd';
import { InboxOutlined } from '@ant-design/icons';
import { importMaterial } from '@/services/areaMaterials';
// 不再需要 OSS 相关的导入
import { useModel } from '@umijs/max';
import { DictionaryCategory } from '@/services/constants';
import { isEmpty } from 'lodash';

const { Dragger } = Upload;

interface BatchAnalyzeModalProps {
  open: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

const BatchAnalyzeModal: React.FC<BatchAnalyzeModalProps> = ({ open, onClose, onSuccess }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [fileList, setFileList] = useState<any[]>([]);
  const [originalFile, setOriginalFile] = useState<File | null>(null);
  const [areas, setAreas] = useState<any[]>([]);

  const { initialState } = useModel('@@initialState');
  const { dictionaryList } = initialState || {};

  // 获取区域数据
  useEffect(() => {
    if (!isEmpty(dictionaryList)) {
      const result: any = dictionaryList?.map((item) => {
        return {
          value: item?.id,
          label: item?.value,
          category: item?.category,
        };
      });

      const _areas = result.filter((item: any) => item.category === DictionaryCategory.Area);
      setAreas(_areas);
    }
  }, [dictionaryList]);

  // 不再需要OSS客户端初始化

  // 重置表单和状态
  const resetForm = () => {
    form.resetFields();
    setFileList([]);
    setOriginalFile(null);
    setLoading(false);
  };

  // 处理弹窗关闭
  const handleClose = () => {
    resetForm();
    onClose();
  };

  // 文件上传前的处理
  const beforeUpload = (file: File) => {
    // 检查文件类型
    const isExcel =
      file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
      file.type === 'application/vnd.ms-excel' ||
      file.name.endsWith('.xlsx') ||
      file.name.endsWith('.xls');

    if (!isExcel) {
      message.error('只能上传 Excel 文件！');
      return false;
    }

    // 检查文件大小 (限制为10MB)
    const isLt10M = file.size / 1024 / 1024 < 10;
    if (!isLt10M) {
      message.error('文件大小不能超过 10MB！');
      return false;
    }

    return true;
  };

  // 文件上传处理
  const handleFileUpload = async (file: File) => {
    try {
      // 保存原始文件对象
      setOriginalFile(file);

      const uploadedFile = {
        uid: file.name + Date.now(),
        name: file.name,
        status: 'done',
        originFileObj: file,
      };

      setFileList([uploadedFile]);
      message.success('文件选择成功');
      return false; // 阻止默认上传行为
    } catch (error) {
      console.error('文件处理失败:', error);
      message.error('文件处理失败，请重试');
      return false;
    }
  };

  // 移除文件
  const handleRemove = () => {
    setFileList([]);
    setOriginalFile(null);
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();

      if (!originalFile) {
        message.error('请先上传文件');
        return;
      }

      setLoading(true);

      // 准备 FormData，area 作为 Query 参数，file 作为 Body 参数
      const formData = new FormData();
      formData.append('file', originalFile);

      // 调用批量导入接口，area 作为 Query 参数
      const response = await importMaterial({
        area: values.area,
        file: originalFile,
      });

      if ((response as any)?.status === 0) {
        message.success('批量分析完成');
        handleClose();
        onSuccess?.();
      } else {
        message.error((response as any)?.message || '批量分析失败');
      }
    } catch (error) {
      console.error('批量分析失败:', error);
      message.error('批量分析失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title="批量分析材料"
      open={open}
      onCancel={handleClose}
      width={600}
      footer={[
        <Button key="cancel" onClick={handleClose}>
          取消
        </Button>,
        <Button key="submit" type="primary" loading={loading} onClick={handleSubmit}>
          完成
        </Button>,
      ]}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          area: undefined,
        }}
      >
        <Row gutter={16}>
          <Col span={24}>
            <Form.Item
              label="选择区域"
              name="area"
              rules={[{ required: true, message: '请选择区域' }]}
            >
              <Select placeholder="请选择区域" options={areas} allowClear />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={24}>
            <Form.Item label="上传文件" required>
              <Dragger
                fileList={fileList}
                beforeUpload={beforeUpload}
                customRequest={({ file }) => handleFileUpload(file as File)}
                onRemove={handleRemove}
                maxCount={1}
                accept=".xlsx,.xls"
              >
                <p className="ant-upload-drag-icon">
                  <InboxOutlined />
                </p>
                <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
                <p className="ant-upload-hint">
                  支持 Excel 文件格式（.xlsx, .xls），文件大小不超过 10MB
                </p>
              </Dragger>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Modal>
  );
};

export default BatchAnalyzeModal;
