/* eslint-disable guard-for-in */
import React from 'react';
import { Button, Descriptions, Input, InputNumber, Select, Image, Upload } from 'antd';
import { DeleteOutlined, PlusOutlined, UpOutlined, DownOutlined } from '@ant-design/icons';

const { TextArea } = Input;

interface BasicInfoProps {
  materialDetail: any;
  setMaterialDetail: (detail: any) => void;
  isEditing: boolean;
  selectedImages: string[];
  imageFileList: any[];
  handleImageUpload: (file: any) => Promise<boolean>;
  handleImageRemove: (file: any) => void;
  moveImageUp: (index: number) => void;
  moveImageDown: (index: number) => void;
  sourceType?: string;
}

const BasicInfo: React.FC<BasicInfoProps> = ({
  materialDetail,
  setMaterialDetail,
  isEditing,
  selectedImages,
  imageFileList,
  handleImageUpload,
  handleImageRemove,
  moveImageUp,
  moveImageDown,
  sourceType,
}) => {
  // 判断是否为图书类型
  const isBook = sourceType === 'book';

  return (
    <div style={{ padding: '0 24px' }}>
      <Descriptions title="基本信息" style={{ marginBottom: 32 }} column={1} layout="vertical">
        <Descriptions.Item label={isBook ? '图书名称' : '商品名称'}>
          {isEditing ? (
            <Input
              value={materialDetail?.name}
              onChange={(e) => setMaterialDetail({ ...materialDetail, name: e.target.value })}
            />
          ) : (
            materialDetail?.name
          )}
        </Descriptions.Item>

        {!isBook && (
          <>
            <Descriptions.Item label="淘宝商品标题">
              {isEditing ? (
                <Input
                  value={materialDetail?.taobaoName}
                  onChange={(e) =>
                    setMaterialDetail({ ...materialDetail, taobaoName: e.target.value })
                  }
                />
              ) : (
                materialDetail?.taobaoName
              )}
            </Descriptions.Item>

            <Descriptions.Item label="淘宝商品链接">
              {isEditing ? (
                <Input
                  value={materialDetail?.taobaoLink}
                  onChange={(e) =>
                    setMaterialDetail({ ...materialDetail, taobaoLink: e.target.value })
                  }
                />
              ) : (
                <a href={materialDetail?.taobaoLink} target="_blank" rel="noreferrer">
                  {materialDetail?.taobaoLink}
                </a>
              )}
            </Descriptions.Item>
          </>
        )}

        {!isBook && (
          <>
            <Descriptions.Item label="材料分类">
              {isEditing ? (
                <Input
                  value={materialDetail?.category}
                  onChange={(e) =>
                    setMaterialDetail({ ...materialDetail, category: e.target.value })
                  }
                  placeholder="请输入材料分类"
                />
              ) : (
                materialDetail?.category || '-'
              )}
            </Descriptions.Item>

            <Descriptions.Item label="品牌">
              {isEditing ? (
                <Input
                  value={materialDetail?.brand}
                  onChange={(e) => setMaterialDetail({ ...materialDetail, brand: e.target.value })}
                />
              ) : (
                materialDetail?.brand
              )}
            </Descriptions.Item>

            <Descriptions.Item label="标签数组">
              {isEditing ? (
                <Input
                  value={materialDetail?.tags}
                  onChange={(e) => setMaterialDetail({ ...materialDetail, tags: e.target.value })}
                  placeholder="请输入标签，多个标签用逗号分隔"
                />
              ) : (
                materialDetail?.tags || '-'
              )}
            </Descriptions.Item>

            <Descriptions.Item label="材料白描">
              {isEditing ? (
                <TextArea
                  value={materialDetail?.description}
                  onChange={(e) =>
                    setMaterialDetail({ ...materialDetail, description: e.target.value })
                  }
                  rows={3}
                  placeholder="请输入材料白描"
                />
              ) : (
                materialDetail?.description || '-'
              )}
            </Descriptions.Item>

            <Descriptions.Item label="图片OCR提取文本">
              {isEditing ? (
                <TextArea
                  value={materialDetail?.ocrText}
                  onChange={(e) =>
                    setMaterialDetail({ ...materialDetail, ocrText: e.target.value })
                  }
                  rows={3}
                  placeholder="请输入OCR提取的文本"
                />
              ) : (
                materialDetail?.ocrText || '-'
              )}
            </Descriptions.Item>
          </>
        )}

        {!isBook && (
          <>
            <Descriptions.Item label="材质">
              {isEditing ? (
                <Input
                  value={materialDetail?.material}
                  onChange={(e) =>
                    setMaterialDetail({ ...materialDetail, material: e.target.value })
                  }
                  placeholder="请输入材质"
                />
              ) : (
                materialDetail?.material || '-'
              )}
            </Descriptions.Item>

            <Descriptions.Item label="单位">
              {isEditing ? (
                <Input
                  value={materialDetail?.unit}
                  onChange={(e) => setMaterialDetail({ ...materialDetail, unit: e.target.value })}
                  placeholder="请输入单位"
                />
              ) : (
                materialDetail?.unit || '-'
              )}
            </Descriptions.Item>

            <Descriptions.Item label="规格">
              {isEditing ? (
                <Input
                  value={materialDetail?.specification}
                  onChange={(e) =>
                    setMaterialDetail({ ...materialDetail, specification: e.target.value })
                  }
                  placeholder="请输入规格"
                />
              ) : (
                materialDetail?.specification || '-'
              )}
            </Descriptions.Item>
          </>
        )}

        {!isBook && (
          <>
            <Descriptions.Item label="平台售价">
              {isEditing ? (
                <InputNumber
                  value={materialDetail?.linePrice}
                  onChange={(value) => setMaterialDetail({ ...materialDetail, linePrice: value })}
                  placeholder="请输入平台售价"
                  style={{ width: '100%' }}
                  addonAfter="元"
                />
              ) : materialDetail?.linePrice ? (
                `${materialDetail.linePrice}元`
              ) : (
                '-'
              )}
            </Descriptions.Item>

            <Descriptions.Item label="淘宝售价">
              {isEditing ? (
                <InputNumber
                  value={materialDetail?.taobaoPrice}
                  onChange={(value) => setMaterialDetail({ ...materialDetail, taobaoPrice: value })}
                  placeholder="请输入淘宝售价"
                  style={{ width: '100%' }}
                  addonAfter="元"
                />
              ) : materialDetail?.taobaoPrice ? (
                `${materialDetail.taobaoPrice}元`
              ) : (
                '-'
              )}
            </Descriptions.Item>

            <Descriptions.Item label="规划线">
              {isEditing ? (
                <Input
                  value={materialDetail?.planLine}
                  onChange={(e) =>
                    setMaterialDetail({ ...materialDetail, planLine: e.target.value })
                  }
                  placeholder="请输入规划线"
                />
              ) : (
                materialDetail?.planLine || '-'
              )}
            </Descriptions.Item>

            <Descriptions.Item label="品质等级">
              {isEditing ? (
                <Input
                  value={materialDetail?.qualityLevel}
                  onChange={(e) =>
                    setMaterialDetail({ ...materialDetail, qualityLevel: e.target.value })
                  }
                  placeholder="请输入品质等级"
                />
              ) : (
                materialDetail?.qualityLevel || '-'
              )}
            </Descriptions.Item>

            <Descriptions.Item label="安全等级">
              {isEditing ? (
                <Input
                  value={materialDetail?.safetyLevel}
                  onChange={(e) =>
                    setMaterialDetail({ ...materialDetail, safetyLevel: e.target.value })
                  }
                  placeholder="请输入安全等级"
                />
              ) : (
                materialDetail?.safetyLevel || '-'
              )}
            </Descriptions.Item>

            <Descriptions.Item label="重量">
              {isEditing ? (
                <InputNumber
                  value={materialDetail?.weightScore}
                  onChange={(value) => setMaterialDetail({ ...materialDetail, weightScore: value })}
                  placeholder="请输入重量"
                  style={{ width: '100%' }}
                  addonAfter="分"
                />
              ) : materialDetail?.weightScore ? (
                `${materialDetail.weightScore}分`
              ) : (
                '-'
              )}
            </Descriptions.Item>

            <Descriptions.Item label="推荐权重">
              {isEditing ? (
                <InputNumber
                  value={materialDetail?.recommendWeight}
                  onChange={(value) =>
                    setMaterialDetail({ ...materialDetail, recommendWeight: value })
                  }
                  placeholder="请输入推荐权重"
                  style={{ width: '100%' }}
                />
              ) : (
                materialDetail?.recommendWeight || '-'
              )}
            </Descriptions.Item>

            <Descriptions.Item label="商品69码">
              {isEditing ? (
                <Input
                  value={materialDetail?.barcode}
                  onChange={(e) =>
                    setMaterialDetail({ ...materialDetail, barcode: e.target.value })
                  }
                  placeholder="请输入商品69码"
                />
              ) : (
                materialDetail?.barcode || '-'
              )}
            </Descriptions.Item>

            <Descriptions.Item label="软删除标记">
              {isEditing ? (
                <Select
                  value={materialDetail?.isDeleted}
                  onChange={(value) => setMaterialDetail({ ...materialDetail, isDeleted: value })}
                  placeholder="请选择软删除标记"
                  style={{ width: '100%' }}
                  options={[
                    { label: '正常', value: 0 },
                    { label: '已删除', value: 1 },
                  ]}
                />
              ) : materialDetail?.isDeleted === 1 ? (
                '已删除'
              ) : (
                '正常'
              )}
            </Descriptions.Item>

            <Descriptions.Item label="用户选择的图片">
              {isEditing ? (
                <Input
                  value={materialDetail?.userImages}
                  onChange={(e) =>
                    setMaterialDetail({ ...materialDetail, userImages: e.target.value })
                  }
                  placeholder="请输入用户选择的图片"
                />
              ) : (
                materialDetail?.userImages || '-'
              )}
            </Descriptions.Item>

            <Descriptions.Item label="区域">
              {isEditing ? (
                <Input
                  value={materialDetail?.area}
                  onChange={(e) => setMaterialDetail({ ...materialDetail, area: e.target.value })}
                  placeholder="请输入区域"
                />
              ) : (
                materialDetail?.area || '-'
              )}
            </Descriptions.Item>
          </>
        )}
      </Descriptions>

      {/* 材料图片管理 */}
      <Descriptions title={isBook ? '图书封面' : '材料图片'} style={{ marginBottom: 32 }}>
        <Descriptions.Item label="" span={2}>
          {isEditing ? (
            <div>
              <div style={{ marginBottom: 16 }}>
                <Upload
                  listType="picture-card"
                  fileList={imageFileList}
                  beforeUpload={handleImageUpload}
                  onRemove={handleImageRemove}
                  accept="image/*"
                  multiple
                >
                  {imageFileList.length >= 10 ? null : (
                    <div>
                      <PlusOutlined />
                      <div style={{ marginTop: 8 }}>上传图片</div>
                    </div>
                  )}
                </Upload>
              </div>

              {imageFileList.length > 0 && (
                <div>
                  <p style={{ marginBottom: 16, fontWeight: 'bold' }}>图片排序：</p>
                  <div style={{ display: 'flex', flexWrap: 'wrap', gap: '16px' }}>
                    {imageFileList.map((file, index) => (
                      <div
                        key={file.uid}
                        style={{
                          position: 'relative',
                          border: '1px solid #d9d9d9',
                          borderRadius: '8px',
                          padding: '8px',
                          textAlign: 'center',
                        }}
                      >
                        <Image
                          src={file.url}
                          width={100}
                          height={100}
                          style={{ objectFit: 'cover' }}
                          preview={false}
                        />
                        <div
                          style={{
                            marginTop: 8,
                            display: 'flex',
                            justifyContent: 'center',
                            gap: '4px',
                          }}
                        >
                          <Button
                            size="small"
                            icon={<UpOutlined />}
                            disabled={index === 0}
                            onClick={() => moveImageUp(index)}
                          />
                          <Button
                            size="small"
                            icon={<DownOutlined />}
                            disabled={index === imageFileList.length - 1}
                            onClick={() => moveImageDown(index)}
                          />
                          <Button
                            size="small"
                            icon={<DeleteOutlined />}
                            danger
                            onClick={() => handleImageRemove(file)}
                          />
                        </div>
                        <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
                          {index + 1}
                        </div>
                      </div>
                    ))}
                  </div>
                  <p style={{ marginTop: 16, color: '#666' }}>共 {imageFileList.length} 张图片</p>
                </div>
              )}
            </div>
          ) : (
            <div>
              {selectedImages.length > 0 ? (
                <div style={{ display: 'flex', flexWrap: 'wrap', gap: '16px' }}>
                  {selectedImages.map((url: string, index: number) => (
                    <div key={index} style={{ textAlign: 'center' }}>
                      <Image
                        src={url}
                        width={120}
                        height={120}
                        style={{ objectFit: 'cover', border: '1px solid #d9d9d9' }}
                        preview
                      />
                      <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
                        {index + 1}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div>
                  <span>暂无选择的图片</span>
                </div>
              )}
            </div>
          )}
        </Descriptions.Item>
      </Descriptions>
    </div>
  );
};

export default BasicInfo;
