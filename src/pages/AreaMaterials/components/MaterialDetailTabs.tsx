/* eslint-disable guard-for-in */
import {
  queryMaterialDetail,
  editMaterialBaseInfo,
  editBookBaseInfo,
} from '@/services/areaMaterials';
import { fetchUserSelectedSchoolsAndClasses } from '@/services/api';
import { Button, Drawer, message, Tabs } from 'antd';
import { fetchClient, handleUpload } from '@/services/fileUpload';
import React, { useEffect, useState } from 'react';
import BasicInfo from './BasicInfo';
import MaterialAnalysis from './MaterialAnalysis';
import ExistingPlayMethods from './ExistingPlayMethods';
import TeacherPlayMethods from './TeacherPlayMethods';

interface MaterialDetailTabsProps {
  open: boolean;
  onClose: () => void;
  materialId?: number;
  sourceType?: string;
  initialEditMode?: boolean;
  materialData?: any; // 列表数据，用于图书类型
}

const MaterialDetailTabs: React.FC<MaterialDetailTabsProps> = ({
  open,
  onClose,
  materialId,
  sourceType,
  initialEditMode = false,
  materialData,
}) => {
  const [materialDetail, setMaterialDetail] = useState<any>({});
  const [isEditing, setIsEditing] = useState(initialEditMode);
  const [loading, setLoading] = useState(false);
  const [selectedImages, setSelectedImages] = useState<string[]>([]);
  const [client, setClient] = useState<any>(null);
  const [imageFileList, setImageFileList] = useState<any[]>([]);
  const [userInfo, setUserInfo] = useState<any>({});
  const [activeTab, setActiveTab] = useState('basic');

  // 获取用户选择的学校和班级信息
  useEffect(() => {
    fetchUserSelectedSchoolsAndClasses()
      .then((res) => {
        if (res?.status === 0) {
          setUserInfo(res.data);
        }
      })
      .catch((error) => {
        console.error('获取用户信息失败:', error);
      });
  }, []);

  useEffect(() => {
    if (open && materialId && sourceType) {
      setLoading(true);
      setIsEditing(initialEditMode); // 设置为传入的编辑模式

      if (sourceType === 'book') {
        // 图书类型直接使用列表数据
        console.log('图书数据:', materialData); // 调试信息
        setMaterialDetail(materialData || {});

        // 初始化选中的图片 - 图书可能有封面图片
        // 检查多种可能的图片字段
        let imageUrls: string[] = [];

        if (materialData?.selectedImg) {
          if (typeof materialData.selectedImg === 'string') {
            // 如果是字符串，检查是否包含逗号分隔的多个URL
            if (materialData.selectedImg.includes(',')) {
              imageUrls = materialData.selectedImg.split(',').filter((url: string) => url.trim());
            } else {
              imageUrls = [materialData.selectedImg];
            }
          } else if (Array.isArray(materialData.selectedImg)) {
            imageUrls = materialData.selectedImg;
          } else {
            imageUrls = [];
          }
        } else if (materialData?.imgUrls) {
          imageUrls = Array.isArray(materialData.imgUrls) ? materialData.imgUrls : [];
        } else if (materialData?.images) {
          imageUrls = Array.isArray(materialData.images) ? materialData.images : [];
        } else if (materialData?.oriPic) {
          imageUrls =
            typeof materialData.oriPic === 'string'
              ? [materialData.oriPic]
              : Array.isArray(materialData.oriPic)
              ? materialData.oriPic
              : [];
        }

        console.log('图书图片字段检查:', {
          selectedImg: materialData?.selectedImg,
          imgUrls: materialData?.imgUrls,
          images: materialData?.images,
          oriPic: materialData?.oriPic,
          finalImageUrls: imageUrls,
        });

        setSelectedImages(imageUrls);

        // 转换为文件列表格式
        const fileList = imageUrls.map((url: string, index: number) => ({
          uid: `${index}`,
          name: `image-${index}.jpg`,
          status: 'done',
          url: url,
          uri: url,
          id: `temp-${index}`,
        }));
        setImageFileList(fileList);
        setLoading(false);
      } else {
        // 玩具类型调用详情接口
        queryMaterialDetail({ id: materialId, sourceType })
          .then((res) => {
            if ((res as any)?.status === 0) {
              const data = (res as any)?.data;
              console.log('材料详情数据:', data); // 调试信息
              setMaterialDetail(data);

              // 初始化选中的图片
              if (data?.selectedImg) {
                // 如果有用户选择的图片，使用用户选择的
                try {
                  let imageUrls: string[] = [];

                  if (typeof data.selectedImg === 'string') {
                    // 如果是字符串，先尝试按逗号分割
                    if (data.selectedImg.includes(',')) {
                      imageUrls = data.selectedImg.split(',').filter((url: string) => url.trim());
                    } else {
                      // 如果没有逗号，尝试 JSON 解析
                      try {
                        const parsed = JSON.parse(data.selectedImg);
                        imageUrls = Array.isArray(parsed) ? parsed : [data.selectedImg];
                      } catch {
                        // JSON 解析失败，当作单个 URL 处理
                        imageUrls = [data.selectedImg];
                      }
                    }
                  } else if (Array.isArray(data.selectedImg)) {
                    imageUrls = data.selectedImg;
                  } else {
                    imageUrls = [];
                  }

                  console.log('用户选择的图片:', imageUrls); // 调试信息
                  setSelectedImages(imageUrls);

                  // 转换为文件列表格式
                  const fileList = imageUrls.map((url: string, index: number) => ({
                    uid: `${index}`,
                    name: `image-${index}.jpg`,
                    status: 'done',
                    url: url,
                    uri: url,
                    id: `temp-${index}`,
                  }));
                  setImageFileList(fileList);
                } catch {
                  setSelectedImages([]);
                  setImageFileList([]);
                }
              } else if (data?.imgUrls) {
                // 如果没有用户选择的图片，默认全选
                console.log('默认全选图片:', data.imgUrls); // 调试信息
                setSelectedImages(data.imgUrls);

                // 转换为文件列表格式
                const fileList = data.imgUrls.map((url: string, index: number) => ({
                  uid: `${index}`,
                  name: `image-${index}.jpg`,
                  status: 'done',
                  url: url,
                  uri: url,
                  id: `temp-${index}`,
                }));
                setImageFileList(fileList);
              } else {
                // 检查其他可能的图片字段
                console.log('查找其他图片字段:', {
                  imgUrls: data?.imgUrls,
                  images: data?.images,
                  pics: data?.pics,
                  oriPic: data?.oriPic,
                });
                setSelectedImages([]);
                setImageFileList([]);
              }
            } else {
              message.error('获取材料详情失败');
            }
          })
          .finally(() => {
            setLoading(false);
          });
      }
    }
  }, [open, materialId, sourceType, initialEditMode, materialData]);

  // 初始化上传客户端
  useEffect(() => {
    if (!client && open) {
      fetchClient(setClient);
    }
  }, [client, open]);

  // 图片上传处理
  const handleImageUpload = async (file: any) => {
    try {
      if (!client) {
        await fetchClient(setClient);
      }
      const resource = await handleUpload(client, file);
      const newImage = {
        uid: resource.id,
        name: resource.filename,
        status: 'done',
        url: resource.uri,
        uri: resource.uri,
        id: resource.id,
      };

      const newFileList = [...imageFileList, newImage];
      setImageFileList(newFileList);

      // 更新选中的图片列表
      const newSelectedImages = newFileList.map((item) => item.url);
      setSelectedImages(newSelectedImages);

      return false; // 阻止默认上传行为
    } catch (error) {
      console.error('图片上传失败:', error);
      message.error('图片上传失败');
      return false;
    }
  };

  // 删除图片
  const handleImageRemove = (file: any) => {
    const newFileList = imageFileList.filter((item) => item.uid !== file.uid);
    setImageFileList(newFileList);

    // 更新选中的图片列表
    const newSelectedImages = newFileList.map((item) => item.url);
    setSelectedImages(newSelectedImages);
  };

  // 图片上移
  const moveImageUp = (index: number) => {
    if (index > 0) {
      const newFileList = [...imageFileList];
      [newFileList[index - 1], newFileList[index]] = [newFileList[index], newFileList[index - 1]];
      setImageFileList(newFileList);

      const newSelectedImages = newFileList.map((item) => item.url);
      setSelectedImages(newSelectedImages);
    }
  };

  // 图片下移
  const moveImageDown = (index: number) => {
    if (index < imageFileList.length - 1) {
      const newFileList = [...imageFileList];
      [newFileList[index], newFileList[index + 1]] = [newFileList[index + 1], newFileList[index]];
      setImageFileList(newFileList);

      const newSelectedImages = newFileList.map((item) => item.url);
      setSelectedImages(newSelectedImages);
    }
  };

  const handleSave = async () => {
    try {
      setLoading(true);

      let response;

      if (sourceType === 'book') {
        // 图书编辑，只传递必要的参数
        const bookParams = {
          id: materialDetail?.id,
          sourceType: materialDetail?.sourceType,
          name: materialDetail?.name,
          selectedImg: selectedImages.join(','),
        };
        response = await editBookBaseInfo(bookParams);
      } else {
        // 玩具编辑，传递完整的参数
        const params = {
          id: materialDetail?.id,
          sourceType: materialDetail?.sourceType,
          baseMaterialId: materialDetail?.baseMaterialId,
          combinedMaterialId: materialDetail?.combinedMaterialId,
          combinedMaterialComponentId: materialDetail?.combinedMaterialComponentId,
          name: materialDetail?.name,
          taobaoName: materialDetail?.taobaoName,
          description: materialDetail?.description,
          area: materialDetail?.area,
          warehouseSn: materialDetail?.warehouseSn,
          warehouseId: materialDetail?.warehouseId,
          isDamaged: materialDetail?.isDamaged,
          buyTime: materialDetail?.buyTime,
          borrowTime: materialDetail?.borrowTime,
          quantity: materialDetail?.quantity,
          isAudited: materialDetail?.isAudited,
          selectedImg: selectedImages.join(','),
          taobaoId: materialDetail?.taobaoId,
          taobaoLink: materialDetail?.taobaoLink,
          category: materialDetail?.category,
          brand: materialDetail?.brand,
          tags: materialDetail?.tags,
          materialDesc: materialDetail?.materialDesc,
          imageText: materialDetail?.imageText,
          materialType: materialDetail?.materialType,
          unit: materialDetail?.unit,
          spec: materialDetail?.spec,
          price: materialDetail?.price,
          taobaoPrice: materialDetail?.taobaoPrice,
          linePrice: materialDetail?.linePrice,
          qualityLevel: materialDetail?.qualityLevel,
          safetyLevel: materialDetail?.safetyLevel,
          weightGram: materialDetail?.weightGram,
          weightScore: materialDetail?.weightScore,
          barcode69: materialDetail?.barcode69,
          oriPic: materialDetail?.oriPic,
          trashImg: materialDetail?.trashImg,
          taobaoJson: materialDetail?.taobaoJson,
        };
        response = await editMaterialBaseInfo(params);
      }

      if ((response as any)?.status === 0) {
        message.success('保存成功');
        setIsEditing(false);
      } else {
        message.error((response as any)?.message || '保存失败');
      }
    } catch (error) {
      console.error('保存失败:', error);
      message.error('保存失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Drawer
      title="材料详情"
      width={800}
      open={open}
      onClose={onClose}
      loading={loading}
      extra={
        <div>
          {activeTab === 'basic' && (
            <Button
              type="primary"
              onClick={() => {
                if (isEditing) {
                  handleSave();
                } else {
                  setIsEditing(true);
                }
              }}
            >
              {isEditing ? '保存' : '编辑'}
            </Button>
          )}
          <Button style={{ marginLeft: 8 }} onClick={onClose}>
            关闭
          </Button>
        </div>
      }
    >
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={
          sourceType === 'book'
            ? [
                // 图书只显示基本信息 tab
                {
                  key: 'basic',
                  label: '基本信息',
                  children: (
                    <BasicInfo
                      materialDetail={materialDetail}
                      setMaterialDetail={setMaterialDetail}
                      isEditing={isEditing}
                      selectedImages={selectedImages}
                      imageFileList={imageFileList}
                      handleImageUpload={handleImageUpload}
                      handleImageRemove={handleImageRemove}
                      moveImageUp={moveImageUp}
                      moveImageDown={moveImageDown}
                      sourceType={sourceType}
                    />
                  ),
                },
              ]
            : [
                // 玩具显示所有 tabs
                {
                  key: 'basic',
                  label: '基本信息',
                  children: (
                    <BasicInfo
                      materialDetail={materialDetail}
                      setMaterialDetail={setMaterialDetail}
                      isEditing={isEditing}
                      selectedImages={selectedImages}
                      imageFileList={imageFileList}
                      handleImageUpload={handleImageUpload}
                      handleImageRemove={handleImageRemove}
                      moveImageUp={moveImageUp}
                      moveImageDown={moveImageDown}
                      sourceType={sourceType}
                    />
                  ),
                },
                {
                  key: 'analysis',
                  label: '材料分析',
                  children: (
                    <MaterialAnalysis materialDetail={materialDetail} materialId={materialId} />
                  ),
                },
                {
                  key: 'existing',
                  label: '已有玩法',
                  children: (
                    <ExistingPlayMethods
                      materialDetail={materialDetail}
                      materialId={materialId}
                      combinedId={materialDetail?.combinedId || materialDetail?.id}
                      classId={userInfo?.currentClassId}
                      schoolId={userInfo?.currentSchoolId}
                    />
                  ),
                },
                {
                  key: 'teacher',
                  label: '老师玩法',
                  children: (
                    <TeacherPlayMethods materialDetail={materialDetail} materialId={materialId} />
                  ),
                },
              ]
        }
      />
    </Drawer>
  );
};

export default MaterialDetailTabs;
