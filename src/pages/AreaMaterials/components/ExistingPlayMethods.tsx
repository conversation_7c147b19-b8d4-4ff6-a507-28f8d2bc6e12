import React, { useEffect, useState } from 'react';
import { List, Card, Spin, Empty, message, Tag, Button, Space, Modal, Form, Input } from 'antd';
import {
  EyeOutlined,
  DeleteOutlined,
  CheckOutlined,
  CloseOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import {
  getMaterialPlayList,
  deletePlay,
  adoptPlay,
  generatePlayList,
} from '@/services/areaMaterials';
import PlayMethodDetailModal from './PlayMethodDetailModal';
import UploadImageComponent from '@/components/UploadImage';
import { fetchClient } from '@/services/fileUpload';

interface ExistingPlayMethodsProps {
  materialDetail: any;
  materialId?: number;
  combinedId?: number;
  classId?: number;
  schoolId?: number;
}

interface PlayMethod {
  id: number;
  playName: string;
  ageMin: number;
  ageMax: number;
  area: string;
  suitablePeopleMin: number;
  suitablePeopleMax: number;
  structured: string;
  playIntro: string;
  combinedId: number;
  [key: string]: any;
}

const ExistingPlayMethods: React.FC<ExistingPlayMethodsProps> = ({
  materialId,
  combinedId,
  classId,
  schoolId,
}) => {
  const [playMethods, setPlayMethods] = useState<PlayMethod[]>([]);
  const [loading, setLoading] = useState(false);
  const [detailModalOpen, setDetailModalOpen] = useState(false);
  const [selectedPlayMethod, setSelectedPlayMethod] = useState<PlayMethod | null>(null);
  const [deleting, setDeleting] = useState(false);
  const [adopting, setAdopting] = useState(false);

  // 新增玩法相关状态
  const [addPlayModalOpen, setAddPlayModalOpen] = useState(false);
  const [addPlayForm] = Form.useForm();
  const [submitting, setSubmitting] = useState(false);
  const [client, setClient] = useState<any>(null);
  const [imageFileList, setImageFileList] = useState<any[]>([]);

  // 获取玩法数据
  const fetchPlayMethods = () => {
    if (materialId) {
      setLoading(true);
      getMaterialPlayList({
        combinedId: materialId,
      })
        .then((res) => {
          if ((res as any)?.status === 0) {
            const data = (res as any)?.data || [];
            setPlayMethods(Array.isArray(data) ? data : []);
          } else {
            message.error('获取已有玩法失败');
            setPlayMethods([]);
          }
        })
        .catch((error) => {
          console.error('获取已有玩法失败:', error);
          message.error('获取已有玩法失败');
          setPlayMethods([]);
        })
        .finally(() => {
          setLoading(false);
        });
    }
  };

  useEffect(() => {
    fetchPlayMethods();
  }, [materialId]);

  // 初始化上传客户端
  useEffect(() => {
    if (!client) {
      fetchClient(setClient);
    }
  }, [client]);

  // 新增玩法处理函数
  const handleAddPlay = () => {
    setAddPlayModalOpen(true);
  };

  // 提交新增玩法
  const handleSubmitAddPlay = async () => {
    try {
      const values = await addPlayForm.validateFields();

      if (!materialId) {
        message.error('材料ID不存在');
        return;
      }

      setSubmitting(true);

      // 获取上传的图片URL
      let imgUrl = '';
      if (imageFileList.length > 0) {
        imgUrl = imageFileList[0].uri || imageFileList[0].url || '';
      }

      const params = {
        combinedId: materialId,
        userPlayStep: values.userPlayStep,
        imgUrl: imgUrl,
      };

      const response = await generatePlayList(params);

      if ((response as any)?.status === 0) {
        message.success('新增玩法成功');
        setAddPlayModalOpen(false);
        addPlayForm.resetFields();
        setImageFileList([]);
        // 重新获取玩法列表
        fetchPlayMethods();
      } else {
        message.error((response as any)?.message || '新增玩法失败');
      }
    } catch (error) {
      console.error('新增玩法失败:', error);
      message.error('新增玩法失败，请重试');
    } finally {
      setSubmitting(false);
    }
  };

  // 取消新增玩法
  const handleCancelAddPlay = () => {
    setAddPlayModalOpen(false);
    addPlayForm.resetFields();
    setImageFileList([]);
  };

  if (loading) {
    return (
      <div style={{ padding: '0 24px' }}>
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: 16,
          }}
        >
          <h3 style={{ margin: 0 }}>已有玩法 (0)</h3>
          <Button type="primary" icon={<PlusOutlined />} onClick={handleAddPlay}>
            新增玩法
          </Button>
        </div>
        <div style={{ padding: '24px', textAlign: 'center' }}>
          <Spin size="large" />
          <p style={{ marginTop: 16 }}>加载已有玩法数据中...</p>
        </div>
      </div>
    );
  }

  const handleView = (item: PlayMethod) => {
    setSelectedPlayMethod(item);
    setDetailModalOpen(true);
  };

  const handleDelete = (item: PlayMethod) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除玩法"${item.playName}"吗？此操作不可恢复。`,
      okText: '确认删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          setDeleting(true);
          const response = await deletePlay({
            playId: item.id,
            combinedId: combinedId || materialId,
            classId: classId,
            schoolId: schoolId,
          });

          if ((response as any)?.status === 0) {
            message.success('删除成功');
            // 重新获取数据
            fetchPlayMethods();
          } else {
            message.error((response as any)?.message || '删除失败');
          }
        } catch (error) {
          console.error('删除失败:', error);
          message.error('删除失败，请重试');
        } finally {
          setDeleting(false);
        }
      },
    });
  };

  const handleAdopt = async (item: PlayMethod, adoptStatus: number) => {
    const actionText = adoptStatus === 1 ? '采用' : '不采用';
    try {
      setAdopting(true);
      const response = await adoptPlay({
        playId: item.id,
        isAdopt: adoptStatus,
        combinedId: combinedId || materialId,
        classId: classId,
        schoolId: schoolId,
      });

      if ((response as any)?.status === 0) {
        message.success(`${actionText}成功`);
        // 重新获取数据
        fetchPlayMethods();
      } else {
        message.error((response as any)?.message || `${actionText}失败`);
      }
    } catch (error) {
      console.error(`${actionText}失败:`, error);
      message.error(`${actionText}失败，请重试`);
    } finally {
      setAdopting(false);
    }
  };

  return (
    <div style={{ padding: '0 24px' }}>
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: 16,
        }}
      >
        <h3 style={{ margin: 0 }}>已有玩法 ({playMethods.length})</h3>
        <Button type="primary" icon={<PlusOutlined />} onClick={handleAddPlay}>
          新增玩法
        </Button>
      </div>

      {playMethods.length === 0 ? (
        <div style={{ padding: '24px', textAlign: 'center' }}>
          <Empty description="暂无已有玩法数据" image={Empty.PRESENTED_IMAGE_SIMPLE} />
        </div>
      ) : (
        <List
          grid={{ gutter: 16, column: 1 }}
          dataSource={playMethods}
          renderItem={(item) => (
            <List.Item>
              <Card
                title={
                  <div
                    style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                    }}
                  >
                    <span>{item.playName || '未命名玩法'}</span>
                    <Space>
                      <Button type="link" icon={<EyeOutlined />} onClick={() => handleView(item)}>
                        查看
                      </Button>
                      {item.playAdopt === 0 && (
                        <>
                          <Button
                            type="link"
                            icon={<CheckOutlined />}
                            loading={adopting}
                            onClick={() => handleAdopt(item, 1)}
                            style={{ color: '#52c41a' }}
                          >
                            采用
                          </Button>
                          <Button
                            type="link"
                            icon={<CloseOutlined />}
                            loading={adopting}
                            onClick={() => handleAdopt(item, -1)}
                            style={{ color: '#ff4d4f' }}
                          >
                            不采用
                          </Button>
                        </>
                      )}
                      <Button
                        type="link"
                        danger
                        icon={<DeleteOutlined />}
                        loading={deleting}
                        onClick={() => handleDelete(item)}
                      >
                        删除
                      </Button>
                    </Space>
                  </div>
                }
                size="small"
              >
                <div style={{ marginBottom: 12 }}>
                  <Space wrap>
                    <Tag color="blue">
                      年龄段: {item.ageMin}-{item.ageMax}岁
                    </Tag>
                    <Tag color="green">投放区域: {item.area || '-'}</Tag>
                    <Tag color="orange">
                      适宜人数: {item.suitablePeopleMin}-{item.suitablePeopleMax}人
                    </Tag>
                    <Tag color="purple">结构化: {item.structured || '-'}</Tag>
                  </Space>
                </div>

                <div>
                  <h4 style={{ marginBottom: 8, fontSize: 14, fontWeight: 'bold' }}>玩法介绍：</h4>
                  <p
                    style={{
                      margin: 0,
                      lineHeight: '1.6',
                      color: '#666',
                      fontSize: 13,
                    }}
                  >
                    {item.playIntro || '暂无介绍'}
                  </p>
                </div>
              </Card>
            </List.Item>
          )}
        />
      )}

      <PlayMethodDetailModal
        open={detailModalOpen}
        onClose={() => {
          setDetailModalOpen(false);
          setSelectedPlayMethod(null);
        }}
        playMethod={selectedPlayMethod}
      />

      {/* 新增玩法弹窗 */}
      <Modal
        title="新增玩法"
        open={addPlayModalOpen}
        onOk={handleSubmitAddPlay}
        onCancel={handleCancelAddPlay}
        confirmLoading={submitting}
        width={600}
        destroyOnClose
      >
        <Form form={addPlayForm} layout="vertical" style={{ marginTop: 16 }}>
          <Form.Item
            name="userPlayStep"
            label="请输入玩法流程"
            rules={[{ required: true, message: '请输入玩法流程' }]}
          >
            <Input.TextArea
              rows={6}
              placeholder="请详细描述玩法流程..."
              maxLength={1000}
              showCount
            />
          </Form.Item>

          <Form.Item label="补充图片">
            <UploadImageComponent
              label=""
              max={1}
              client={client}
              fileList={imageFileList}
              setClient={setClient}
              setFileList={setImageFileList}
              accept=".png,.jpg,.jpeg,.gif"
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ExistingPlayMethods;
