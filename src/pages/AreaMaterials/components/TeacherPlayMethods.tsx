import React, { useEffect, useState } from 'react';
import { List, Card, Spin, Empty, message, Tag, Button, Space, Checkbox } from 'antd';
import { EyeOutlined, ImportOutlined } from '@ant-design/icons';
import { getTeacherMaterialPlayList, saveTeacherPlayToBasePlay } from '@/services/areaMaterials';
import PlayMethodDetailModal from './PlayMethodDetailModal';

interface TeacherPlayMethodsProps {
  materialDetail: any;
  materialId?: number;
}

interface TeacherPlayMethod {
  id: number;
  playName: string;
  ageMin: number;
  ageMax: number;
  area: string;
  suitablePeopleMin: number;
  suitablePeopleMax: number;
  structured: string;
  playIntro: string;
  combinedId: number;
  [key: string]: any;
}

const TeacherPlayMethods: React.FC<TeacherPlayMethodsProps> = ({ materialId }) => {
  const [teacherPlayMethods, setTeacherPlayMethods] = useState<TeacherPlayMethod[]>([]);
  const [loading, setLoading] = useState(false);
  const [detailModalOpen, setDetailModalOpen] = useState(false);
  const [selectedPlayMethod, setSelectedPlayMethod] = useState<TeacherPlayMethod | null>(null);
  const [selectedPlayIds, setSelectedPlayIds] = useState<number[]>([]);
  const [importing, setImporting] = useState(false);

  // 获取老师玩法数据
  const fetchTeacherPlayMethods = () => {
    if (materialId) {
      setLoading(true);
      getTeacherMaterialPlayList({
        combinedId: materialId,
      })
        .then((res) => {
          if ((res as any)?.status === 0) {
            const data = (res as any)?.data || [];
            setTeacherPlayMethods(Array.isArray(data) ? data : []);
          } else {
            message.error('获取老师玩法失败');
            setTeacherPlayMethods([]);
          }
        })
        .catch((error) => {
          console.error('获取老师玩法失败:', error);
          message.error('获取老师玩法失败');
          setTeacherPlayMethods([]);
        })
        .finally(() => {
          setLoading(false);
        });
    }
  };

  useEffect(() => {
    fetchTeacherPlayMethods();
  }, [materialId]);

  if (loading) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <Spin size="large" />
        <p style={{ marginTop: 16 }}>加载老师玩法数据中...</p>
      </div>
    );
  }

  if (teacherPlayMethods.length === 0) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <Empty description="暂无老师玩法数据" image={Empty.PRESENTED_IMAGE_SIMPLE} />
      </div>
    );
  }

  const handleView = (item: TeacherPlayMethod) => {
    setSelectedPlayMethod(item);
    setDetailModalOpen(true);
  };

  // 处理多选
  const handleSelectChange = (playId: number, checked: boolean) => {
    if (checked) {
      setSelectedPlayIds([...selectedPlayIds, playId]);
    } else {
      setSelectedPlayIds(selectedPlayIds.filter((id) => id !== playId));
    }
  };

  // 全选/取消全选
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedPlayIds(teacherPlayMethods.map((item) => item.id));
    } else {
      setSelectedPlayIds([]);
    }
  };

  // 导入选中的玩法
  const handleImport = async () => {
    if (selectedPlayIds.length === 0) {
      message.warning('请先选择要导入的玩法');
      return;
    }

    if (!materialId) {
      message.error('材料ID不存在');
      return;
    }

    try {
      setImporting(true);
      const response = await saveTeacherPlayToBasePlay({
        playIds: selectedPlayIds,
        combinedId: materialId,
      });

      if ((response as any)?.status === 0) {
        message.success('导入成功');
        setSelectedPlayIds([]);
        // 可以选择是否重新获取数据
        // fetchTeacherPlayMethods();
      } else {
        message.error((response as any)?.message || '导入失败');
      }
    } catch (error) {
      console.error('导入失败:', error);
      message.error('导入失败，请重试');
    } finally {
      setImporting(false);
    }
  };

  const isAllSelected =
    teacherPlayMethods.length > 0 && selectedPlayIds.length === teacherPlayMethods.length;
  const isIndeterminate =
    selectedPlayIds.length > 0 && selectedPlayIds.length < teacherPlayMethods.length;

  return (
    <div style={{ padding: '0 24px' }}>
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: 16,
        }}
      >
        <h3 style={{ margin: 0 }}>老师玩法 ({teacherPlayMethods.length})</h3>
        {teacherPlayMethods.length > 0 && (
          <Checkbox
            indeterminate={isIndeterminate}
            checked={isAllSelected}
            onChange={(e) => handleSelectAll(e.target.checked)}
          >
            全选
          </Checkbox>
        )}
      </div>

      <List
        grid={{ gutter: 16, column: 1 }}
        dataSource={teacherPlayMethods}
        renderItem={(item) => (
          <List.Item>
            <Card
              title={
                <div
                  style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}
                >
                  <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
                    <Checkbox
                      checked={selectedPlayIds.includes(item.id)}
                      onChange={(e) => handleSelectChange(item.id, e.target.checked)}
                    />
                    <span>{item.playName || '未命名玩法'}</span>
                  </div>
                  <Button type="link" icon={<EyeOutlined />} onClick={() => handleView(item)}>
                    查看
                  </Button>
                </div>
              }
              size="small"
            >
              <div style={{ marginBottom: 12 }}>
                <Space wrap>
                  <Tag color="blue">
                    年龄段: {item.ageMin}-{item.ageMax}岁
                  </Tag>
                  <Tag color="green">投放区域: {item.area || '-'}</Tag>
                  <Tag color="orange">
                    适宜人数: {item.suitablePeopleMin}-{item.suitablePeopleMax}人
                  </Tag>
                  <Tag color="purple">结构化: {item.structured || '-'}</Tag>
                </Space>
              </div>

              <div>
                <h4 style={{ marginBottom: 8, fontSize: 14, fontWeight: 'bold' }}>玩法介绍：</h4>
                <p
                  style={{
                    margin: 0,
                    lineHeight: '1.6',
                    color: '#666',
                    fontSize: 13,
                  }}
                >
                  {item.playIntro || '暂无介绍'}
                </p>
              </div>
            </Card>
          </List.Item>
        )}
      />

      {selectedPlayIds.length > 0 && (
        <div
          style={{
            position: 'fixed',
            bottom: 24,
            right: 24,
            zIndex: 1000,
            background: '#fff',
            padding: '12px 24px',
            borderRadius: 8,
            boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
            border: '1px solid #d9d9d9',
          }}
        >
          <Space>
            <span>已选择 {selectedPlayIds.length} 个玩法</span>
            <Button
              type="primary"
              icon={<ImportOutlined />}
              loading={importing}
              onClick={handleImport}
            >
              导入玩法
            </Button>
          </Space>
        </div>
      )}

      <PlayMethodDetailModal
        open={detailModalOpen}
        onClose={() => {
          setDetailModalOpen(false);
          setSelectedPlayMethod(null);
        }}
        playMethod={selectedPlayMethod}
      />
    </div>
  );
};

export default TeacherPlayMethods;
