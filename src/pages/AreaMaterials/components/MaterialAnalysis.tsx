import React, { useEffect, useState } from 'react';
import { Descriptions, Input, InputNumber, message, Spin, Button, Empty, Select } from 'antd';
import {
  getMaterialAnalysis,
  generatePlayList,
  listAddAttributesDict,
  updateMaterialAnalysis,
} from '@/services/areaMaterials';

const { TextArea } = Input;

interface MaterialAnalysisProps {
  materialDetail: any;
  materialId?: number;
}

const MaterialAnalysis: React.FC<MaterialAnalysisProps> = ({ materialId }) => {
  const [analysisData, setAnalysisData] = useState<any>({});
  const [loading, setLoading] = useState(false);
  const [generating, setGenerating] = useState(false);
  const [hasData, setHasData] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editData, setEditData] = useState<any>({});
  const [attributesDict, setAttributesDict] = useState<any[]>([]);
  const [saving, setSaving] = useState(false);

  // 获取属性字典数据
  const fetchAttributesDict = async () => {
    try {
      const response = await listAddAttributesDict({});
      if ((response as any)?.status === 0) {
        const data = (response as any)?.data || [];
        setAttributesDict(data);
      } else {
        message.error('获取属性字典失败');
      }
    } catch (error) {
      console.error('获取属性字典失败:', error);
      message.error('获取属性字典失败');
    }
  };

  // 获取材料分析数据
  const fetchAnalysisData = () => {
    if (materialId) {
      setLoading(true);
      getMaterialAnalysis({
        combinedId: materialId,
      })
        .then((res) => {
          if ((res as any)?.status === 0) {
            const data = (res as any)?.data || {};
            setAnalysisData(data);
            setEditData(data); // 初始化编辑数据
            // 检查数据是否为空（判断关键字段是否有值）
            const isEmpty = !data.id && !data.materialSummary && !data.purpose;
            setHasData(!isEmpty);
          } else {
            message.error('获取材料分析失败');
            setHasData(false);
          }
        })
        .catch((error) => {
          console.error('获取材料分析失败:', error);
          message.error('获取材料分析失败');
          setHasData(false);
        })
        .finally(() => {
          setLoading(false);
        });
    }
  };

  useEffect(() => {
    fetchAnalysisData();
    fetchAttributesDict();
  }, [materialId]);

  // 开始编辑
  const handleEdit = () => {
    setIsEditing(true);
    setEditData({ ...analysisData });
  };

  // 取消编辑
  const handleCancel = () => {
    setIsEditing(false);
    setEditData({ ...analysisData });
  };

  // 保存编辑
  const handleSave = async () => {
    try {
      setSaving(true);

      const params = {
        id: editData.id,
        materialId: editData.materialId,
        combinedId: editData.combinedId,
        material_attributes: editData.materialAttributes, // 使用 material_attributes
        attributes_id: editData.attributesId, // 使用 attributes_id
        // 其他字段保持不变
        applicableAreas: editData.applicableAreas,
        materialSummary: editData.materialSummary,
        purpose: editData.purpose,
        productionMethod: editData.productionMethod,
        minAge: editData.minAge,
        maxAge: editData.maxAge,
        suitablePeopleMin: editData.suitablePeopleMin,
        suitablePeopleMax: editData.suitablePeopleMax,
        avgInterest: editData.avgInterest,
        status: editData.status,
        isDeleted: editData.isDeleted,
      };

      console.log('保存参数:', params);

      const response = await updateMaterialAnalysis(params);

      if ((response as any)?.status === 0) {
        message.success('保存成功');
        setIsEditing(false);
        // 重新获取分析数据
        fetchAnalysisData();
      } else {
        message.error((response as any)?.message || '保存失败');
      }
    } catch (error) {
      console.error('保存失败:', error);
      message.error('保存失败');
    } finally {
      setSaving(false);
    }
  };

  // 生成材料分析
  const handleGenerateAnalysis = async () => {
    if (!materialId) {
      message.error('材料ID不存在');
      return;
    }

    try {
      setGenerating(true);
      const response = await generatePlayList({
        combinedId: materialId,
      });

      if ((response as any)?.status === 0) {
        message.success('材料分析生成成功');
        // 重新获取分析数据
        fetchAnalysisData();
      } else {
        message.error((response as any)?.message || '生成材料分析失败');
      }
    } catch (error) {
      console.error('生成材料分析失败:', error);
      message.error('生成材料分析失败');
    } finally {
      setGenerating(false);
    }
  };

  if (loading) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <Spin size="large" />
        <p style={{ marginTop: 16 }}>加载材料分析数据中...</p>
      </div>
    );
  }

  // 如果没有数据，显示生成按钮
  if (!hasData) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <Empty description="暂无材料分析数据" image={Empty.PRESENTED_IMAGE_SIMPLE}>
          <Button type="primary" size="large" loading={generating} onClick={handleGenerateAnalysis}>
            生成材料分析
          </Button>
        </Empty>
      </div>
    );
  }

  return (
    <div style={{ padding: '0 24px' }}>
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: 24,
        }}
      >
        <h3 style={{ margin: 0 }}>材料分析</h3>
        <div style={{ display: 'flex', gap: 8 }}>
          {isEditing ? (
            <>
              <Button onClick={handleCancel}>取消</Button>
              <Button type="primary" loading={saving} onClick={handleSave}>
                保存
              </Button>
            </>
          ) : (
            <>
              <Button onClick={handleEdit}>编辑</Button>
              <Button type="primary" loading={generating} onClick={handleGenerateAnalysis}>
                重新生成分析
              </Button>
            </>
          )}
        </div>
      </div>
      <Descriptions column={1} layout="vertical">
        {/* <Descriptions.Item label="分析ID">
          <Input value={analysisData?.id || '-'} readOnly />
        </Descriptions.Item>

        <Descriptions.Item label="关联材料ID">
          <Input value={analysisData?.materialId || materialDetail?.id || '-'} readOnly />
        </Descriptions.Item>

        <Descriptions.Item label="关联组合材料ID">
          <Input value={analysisData?.combinedId || '-'} readOnly />
        </Descriptions.Item> */}

        <Descriptions.Item label="可投放区域">
          {isEditing ? (
            <Input
              value={editData?.applicableAreas || ''}
              onChange={(e) => setEditData({ ...editData, applicableAreas: e.target.value })}
              placeholder="可投放区域"
            />
          ) : (
            <Input value={analysisData?.applicableAreas || '-'} readOnly />
          )}
        </Descriptions.Item>

        <Descriptions.Item label="材料概述">
          {isEditing ? (
            <TextArea
              value={editData?.materialSummary || ''}
              onChange={(e) => setEditData({ ...editData, materialSummary: e.target.value })}
              rows={4}
              placeholder="材料概述（200字以内）"
            />
          ) : (
            <TextArea
              value={analysisData?.materialSummary || '-'}
              rows={4}
              readOnly
              placeholder="材料概述（200字以内）"
            />
          )}
        </Descriptions.Item>

        <Descriptions.Item label="投放目的">
          {isEditing ? (
            <TextArea
              value={editData?.purpose || ''}
              onChange={(e) => setEditData({ ...editData, purpose: e.target.value })}
              rows={3}
              placeholder="教学目标说明"
            />
          ) : (
            <TextArea
              value={analysisData?.purpose || '-'}
              rows={3}
              readOnly
              placeholder="教学目标说明"
            />
          )}
        </Descriptions.Item>

        <Descriptions.Item label="制作方式">
          {isEditing ? (
            <Input
              value={editData?.productionMethod || ''}
              onChange={(e) => setEditData({ ...editData, productionMethod: e.target.value })}
              placeholder="制作方式"
            />
          ) : (
            <Input value={analysisData?.productionMethod || '-'} readOnly />
          )}
        </Descriptions.Item>

        <Descriptions.Item label="最小适用年龄">
          {isEditing ? (
            <InputNumber
              value={editData?.minAge}
              onChange={(value) => setEditData({ ...editData, minAge: value })}
              style={{ width: '100%' }}
              addonAfter="岁"
              min={0}
              max={18}
            />
          ) : (
            <InputNumber
              value={analysisData?.minAge}
              readOnly
              style={{ width: '100%' }}
              addonAfter="岁"
            />
          )}
        </Descriptions.Item>

        <Descriptions.Item label="最大适用年龄">
          {isEditing ? (
            <InputNumber
              value={editData?.maxAge}
              onChange={(value) => setEditData({ ...editData, maxAge: value })}
              style={{ width: '100%' }}
              addonAfter="岁"
              min={0}
              max={18}
            />
          ) : (
            <InputNumber
              value={analysisData?.maxAge}
              readOnly
              style={{ width: '100%' }}
              addonAfter="岁"
            />
          )}
        </Descriptions.Item>

        <Descriptions.Item label="适宜人数（最小）">
          {isEditing ? (
            <InputNumber
              value={editData?.suitablePeopleMin}
              onChange={(value) => setEditData({ ...editData, suitablePeopleMin: value })}
              style={{ width: '100%' }}
              addonAfter="人"
              min={1}
              max={50}
            />
          ) : (
            <InputNumber
              value={analysisData?.suitablePeopleMin}
              readOnly
              style={{ width: '100%' }}
              addonAfter="人"
            />
          )}
        </Descriptions.Item>

        <Descriptions.Item label="适宜人数（最大）">
          {isEditing ? (
            <InputNumber
              value={editData?.suitablePeopleMax}
              onChange={(value) => setEditData({ ...editData, suitablePeopleMax: value })}
              style={{ width: '100%' }}
              addonAfter="人"
              min={1}
              max={50}
            />
          ) : (
            <InputNumber
              value={analysisData?.suitablePeopleMax}
              readOnly
              style={{ width: '100%' }}
              addonAfter="人"
            />
          )}
        </Descriptions.Item>

        <Descriptions.Item label="材料属性分层">
          {isEditing ? (
            <Select
              mode="multiple"
              value={(() => {
                try {
                  return editData?.materialAttributes
                    ? JSON.parse(editData.materialAttributes)
                    : [];
                } catch {
                  return [];
                }
              })()}
              onChange={(values: string[]) => {
                setEditData({
                  ...editData,
                  materialAttributes: JSON.stringify(values),
                  attributesId: values
                    .map((val: string) => {
                      const attr = attributesDict.find((item) => item.dictItemName === val);
                      return attr?.dictItemCode;
                    })
                    .filter(Boolean)
                    .join(','),
                });
              }}
              placeholder="请选择材料属性分层"
              style={{ width: '100%' }}
              showSearch
              filterOption={(input, option) =>
                (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
              }
              options={attributesDict.map((attr) => ({
                value: attr.dictItemName,
                label: attr.dictItemName,
                key: attr.dictItemCode,
              }))}
            />
          ) : (
            <div>
              {(() => {
                try {
                  const attributes = analysisData?.materialAttributes
                    ? JSON.parse(analysisData.materialAttributes)
                    : [];
                  return attributes.length > 0 ? (
                    <ul style={{ margin: 0, paddingLeft: 20 }}>
                      {attributes.map((attr: string, index: number) => (
                        <li key={index}>{attr}</li>
                      ))}
                    </ul>
                  ) : (
                    '-'
                  );
                } catch {
                  return analysisData?.materialAttributes || '-';
                }
              })()}
            </div>
          )}
        </Descriptions.Item>

        {/* <Descriptions.Item label="材料属性分层编号">
          <Input value={analysisData?.materialAttributesId || '-'} readOnly />
        </Descriptions.Item> */}

        <Descriptions.Item label="幼儿兴趣均值">
          {isEditing ? (
            <InputNumber
              value={editData?.avgInterest}
              onChange={(value) => setEditData({ ...editData, avgInterest: value })}
              style={{ width: '100%' }}
              precision={2}
              min={0}
              max={10}
              step={0.1}
            />
          ) : (
            <InputNumber
              value={analysisData?.avgInterest}
              readOnly
              style={{ width: '100%' }}
              precision={2}
            />
          )}
        </Descriptions.Item>

        {/* <Descriptions.Item label="材料属性编号">
          <Input value={analysisData?.attributesId || '-'} readOnly />
        </Descriptions.Item> */}

        <Descriptions.Item label="状态">
          <Input
            value={analysisData?.status === 0 ? '正常' : analysisData?.status || '-'}
            readOnly
          />
        </Descriptions.Item>

        <Descriptions.Item label="是否删除">
          <Input value={analysisData?.isDeleted === 1 ? '已删除' : '正常'} readOnly />
        </Descriptions.Item>

        <Descriptions.Item label="创建时间">
          <Input value={analysisData?.createTime || '-'} readOnly />
        </Descriptions.Item>

        <Descriptions.Item label="更新时间">
          <Input value={analysisData?.updateTime || '-'} readOnly />
        </Descriptions.Item>
      </Descriptions>
    </div>
  );
};

export default MaterialAnalysis;
