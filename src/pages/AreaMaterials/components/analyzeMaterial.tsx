import { Button, Form, Input, Modal, Radio, Select, Checkbox, Image, message, Upload } from 'antd';
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons';
import React, { useState, useEffect } from 'react';
import {
  getMaterialByTaobaoUrl,
  saveMaterialInfo,
  generatePlayList,
} from '@/services/areaMaterials';
import { fetchUserSelectedSchoolsAndClasses } from '@/services/api';
import { fetchClient, handleUpload } from '@/services/fileUpload';
import { useModel } from '@umijs/max';
import { DictionaryCategory } from '@/services/constants';
import { isEmpty } from 'lodash';
interface AnalyzeMaterialProps {
  open: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

const AnalyzeMaterial: React.FC<AnalyzeMaterialProps> = ({ open, onClose, onSuccess }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [addMethod, setAddMethod] = useState('taobao'); // 默认淘宝链接
  const [materialType, setMaterialType] = useState('toy'); // 材料类型
  const [materialData, setMaterialData] = useState<any>(null); // 存储获取到的材料数据
  const [areas, setAreas] = useState<any>([]);
  const [selectedImages, setSelectedImages] = useState<string[]>([]);
  const [imageFileList, setImageFileList] = useState<any[]>([]); // 上传的图片列表
  const [userInfo, setUserInfo] = useState<any>(null); // 用户选择的学校和班级信息

  const { initialState } = useModel('@@initialState');
  const { dictionaryList } = initialState || {};

  // 获取区域数据
  useEffect(() => {
    if (!isEmpty(dictionaryList)) {
      const result: any = dictionaryList?.map((item) => {
        return {
          value: item?.id,
          label: item?.value,
          category: item?.category,
        };
      });
      const _areas = result.filter((item: any) => item.category === DictionaryCategory.Area);
      setAreas(_areas);
    }
  }, [dictionaryList]);

  // 获取用户选择的学校和班级信息
  useEffect(() => {
    fetchUserSelectedSchoolsAndClasses()
      .then((res) => {
        if (res?.status === 0) {
          setUserInfo(res.data);
        }
      })
      .catch((error) => {
        console.error('获取用户信息失败:', error);
      });
  }, []);

  // 图片上传处理
  const handleImageUpload = async (file: any) => {
    try {
      let client = null;
      await fetchClient((c: any) => {
        client = c;
      });

      const resource = await handleUpload(client, file);
      const newImage = {
        uid: resource.id,
        name: resource.filename,
        status: 'done',
        url: resource.uri,
        uri: resource.uri,
        id: resource.id,
      };

      setImageFileList((prev) => [...prev, newImage]);
      return false; // 阻止默认上传行为
    } catch (error) {
      console.error('图片上传失败:', error);
      message.error('图片上传失败');
      return false;
    }
  };

  // 删除图片
  const handleImageRemove = (file: any) => {
    setImageFileList((prev) => prev.filter((item) => item.uid !== file.uid));
  };

  // 获取材料信息
  const handleGetMaterialInfo = async () => {
    try {
      const values = await form.validateFields(['taobaoLink']);
      setLoading(true);

      const response = await getMaterialByTaobaoUrl({
        url: values.taobaoLink,
      });

      if ((response as any)?.status === 0) {
        const data = (response as any).data;
        setMaterialData(data);

        // 默认全选图片
        if (data.imgUrls && data.imgUrls.length > 0) {
          setSelectedImages([...data.imgUrls]); // 确保是新数组，默认全选
        }

        // 自动填充表单
        form.setFieldsValue({
          name: data.name,
          brand: data.brand,
          selectedImages: data.imgUrls || [], // 同步表单字段
        });

        message.success('材料信息获取成功');
      } else {
        message.error((response as any)?.message || '获取材料信息失败');
      }
    } catch (error) {
      console.error('获取材料信息失败:', error);
      message.error('获取材料信息失败');
    } finally {
      setLoading(false);
    }
  };

  // AI分析/保存
  const handleAIAnalyze = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      // 获取选中的区域信息
      const selectedArea: any = areas.find((area: any) => area.value === values.areaId);

      let params: any = {
        sourceType: addMethod === 'taobao' ? 'toy' : materialType, // 淘宝链接默认toy，自行编辑使用选择的类型
        name: values.name,
        area: selectedArea?.label || '',
        areaId: values.areaId,
        brand: values.brand || '',
        taobaoLink: values.taobaoLink || '',
      };

      if (addMethod === 'taobao') {
        // 淘宝链接方式
        if (!materialData) {
          message.error('请先获取材料信息');
          return;
        }
        params = {
          ...params,
          baseMaterialId: materialData.baseMaterialId,
          combinedMaterialId: materialData.combinedMaterialId,
          combinedMaterialComponentId: materialData.combinedMaterialComponentId,
          taobaoName: materialData.taobaoName,
          selectedImg: selectedImages.join(','),
          taobaoId: materialData.taobaoId,
          linePrice: materialData.linePrice,
          taobaoPrice: materialData.taobaoPrice,
        };
      } else {
        // 自行编辑方式
        const uploadedImages = imageFileList.map((file) => file.uri || file.url).join(',');
        params = {
          ...params,
          baseMaterialId: null,
          combinedMaterialId: null,
          combinedMaterialComponentId: null,
          selectedImg: uploadedImages,
          trashImg: '',
          classId: userInfo?.currentClassId || null,
          schoolId: userInfo?.currentSchoolId || null,
        };
      }

      const response = await saveMaterialInfo(params);

      if ((response as any)?.status === 0) {
        // 只有玩具类或淘宝链接方式才调用 generatePlayList
        if (addMethod === 'taobao' || (addMethod === 'manual' && materialType === 'toy')) {
          await generatePlayList({
            combinedId: (response as any).data.combinedId,
          });
        }
        message.success(addMethod === 'taobao' ? 'AI分析中...' : '保存成功');
        onSuccess?.();
        onClose();
        form.resetFields();
        setAddMethod('taobao');
        setMaterialType('toy');
        setMaterialData(null);
        setSelectedImages([]);
        setImageFileList([]);
      } else {
        message.error(
          (response as any)?.message || (addMethod === 'taobao' ? 'AI分析失败' : '保存失败'),
        );
      }
    } catch (error) {
      console.error(addMethod === 'taobao' ? 'AI分析失败:' : '保存失败:', error);
      message.error(addMethod === 'taobao' ? 'AI分析失败' : '保存失败');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    setAddMethod('taobao');
    setMaterialType('toy');
    setMaterialData(null);
    setSelectedImages([]);
    setImageFileList([]);
    onClose();
  };

  return (
    <Modal
      title="分析材料"
      open={open}
      onCancel={handleCancel}
      footer={null}
      width={600}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          addMethod: 'taobao',
          materialType: 'toy',
        }}
      >
        <Form.Item
          label="添加方式"
          name="addMethod"
          rules={[{ required: true, message: '请选择添加方式' }]}
        >
          <Radio.Group value={addMethod} onChange={(e) => setAddMethod(e.target.value)}>
            <Radio value="taobao">淘宝链接</Radio>
            <Radio value="manual">自行编辑</Radio>
          </Radio.Group>
        </Form.Item>

        {addMethod === 'manual' && (
          <Form.Item
            label="材料类型"
            name="materialType"
            rules={[{ required: true, message: '请选择材料类型' }]}
          >
            <Radio.Group value={materialType} onChange={(e) => setMaterialType(e.target.value)}>
              <Radio value="toy">玩具类</Radio>
              <Radio value="book">图书类</Radio>
            </Radio.Group>
          </Form.Item>
        )}

        {addMethod === 'taobao' && (
          <>
            <Form.Item
              label="淘宝链接"
              name="taobaoLink"
              rules={[{ required: true, message: '请输入淘宝链接' }]}
            >
              <Input.TextArea placeholder="请输入淘宝商品链接" rows={3} />
            </Form.Item>

            <Form.Item style={{ textAlign: 'center' }}>
              <Button type="primary" loading={loading} onClick={handleGetMaterialInfo}>
                获取材料信息
              </Button>
            </Form.Item>

            {materialData && (
              <>
                <Form.Item
                  label="区域选择"
                  name="areaId"
                  rules={[{ required: true, message: '请选择区域' }]}
                >
                  <Select placeholder="请选择区域" options={areas} />
                </Form.Item>

                <Form.Item
                  label="商品名称"
                  name="name"
                  rules={[{ required: true, message: '请输入商品名称' }]}
                >
                  <Input placeholder="请输入商品名称" />
                </Form.Item>

                <Form.Item label="材料品牌" name="brand">
                  <Input placeholder="请输入材料品牌" />
                </Form.Item>

                <Form.Item label="材料图片" name="selectedImages">
                  <Checkbox.Group
                    value={selectedImages}
                    onChange={setSelectedImages}
                    style={{ width: '100%' }}
                  >
                    <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px' }}>
                      {materialData.imgUrls?.map((url: string, index: number) => (
                        <div key={index} style={{ textAlign: 'center' }}>
                          <Image
                            src={url}
                            width={80}
                            height={80}
                            style={{ objectFit: 'cover' }}
                            preview={false}
                          />
                          <br />
                          <Checkbox value={url} />
                        </div>
                      ))}
                    </div>
                  </Checkbox.Group>
                </Form.Item>
              </>
            )}
          </>
        )}

        {addMethod === 'manual' && (
          <>
            <Form.Item
              label="区域选择"
              name="areaId"
              rules={[{ required: true, message: '请选择区域' }]}
            >
              <Select placeholder="请选择区域" options={areas} />
            </Form.Item>

            {materialType === 'toy' && (
              <>
                <Form.Item
                  label="商品名称"
                  name="name"
                  rules={[{ required: true, message: '请输入商品名称' }]}
                >
                  <Input placeholder="请输入商品名称" />
                </Form.Item>

                <Form.Item label="材料品牌" name="brand">
                  <Input placeholder="请输入材料品牌" />
                </Form.Item>

                <Form.Item label="淘宝链接" name="taobaoLink">
                  <Input.TextArea placeholder="请输入淘宝商品链接" rows={3} />
                </Form.Item>

                <Form.Item label="材料图片上传">
                  <div>
                    <Upload
                      listType="picture-card"
                      fileList={imageFileList}
                      beforeUpload={handleImageUpload}
                      onRemove={handleImageRemove}
                      accept="image/*"
                      multiple
                    >
                      {imageFileList.length >= 10 ? null : (
                        <div>
                          <PlusOutlined />
                          <div style={{ marginTop: 8 }}>上传图片</div>
                        </div>
                      )}
                    </Upload>

                    {imageFileList.length > 0 && (
                      <div style={{ marginTop: 16 }}>
                        <p style={{ marginBottom: 16, fontWeight: 'bold' }}>已上传图片：</p>
                        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '16px' }}>
                          {imageFileList.map((file, index) => (
                            <div
                              key={file.uid}
                              style={{
                                position: 'relative',
                                border: '1px solid #d9d9d9',
                                borderRadius: '8px',
                                padding: '8px',
                                textAlign: 'center',
                              }}
                            >
                              <Image
                                src={file.url}
                                width={80}
                                height={80}
                                style={{ objectFit: 'cover' }}
                                preview={false}
                              />
                              <div
                                style={{
                                  marginTop: 8,
                                  display: 'flex',
                                  justifyContent: 'center',
                                  gap: '4px',
                                }}
                              >
                                <Button
                                  size="small"
                                  icon={<DeleteOutlined />}
                                  danger
                                  onClick={() => handleImageRemove(file)}
                                />
                              </div>
                              <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
                                {index + 1}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </Form.Item>
              </>
            )}

            {materialType === 'book' && (
              <>
                <Form.Item
                  label="书籍名称"
                  name="name"
                  rules={[{ required: true, message: '请输入书籍名称' }]}
                >
                  <Input placeholder="请输入书籍名称" />
                </Form.Item>

                <Form.Item label="书籍图片上传">
                  <div>
                    <Upload
                      listType="picture-card"
                      fileList={imageFileList}
                      beforeUpload={handleImageUpload}
                      onRemove={handleImageRemove}
                      accept="image/*"
                      multiple
                    >
                      {imageFileList.length >= 10 ? null : (
                        <div>
                          <PlusOutlined />
                          <div style={{ marginTop: 8 }}>上传图片</div>
                        </div>
                      )}
                    </Upload>

                    {imageFileList.length > 0 && (
                      <div style={{ marginTop: 16 }}>
                        <p style={{ marginBottom: 16, fontWeight: 'bold' }}>已上传图片：</p>
                        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '16px' }}>
                          {imageFileList.map((file, index) => (
                            <div
                              key={file.uid}
                              style={{
                                position: 'relative',
                                border: '1px solid #d9d9d9',
                                borderRadius: '8px',
                                padding: '8px',
                                textAlign: 'center',
                              }}
                            >
                              <Image
                                src={file.url}
                                width={80}
                                height={80}
                                style={{ objectFit: 'cover' }}
                                preview={false}
                              />
                              <div
                                style={{
                                  marginTop: 8,
                                  display: 'flex',
                                  justifyContent: 'center',
                                  gap: '4px',
                                }}
                              >
                                <Button
                                  size="small"
                                  icon={<DeleteOutlined />}
                                  danger
                                  onClick={() => handleImageRemove(file)}
                                />
                              </div>
                              <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
                                {index + 1}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </Form.Item>
              </>
            )}
          </>
        )}

        <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
          <Button style={{ marginRight: 8 }} onClick={handleCancel}>
            取消
          </Button>
          {addMethod === 'taobao' && materialData ? (
            <Button type="primary" loading={loading} onClick={handleAIAnalyze}>
              AI分析
            </Button>
          ) : addMethod === 'manual' ? (
            <Button type="primary" loading={loading} onClick={handleAIAnalyze}>
              保存
            </Button>
          ) : null}
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default AnalyzeMaterial;
