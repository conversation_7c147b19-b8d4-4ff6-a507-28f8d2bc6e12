/* eslint-disable guard-for-in */
import { ProTable } from '@ant-design/pro-components';
import { Card, Descriptions, Image, ConfigProvider } from 'antd';

import { get } from 'lodash';
import './index.less';

const { Meta } = Card;

export const FormItem = ({
  title,
  lists,
  data,
}: {
  lists: {
    label: string;
    dataIndex: string | string[];
    render?: (data: Record<string, any>, key: string | string[]) => string;
    span?: number;
    options?: Record<string, any>[];
    type?: 'text' | 'select' | 'file' | 'protable';
    columns?: Record<string, any>[];
  }[];
  data: Record<string, any>;
  title?: string;
}) => {
  return (
    <Descriptions
      title={title || ''}
      style={{
        marginBottom: 32,
      }}
      className="descriptionDetail"
    >
      {lists?.map((list) => {
        const { dataIndex, span, options, type, columns } = list;
        let value = get(data, dataIndex);
        const key = Array.isArray(dataIndex) ? dataIndex.join(',') : dataIndex;
        if (type === 'file') {
          return (
            <Descriptions.Item className="descriptionsCard" key={key} span={4} label={list.label}>
              {value?.length ? (
                <Image.PreviewGroup
                  preview={{
                    onChange: (current, prev) =>
                      console.log(`current index: ${current}, prev index: ${prev}`),
                  }}
                >
                  {value?.map((item, index) => {
                    return (
                      <Card
                        key={index}
                        hoverable
                        // className='descriptionsCard'
                        style={{ width: 200, marginRight: 10, marginBottom: 10 }}
                        cover={
                          <div className="descriptionsImage">
                            <Image
                              style={{
                                maxHeight: 140,
                                maxWidth: 200,
                                height: '100%',
                                width: 'auto',
                              }}
                              src={item.uri}
                              fallback={
                                'https://pace-toy-store.oss-cn-shenzhen.aliyuncs.com/df/02/4d151d7d5a3ca1684c02736cbc8b02df.png'
                              }
                            />
                          </div>
                        }
                      >
                        <Meta title="" description={item.filename} />
                      </Card>
                    );
                  })}
                </Image.PreviewGroup>
              ) : (
                <div style={{ color: '#9e9b9b' }} className="descriptionsTips">
                  暂未上传
                </div>
              )}
            </Descriptions.Item>
          );
        }
        if (type === 'protable') {
          return (
            <Descriptions.Item key={key} span={4} label={list.label}>
              <ConfigProvider
                renderEmpty={() => {
                  return <div>暂无数据</div>;
                }}
              >
                <ProTable<API.RuleListItem, API.PageParams>
                  rowKey="id"
                  search={false}
                  pagination={false}
                  dataSource={value || []}
                  options={false}
                  columns={columns}
                />
              </ConfigProvider>
            </Descriptions.Item>
          );
        }
        if (options?.length && type === 'select') {
          value = options?.find((item) => item.value === get(data, dataIndex))?.label;
        } else if (list?.render) {
          value = list?.render(data, dataIndex);
        }
        return (
          <Descriptions.Item key={key} span={span || 1} label={list.label}>
            {value || '-'}
          </Descriptions.Item>
        );
      })}
    </Descriptions>
  );
};
