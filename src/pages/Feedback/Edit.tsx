import AntForms, { FormFields, FormTypes } from '@/components/UseForms';
import { addFeedback, getFeedbackDetail, updateFeedback } from '@/services/feedback';
import { filterEmptyObject } from '@/services/utils';
import { PageContainer } from '@ant-design/pro-components';
import { history, useParams } from '@umijs/max';
import { Button, message, Row } from 'antd';
import { useForm } from 'antd/es/form/Form';
import { useCallback, useEffect, useState } from 'react';

const ChildDetail: React.FC = () => {
  const params = useParams();
  const { id } = params || {};
  const [_, setDetail] = useState({});
  const [form] = useForm();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (id) {
      // 获取详情
      getFeedbackDetail({ id }).then((res) => {
        setDetail(res.data || {});
        form.setFieldsValue({
          ...res.data,
        });
      });
    }
  }, []);

  const childForms: FormFields = [
    {
      label: '标题',
      name: 'title',
      formType: FormTypes.input,
      rules: [
        {
          message: '请输入反馈标题',
          required: true,
        },
      ],
    },
    {
      label: '反馈内容',
      name: 'content',
      width: 1000,
      rules: [
        {
          message: '请输入反馈内容',
          required: true,
        },
      ],
      formType: FormTypes.textarea,
    },
    {
      label: '照片',
      name: 'imageIds',
      fileKey: 'imageIds',
      // rules: [
      //   {
      //     message: '请输入反馈内容',
      //     required: true,
      //   },
      // ],
      formType: FormTypes.file,
    },
  ];

  const onUpdateChild = useCallback(
    (values: any) => {
      const fn = id ? updateFeedback : addFeedback;
      setLoading(true);
      const {title, content, imageIds}=values;
      fn(
        filterEmptyObject({
          title,
          content,
          imageIds: imageIds?.map(item => item.id),
          id,
        }),
      )
        .then((r) => {
          if (r.status === 0) {
            message.success(id ? '更新成功' : '添加成功');
            if (r?.data?.id) {
              history.replace('/feedback/detail/' + r.data.id );
            }
          }
        })
        .finally(() => {
          setLoading(false);
        });
    },
    [id],
  );

  return (
    <PageContainer style={{ background: '#fff', height: '100%' }}>
      <AntForms
        form={form}
        formProps={{
          onFinish: onUpdateChild,
        }}
        forms={childForms}
      >
        <Row justify="end">
          <Button loading={loading} style={{ marginRight: 16 }} htmlType="submit" type="primary">
            确定
          </Button>
        </Row>
      </AntForms>
    </PageContainer>
  );
};

export default ChildDetail;
