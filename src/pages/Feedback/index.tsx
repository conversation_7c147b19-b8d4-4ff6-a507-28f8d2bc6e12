/* eslint-disable guard-for-in */

import { AntFormsI<PERSON>, FormFields, FormTypes } from '@/components/UseForms';
import {
  addFeedback,
  fetchEnum,
  getFeedbackDetail,
  getFeedbackList,
  updateFeedback,
} from '@/services/api';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ModalForm, PageContainer, ProTable } from '@ant-design/pro-components';
import { Button, Form, Image, message } from 'antd';
import { keyBy } from 'lodash';
import React, { useEffect, useRef, useState } from 'react';

const EditModal = (props: {
  data?: Record<string, any>;
  onRefresh: () => void;
  options: Record<string, any>;
  type: string;
}) => {
  const { data, options, type } = props;
  const [detail, setDetail] = useState({});
  const getTitle = (mode?: string) => {
    if (data?.id) {
      return (type && '详情') || (mode === 'button' && '编辑') || `编辑反馈`;
    }
    return (type && '详情') || (mode === 'button' && '新增') || `新增反馈`;
  };

  const onSubmit = async (value) => {
    try {
      let res;
      if (data?.id) {
        res = await updateFeedback({
          ...value,
          categoryId: value.categoryId || null,
          urgency: value.urgency || null,
          state: value.state || null,
          id: data.id,
        });
      } else {
        res = await addFeedback({
          ...value,
        });
      }

      if (res?.status === 0) {
        message.success('更新成功');
        // getList();
        props?.onRefresh();
        return true;
      } else {
        message.success('更新失败');
        return false;
      }
    } catch (error) {
      message.error(error?.message || '更新失败，请重试');
      return false;
    }
  };
  const [form] = Form.useForm<{ name: string; company: string }>();

  const childForms: FormFields = [
    {
      label: '标题',
      name: 'title',
      formType: FormTypes.input,
      rules: [
        {
          message: '请输入反馈标题',
          required: true,
        },
      ],
      disabled: true,
    },
    {
      label: '反馈内容',
      name: 'content',
      width: 1000,
      rules: [
        {
          message: '请输入反馈内容',
          required: true,
        },
      ],
      formType: FormTypes.textarea,
      disabled: true,
    },
    {
      label: '照片',
      name: 'imageIds',
      formType: FormTypes.render,
      disabled: true,
      render: () => {
        return detail?.images?.map((item) => {
          return (
            <Image
              alt={item.filename}
              style={{ width: '200px', margin: '0px 5px 5px' }}
              src={item?.uri || ''}
            />
          );
        });
      },
    },
    {
      label: '分类',
      name: 'categoryId',
      formType: FormTypes.select,
      disabled: type ? true : false,
      options: options.FeedbackCategoryEnum,
    },
    {
      label: '紧急程度',
      name: 'urgency',
      formType: FormTypes.select,
      disabled: type ? true : false,
      options: options.FeedbackUrgencyEnum,
    },
    {
      label: '状态',
      name: 'state',
      formType: FormTypes.select,
      disabled: type ? true : false,
      options: options.FeedbackStateEnum,
    },

    {
      label: '备注',
      name: 'note',
      disabled: type ? true : false,
      formType: FormTypes.textarea,
    },
  ];
  return (
    <ModalForm<{
      title: string;
      content: string;
      note: string;
      state: string;
    }>
      clearOnDestroy
      title={getTitle()}
      trigger={
        <Button size={'small'} type={(type && 'link') || (data?.id ? 'default' : 'primary')}>
          {getTitle('button')}
        </Button>
      }
      form={form}
      onOpenChange={(open) => {
        if (open) {
          if (data?.id) {
            getFeedbackDetail({
              id: data?.id,
            }).then((res) => {
              form.setFieldsValue(res.data);
              setDetail(res.data);
            });
          }
        }
      }}
      autoFocusFirstInput
      modalProps={{
        destroyOnClose: true,
        onCancel: () => console.log('run'),
      }}
      submitTimeout={2000}
      initialValues={data}
      onFinish={async (values) => {
        return onSubmit(values);
      }}
    >
      <AntFormsItem readonly={type ? true : false} form={form} forms={childForms} />
    </ModalForm>
  );
};

const Feedback: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const [options, setOptions] = useState({
    FeedbackCategoryEnum: [],
    FeedbackStateEnum: [],
    FeedbackUrgencyEnum: [],
  });

  useEffect(() => {
    fetchEnum().then((res) => {
      const { FeedbackCategoryEnum, FeedbackStateEnum, FeedbackUrgencyEnum } = res?.data;
      const handleOptions = (result) => {
        return Object.keys(result)?.map((item) => {
          const _label = result[item];
          const label = typeof _label === 'string' ? _label : _label.adminDesc;
          return {
            label: label,
            text: label,
            value: parseInt(item),
          };
        });
      };
      const _options = {
        FeedbackCategoryEnum: handleOptions(FeedbackCategoryEnum),
        FeedbackStateEnum: handleOptions(FeedbackStateEnum),
        FeedbackUrgencyEnum: handleOptions(FeedbackUrgencyEnum),
      };
      setOptions(_options);
    });
  }, []);
  const columns: ProColumns<{
    id: string;
    title: string;
    content: string;
  }>[] = [
    {
      title: '编号',
      dataIndex: 'id',
      valueType: 'text',
      width: 60,
      search: false,
    },
    {
      title: '标题',
      dataIndex: 'title',
      valueType: 'text',
      width: 80,
    },
    {
      title: '内容',
      dataIndex: 'content',
      valueType: 'text',
      width: 220,
      search: false,
    },
    {
      title: '状态',
      dataIndex: 'state',
      valueType: 'select',
      width: 80,
      // search: false,
      valueEnum: keyBy(options.FeedbackStateEnum, 'value'),
      initialValue: '1',
    },
    {
      title: '分类',
      dataIndex: 'categoryId',
      valueType: 'select',
      width: 80,
      // search: false,
      initialValue: '1',
      valueEnum: keyBy(options.FeedbackCategoryEnum, 'value'),
    },
    {
      title: '紧急程度',
      dataIndex: 'urgency',
      valueType: 'select',
      width: 80,
      // search: false,
      valueEnum: keyBy(options.FeedbackUrgencyEnum, 'value'),
    },
    {
      title: '用户名',
      dataIndex: 'userName',
      valueType: 'text',
      search: false,
      width: 80,
      // formType: FormTypes.textarea,
    },
    {
      title: '学校',
      dataIndex: 'schoolTitle',
      valueType: 'text',
      search: false,
      width: 80,
    },
    {
      title: '反馈时间',
      dataIndex: 'createdAt',
      valueType: 'text',
      search: false,
      width: 80,
      // formType: FormTypes.input,
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      valueType: 'option',
      width: '8em',
      search: false,
      render: (_, record) => [
        // @ts-ignore
        <EditModal data={record} options={options} onRefresh={actionRef.current?.reload} />,
        <EditModal
          data={record}
          type={'detail'}
          options={options}
          onRefresh={actionRef.current?.reload}
        />,
      ],
    },
  ];

  return (
    <PageContainer>
      <ProTable<API.RuleListItem, API.PageParams>
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 'auto',
          defaultCollapsed: false,
          collapsed: false,
          collapseRender: false,
        }}
        toolBarRender={false}
        // toolBarRender={() => [
        //   // @ts-ignore
        //   <EditModal onRefresh={actionRef.current?.reload} />,
        //   // <Button size="small" type="link" key="detail" href={`/feedback/add`} target="_blank">
        //   //   <PlusOutlined /> 新建
        //   // </Button>,
        // ]}
        request={getFeedbackList}
        columns={columns}
      />
    </PageContainer>
  );
};

export default Feedback;
