.descriptionDetail {
  .ant-table-cell {
    font-size: 13px !important;
  }
  // .ant-image {
  //   width: 96px !important;
  // }
}
.descriptionsCard {
  .ant-card-body {
    padding: 10px;
    border-top: 1px dashed #ccc;
  }
  .ant-card-meta-description {
    word-wrap: break-word;
    word-break: break-all;
    max-height: 66px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }
  .ant-descriptions-item-content {
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    align-items: center !important;
  }
}
.descriptionsImage {
  width: 100%;
  height: 160px;
  padding: 10px 0;

  .ant-image-error {
    img {
      width: 40px !important;
      height: auto !important;
      opacity: 0.4;
      height: auto;
    }
  }
  .ant-image {
    display: flex;
    justify-content: center !important;
    align-items: center !important;
    height: 100%;
  }

  img {
    width: 100%;
    max-height: 140px;
  }
}
.descriptionsModal {
  position: relative;

  .anticon-info-circle {
    display: none;
  }

  .ant-modal-confirm-btns,
  .descriptionsModalClose {
    position: absolute;
    top: 8px;
    right: 0;
    height: 42px;
    width: 100px;
    display: flex;
    justify-content: center;
    align-items: center;

    .ant-btn {
      background: transparent;
      box-shadow: none;
      margin-top: 0;
      &:active,
      &:hover {
        background-color: transparent;
      }
      &:focus-visible {
        outline: none;
      }
    }
    span {
      color: transparent;
    }
  }
}
