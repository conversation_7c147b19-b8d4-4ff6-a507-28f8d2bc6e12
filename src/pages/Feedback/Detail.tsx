import { FormFields, FormTypes } from '@/components/UseForms';
import { getFeedbackDetail } from '@/services/feedback';
import { PageContainer } from '@ant-design/pro-components';
import { useParams } from '@umijs/max';
import { useForm } from 'antd/es/form/Form';
import { useEffect, useState } from 'react';
import { FormItem } from './FormItemDetail';

const ChildDetail: React.FC = () => {
  const params = useParams();
  const { id } = params || {};
  const [detail, setDetail] = useState({});
  const [form] = useForm();

  useEffect(() => {
    if (id) {
      // 获取详情
      getFeedbackDetail({ id }).then((res) => {
        setDetail(res.data || {});
        form.setFieldsValue({
          ...res.data,
        });
      });
    }
  }, []);


  return (
    <PageContainer style={{ background: '#fff', minHeight: '400px' }}>
      <FormItem
        data={detail}
        lists={[
          {
            dataIndex: 'title',
            label: '标题',
            span: 4,
          },
          {
            dataIndex: '链接',
            label: 'link',
            span: 4,
          },
          {
            dataIndex: 'content',
            label: '内容',
            span: 4,
          },
        ]}
      />
    </PageContainer>
  );
};

export default ChildDetail;
