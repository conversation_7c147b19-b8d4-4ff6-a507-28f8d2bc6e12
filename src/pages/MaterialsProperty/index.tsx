import { <PERSON><PERSON><PERSON>r, ModalForm, ProFormText, ProFormSelect } from '@ant-design/pro-components';
import { Button, message, Tree, Card, Space, Tag, Row, Col, Spin, Tooltip, Modal } from 'antd';
import React, { useState, useEffect } from 'react';
import {
  listAddAttributes,
  addAttributes,
  editAttributes,
  deleteAttributes,
} from '@/services/areaMaterials';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  FolderOutlined,
  FileOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import type { DataNode } from 'antd/es/tree';

interface TreeNodeData {
  id: number;
  materialName: string;
  parentId: number;
  depth: number;
  fullPath: string;
  fullPathId: string;
  hasChild: number;
  childList?: TreeNodeData[];
  createTime?: string;
  updateTime?: string;
  isDeleted?: number;
}

interface ParentOption {
  value: number;
  label: string;
}

const MaterialsPropertyList: React.FC = () => {
  const [treeData, setTreeData] = useState<any[]>([]);
  const [antdTreeData, setAntdTreeData] = useState<DataNode[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalOpen, setModalOpen] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [currentNode, setCurrentNode] = useState<any>({});
  const [selectedNode, setSelectedNode] = useState<TreeNodeData | null>(null);
  const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [parentOptions, setParentOptions] = useState<ParentOption[]>([]);

  // 生成父级选项（扁平化树形数据）
  const generateParentOptions = (data: TreeNodeData[]): void => {
    const options: ParentOption[] = [{ value: 0, label: '顶级分类' }];

    const flatten = (nodes: TreeNodeData[]): void => {
      nodes.forEach((node) => {
        options.push({
          value: node.id,
          label: node.fullPath,
        });

        if (node.childList && node.childList.length > 0) {
          flatten(node.childList);
        }
      });
    };

    flatten(data);
    setParentOptions(options);
  };

  // 获取材料属性数据
  const fetchData = async (): Promise<void> => {
    setLoading(true);
    try {
      const response = await listAddAttributes({});
      console.log('接口返回:', response);

      if ((response as any)?.status === 0) {
        const data = (response as any)?.data || [];
        const dataArray = Array.isArray(data) ? data : [data];
        setTreeData(dataArray);

        // 转换为 Ant Design Tree 数据格式
        const antdData = convertToAntdTreeData(dataArray);
        setAntdTreeData(antdData);

        // 默认展开第一级
        const firstLevelKeys = dataArray.map((item) => item.id);
        setExpandedKeys(firstLevelKeys);

        // 生成父级选项
        generateParentOptions(dataArray);
      } else {
        message.error((response as any)?.message || '获取数据失败');
      }
    } catch (error) {
      console.error('获取材料属性列表失败:', error);
      message.error('获取数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 删除
  const handleDelete = async (node: TreeNodeData): Promise<void> => {
    Modal.confirm({
      title: '确认删除',
      icon: <ExclamationCircleOutlined />,
      content: (
        <div>
          <p>
            确定要删除 <strong>&ldquo;{node.materialName}&rdquo;</strong> 吗？
          </p>
          {node.hasChild ? (
            <p style={{ color: '#ff4d4f' }}>⚠️ 注意：该分类下还有子项，删除后子项也会一并删除！</p>
          ) : null}
        </div>
      ),
      okText: '确定删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          console.log('删除参数:', { ids: node.id });

          const response = await deleteAttributes({ ids: node.id });

          if ((response as any)?.status === 0) {
            message.success('删除成功');
            // 重新获取数据
            fetchData();
            // 清空选中状态
            setSelectedNode(null);
            setSelectedKeys([]);
          } else {
            message.error((response as any)?.message || '删除失败');
          }
        } catch (error) {
          console.error('删除失败:', error);
          message.error('删除失败');
        }
      },
    });
  };

  // 将原始数据转换为 Ant Design Tree 组件需要的格式
  const convertToAntdTreeData = (data: TreeNodeData[]): DataNode[] => {
    return data.map((item) => ({
      key: item.id,
      title: (
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <span style={{ display: 'flex', alignItems: 'center' }}>
            {item.hasChild ? (
              <FolderOutlined style={{ marginRight: 4, color: '#1890ff' }} />
            ) : (
              <FileOutlined style={{ marginRight: 4, color: '#52c41a' }} />
            )}
            {item.materialName}
            <Tag color="blue" style={{ marginLeft: 8, fontSize: '12px' }}>
              深度{item.depth}
            </Tag>
          </span>
          <Space size="small" onClick={(e) => e.stopPropagation()}>
            <Tooltip title="新增子项">
              <Button
                type="text"
                size="small"
                icon={<PlusOutlined />}
                onClick={() => handleAdd(item)}
              />
            </Tooltip>
            <Tooltip title="编辑">
              <Button
                type="text"
                size="small"
                icon={<EditOutlined />}
                onClick={() => handleEdit(item)}
              />
            </Tooltip>
            <Tooltip title="删除">
              <Button
                type="text"
                size="small"
                danger
                icon={<DeleteOutlined />}
                onClick={() => handleDelete(item)}
              />
            </Tooltip>
          </Space>
        </div>
      ),
      children:
        item.childList && item.childList.length > 0
          ? convertToAntdTreeData(item.childList)
          : undefined,
      data: item, // 保存原始数据
    }));
  };

  // 新增
  const handleAdd = (parentNode?: TreeNodeData): void => {
    setIsEdit(false);
    setCurrentNode({
      parentId: parentNode?.id || 0,
    });
    setModalOpen(true);
  };

  // 编辑
  const handleEdit = (node: TreeNodeData): void => {
    setIsEdit(true);
    setCurrentNode({
      id: node.id,
      materialName: node.materialName,
      parentId: node.parentId,
      fullPath: node.fullPath,
      fullPathId: node.fullPathId,
      depth: node.depth,
      createTime: node.createTime,
      updateTime: node.updateTime,
      isDeleted: node.isDeleted || 0,
    });
    setModalOpen(true);
  };

  // 计算完整路径和深度
  const calculatePathAndDepth = (parentId: number) => {
    if (parentId === 0) {
      return {
        fullPath: '',
        fullPathId: '',
        depth: 1,
      };
    }

    // 查找父节点
    const findParentNode = (nodes: TreeNodeData[], targetId: number): TreeNodeData | null => {
      for (const node of nodes) {
        if (node.id === targetId) {
          return node;
        }
        if (node.childList) {
          const found = findParentNode(node.childList, targetId);
          if (found) return found;
        }
      }
      return null;
    };

    const parentNode = findParentNode(treeData, parentId);
    if (parentNode) {
      return {
        fullPath: parentNode.fullPath,
        fullPathId: parentNode.fullPathId,
        depth: parentNode.depth + 1,
      };
    }

    return {
      fullPath: '',
      fullPathId: '',
      depth: 1,
    };
  };

  // 提交表单
  const handleSubmit = async (values: any) => {
    try {
      let params: any = {
        materialName: values.materialName,
        parentId: values.parentId || 0,
      };

      if (isEdit) {
        // 编辑时需要传递完整的参数
        const pathInfo = calculatePathAndDepth(values.parentId || 0);
        params = {
          id: currentNode.id,
          materialName: values.materialName,
          parentId: values.parentId || 0,
          fullPath: pathInfo.fullPath
            ? `${pathInfo.fullPath}/${values.materialName}`
            : values.materialName,
          fullPathId: pathInfo.fullPathId
            ? `${pathInfo.fullPathId}/${currentNode.id}`
            : `${currentNode.id}`,
          depth: pathInfo.depth,
          // 保留原有的时间信息
          createTime: currentNode.createTime,
          updateTime: new Date().toISOString(),
          isDeleted: currentNode.isDeleted || 0,
        };
      } else {
        // 新增时需要计算路径和深度
        const pathInfo = calculatePathAndDepth(values.parentId || 0);
        params = {
          ...params,
          fullPath: pathInfo.fullPath
            ? `${pathInfo.fullPath}/${values.materialName}`
            : values.materialName,
          fullPathId: pathInfo.fullPathId
            ? `${pathInfo.fullPathId}/${Date.now()}`
            : `${Date.now()}`,
          depth: pathInfo.depth,
          isDeleted: 0,
        };
      }

      console.log('提交参数:', params);

      const response = isEdit ? await editAttributes(params) : await addAttributes(params);

      if ((response as any)?.status === 0) {
        message.success(isEdit ? '编辑成功' : '新增成功');
        setModalOpen(false);
        fetchData();
      } else {
        message.error((response as any)?.message || (isEdit ? '编辑失败' : '新增失败'));
      }
    } catch (error) {
      console.error('操作失败:', error);
      message.error(isEdit ? '编辑失败' : '新增失败');
    }
  };

  // 树节点选择
  const onSelect = (selectedKeys: React.Key[], info: any) => {
    setSelectedKeys(selectedKeys);
    if (selectedKeys.length > 0) {
      setSelectedNode(info.node.data);
    } else {
      setSelectedNode(null);
    }
  };

  // 树节点展开/收起
  const onExpand = (expandedKeys: React.Key[]) => {
    setExpandedKeys(expandedKeys);
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <PageContainer>
      <Row gutter={16}>
        {/* 左侧树形展示 */}
        <Col span={16}>
          <Card
            title="材料属性分类"
            extra={
              <Button type="primary" icon={<PlusOutlined />} onClick={() => handleAdd()}>
                新增顶级分类
              </Button>
            }
          >
            <Spin spinning={loading}>
              {antdTreeData.length > 0 ? (
                <Tree
                  treeData={antdTreeData}
                  selectedKeys={selectedKeys}
                  expandedKeys={expandedKeys}
                  onSelect={onSelect}
                  onExpand={onExpand}
                  showLine
                  height={600}
                />
              ) : (
                <div style={{ textAlign: 'center', padding: '50px', color: '#999' }}>
                  暂无数据，请点击右上角按钮新增分类
                </div>
              )}
            </Spin>
          </Card>
        </Col>

        {/* 右侧详情展示 */}
        <Col span={8}>
          <Card title="选中节点信息">
            {selectedNode ? (
              <div>
                <p>
                  <strong>名称：</strong>
                  {selectedNode.materialName}
                </p>
                <p>
                  <strong>完整路径：</strong>
                  {selectedNode.fullPath}
                </p>
                <p>
                  <strong>深度：</strong>
                  {selectedNode.depth}
                </p>
                <p>
                  <strong>父级ID：</strong>
                  {selectedNode.parentId}
                </p>
                <p>
                  <strong>是否有子项：</strong>
                  {selectedNode.hasChild ? '是' : '否'}
                </p>
                <Space style={{ marginTop: 16 }}>
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={() => handleAdd(selectedNode)}
                  >
                    新增子项
                  </Button>
                  <Button icon={<EditOutlined />} onClick={() => handleEdit(selectedNode)}>
                    编辑
                  </Button>
                  <Button
                    danger
                    icon={<DeleteOutlined />}
                    onClick={() => handleDelete(selectedNode)}
                  >
                    删除
                  </Button>
                </Space>
              </div>
            ) : (
              <div style={{ textAlign: 'center', padding: '50px', color: '#999' }}>
                请在左侧选择一个节点查看详情
              </div>
            )}
          </Card>
        </Col>
      </Row>

      {/* 新增/编辑弹窗 */}
      <ModalForm
        title={isEdit ? '编辑材料属性' : '新增材料属性'}
        open={modalOpen}
        onOpenChange={setModalOpen}
        onFinish={handleSubmit}
        initialValues={currentNode}
        width={500}
      >
        <ProFormText
          name="materialName"
          label="属性名称"
          placeholder="请输入属性名称"
          rules={[{ required: true, message: '请输入属性名称' }]}
        />
        <ProFormSelect
          name="parentId"
          label="父级分类"
          placeholder="请选择父级分类"
          options={parentOptions}
          rules={[{ required: true, message: '请选择父级分类' }]}
        />
      </ModalForm>
    </PageContainer>
  );
};

export default MaterialsPropertyList;
