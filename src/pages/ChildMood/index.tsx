import type { ActionType, ProColumns } from '@ant-design/pro-components';
import {
  PageContainer,
  ProTable,
  ModalForm,
  ProFormText,
  ProFormSelect,
  ProFormDigit,
} from '@ant-design/pro-components';
import { Button, message, Form, Modal, Tooltip, Image } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import React, { useRef, useState, useEffect } from 'react';
import {
  queryMoodDictionaryPageList,
  saveOrUpdateMoodDictionary,
  deleteMoodDictionary,
} from '@/services/childMood';
import UploadImageComponent from '@/components/UploadImage';
import { fetchClient } from '@/services/fileUpload';

const ChildMoodManagement: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const [form] = Form.useForm();
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<any>({});
  const [client, setClient] = useState<any>(null);
  const [imageList, setImageList] = useState<any[]>([]);

  // 初始化上传客户端
  useEffect(() => {
    fetchClient(setClient);
  }, []);

  // 处理新增
  const handleAdd = () => {
    setCurrentRow({});
    setImageList([]);
    form.resetFields();
    setModalOpen(true);
  };

  // 处理编辑
  const handleEdit = (record: any) => {
    setCurrentRow(record);
    // 如果有图片ID，构造图片列表数据
    if (record.imageId) {
      // 构造图片数据，兼容 imageUrl 字段
      const imageData = {
        id: record.imageId,
        uri: record.imageUrl || '', // 优先使用 imageUrl
        url: record.imageUrl || '', // 添加 url 字段用于显示
        filename: `mood_image_${record.imageId}`,
        uid: record.imageId,
        hash: record.imageId,
      };
      setImageList([imageData]);
    } else {
      setImageList([]);
    }
    form.setFieldsValue(record);
    setModalOpen(true);
  };

  // 处理保存
  const handleSave = async (values: any) => {
    try {
      // 验证心情图标是否已上传
      if (imageList.length === 0) {
        message.error('请上传心情图标');
        return false;
      }

      const params = {
        ...values,
        category: 1, // 默认设置为基础表情
        imageId: imageList[0].id,
      };

      if (currentRow.id) {
        params.id = currentRow.id;
      }

      const response = await saveOrUpdateMoodDictionary(params);

      if (response?.status === 0) {
        message.success(currentRow.id ? '修改成功' : '新增成功');
        setModalOpen(false);
        actionRef.current?.reload();
        return true;
      } else {
        message.error(response?.message || '操作失败');
        return false;
      }
    } catch (error) {
      console.error('保存失败:', error);
      message.error('操作失败');
      return false;
    }
  };

  // 处理删除
  const handleDelete = (record: any) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除心情"${record.moodName}"吗？此操作不可恢复。`,
      okText: '确定',
      cancelText: '取消',
      okType: 'danger',
      onOk: async () => {
        try {
          const response = await deleteMoodDictionary(record.id);
          if (response?.status === 0) {
            message.success('删除成功');
            actionRef.current?.reload();
          } else {
            message.error(response?.message || '删除失败');
          }
        } catch (error) {
          console.error('删除失败:', error);
          message.error('删除失败');
        }
      },
    });
  };

  // 定义表格列
  const columns: ProColumns<any>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      valueType: 'text',
      width: 80,
      search: false,
    },
    {
      title: '心情名称',
      dataIndex: 'moodName',
      valueType: 'text',
      width: 200,
      render: (result) => {
        return (
          <Tooltip title={result}>
            <div
              style={{
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                maxWidth: '180px',
              }}
            >
              {result}
            </div>
          </Tooltip>
        );
      },
    },
    {
      title: '心情图片',
      dataIndex: 'imageUrl',
      valueType: 'text',
      width: 100,
      search: false,
      render: (_, record) => {
        // 优先显示 imageUrl，如果没有则显示 imageId
        if (record.imageUrl) {
          return (
            <Image
              src={record.imageUrl}
              alt="心情图片"
              width={60}
              height={60}
              style={{ objectFit: 'cover', borderRadius: 4 }}
              fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
            />
          );
        } else if (record.imageId) {
          return <span style={{ color: '#666', fontSize: '12px' }}>ID: {record.imageId}</span>;
        }
        return '-';
      },
    },
    {
      title: '分类',
      dataIndex: 'category',
      valueType: 'text',
      width: 120,
      render: (result) => {
        const categoryMap: Record<number, string> = {
          1: '基础表情',
          5: '自定义表情',
        };
        return categoryMap[result as number] || result;
      },
      valueEnum: {
        1: { text: '基础表情', status: 'Processing' },
        5: { text: '自定义表情', status: 'Default' },
      },
    },

    {
      title: '班级',
      dataIndex: 'className',
      valueType: 'text',
      width: 120,
      search: false,
      render: (result) => {
        return result || '-';
      },
    },
    {
      title: '学校',
      dataIndex: 'schoolName',
      valueType: 'text',
      width: 150,
      search: false,
      render: (result) => {
        return result || '-';
      },
    },
    {
      title: '排序',
      dataIndex: 'sort',
      valueType: 'text',
      width: 80,
      search: false,
      render: (result) => {
        return result || '-';
      },
    },
    {
      title: '状态',
      dataIndex: 'state',
      valueType: 'text',
      width: 100,
      search: false,
      render: (result) => {
        const stateMap: Record<number, { text: string; color: string }> = {
          0: { text: '禁用', color: '#ff4d4f' },
          1: { text: '启用', color: '#52c41a' },
        };
        const state = stateMap[result as number];
        return state ? <span style={{ color: state.color }}>{state.text}</span> : result;
      },
      valueEnum: {
        0: { text: '禁用', status: 'Error' },
        1: { text: '启用', status: 'Success' },
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      valueType: 'text',
      width: 150,
      search: false,
      render: (result) => {
        return result || '-';
      },
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      valueType: 'text',
      width: 150,
      search: false,
      render: (result) => {
        return result || '-';
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      valueType: 'option',
      width: 160,
      fixed: 'right',
      render: (_, record) => [
        <Button key="edit" type="link" size="small" onClick={() => handleEdit(record)}>
          编辑
        </Button>,
        <Button key="delete" type="link" size="small" danger onClick={() => handleDelete(record)}>
          删除
        </Button>,
      ],
    },
  ];

  return (
    <PageContainer>
      <ProTable<any>
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 'auto',
          defaultCollapsed: false,
          collapsed: false,
          collapseRender: false,
        }}
        toolBarRender={() => [
          <Button type="primary" key="primary" onClick={handleAdd} icon={<PlusOutlined />}>
            新建心情
          </Button>,
        ]}
        request={async (params) => {
          try {
            console.log('请求参数:', params);
            const response = await queryMoodDictionaryPageList({
              pageNum: params.current,
              pageSize: params.pageSize,
              moodName: params.moodName,
              // 如果用户没有选择分类，默认显示基础表情(category: 1)
              category: params.category !== undefined ? params.category : 1,
            });

            console.log('接口返回:', response);

            if (response?.status === 0) {
              return {
                data: response.data?.records || [],
                success: true,
                total: response.data?.total || 0,
              };
            } else {
              message.error(response?.message || '获取数据失败');
              return {
                data: [],
                success: false,
                total: 0,
              };
            }
          } catch (error) {
            console.error('获取心情字典列表失败:', error);
            message.error('获取数据失败');
            return {
              data: [],
              success: false,
              total: 0,
            };
          }
        }}
        columns={columns}
        pagination={{
          defaultPageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
        }}
        scroll={{ x: 1000 }}
      />

      <ModalForm
        title={currentRow.id ? '编辑心情' : '新增心情'}
        width="600px"
        form={form}
        open={modalOpen}
        modalProps={{
          maskClosable: false,
          destroyOnClose: true,
        }}
        onOpenChange={(open) => {
          setModalOpen(open);
          if (!open) {
            setCurrentRow({});
            setImageList([]);
            form.resetFields();
          }
        }}
        onFinish={handleSave}
      >
        <ProFormText
          name="moodName"
          label="心情名称"
          rules={[{ required: true, message: '请输入心情名称' }]}
          placeholder="请输入心情名称"
        />

        <ProFormSelect
          name="state"
          label="状态"
          rules={[{ required: true, message: '请选择状态' }]}
          options={[
            { label: '禁用', value: 0 },
            { label: '启用', value: 1 },
          ]}
          placeholder="请选择状态"
          initialValue={1}
        />

        <ProFormDigit name="sort" label="排序" placeholder="请输入排序值" min={0} />

        <UploadImageComponent
          key="upload"
          fileName="imageId"
          label="心情图标"
          max={1}
          client={client}
          fileList={imageList}
          setClient={setClient}
          setFileList={setImageList}
          accept=".png,.jpg,.jpeg,.gif"
        />
      </ModalForm>
    </PageContainer>
  );
};

export default ChildMoodManagement;
