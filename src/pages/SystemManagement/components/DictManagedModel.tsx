/* eslint-disable guard-for-in */
import { ModalForm, ProFormText, ProFormDigit } from '@ant-design/pro-components';
import React, { useMemo } from 'react';
import { addDict, updateDict, addDictItem, updateDictItem } from '@/services/toolKit';
import { message } from 'antd';
const DictManagedModel: React.FC = ({ type, open, setModalOpen, onRefresh, data }) => {
  // 提交
  const handleFinish = async (values: any) => {
    console.log(type);
    console.log(values);
    let fn = null;
    // 判断字符是否包含Item
    if (type.includes('Item')) {
      fn = type === 'addItem' ? addDictItem : updateDictItem;
    } else {
      fn = type === 'add' ? addDict : updateDict;
    }
    const res = await fn(values);
    if (res.status === 0) {
      message.success('成功');
      setModalOpen(false);
      onRefresh();
      return true;
    }
    message.error(res?.error?.stack || '失败');
  };

  const onFinishFailed = () => {
    setModalOpen(false);
  };

  const title = useMemo(() => {
    let result: string = '';

    if (type === 'add') result = '新增字典';
    if (type === 'edit') result = '编辑字典';
    if (type === 'addItem') result = '新增字典项';
    if (type === 'editItem') result = '编辑字典项';
    return result;
  }, [type]);

  return (
    <>
      {type.includes('Item') ? (
        <ModalForm
          title={title}
          width="30em"
          open={open}
          initialValues={data}
          modalProps={{
            destroyOnClose: true,
            onCancel: onFinishFailed,
          }}
          onOpenChange={setModalOpen}
          onFinish={handleFinish}
        >
          {type === 'editItem' ? (
            <ProFormDigit
              rules={[
                {
                  required: true,
                  type: 'number',
                  message: '请填写字典项ID',
                },
              ]}
              label="字典项ID"
              name="dictItemId"
            />
          ) : null}
          <ProFormText
            rules={[
              {
                required: true,
                message: '请填写字典编号',
              },
            ]}
            label="字典编号"
            name="dictCode"
            disabled={true}
          />
          <ProFormText
            rules={[
              {
                required: true,
                message: '请填写字典项编号',
              },
            ]}
            label="字典项编号"
            name="dictItemCode"
          />
          <ProFormText
            rules={[
              {
                required: true,
                message: '请填写字典项名称',
              },
            ]}
            label="字典项名称"
            name="dictItemName"
          />
          <ProFormDigit label="字典项序号" name="dictItemSort" />
        </ModalForm>
      ) : (
        <ModalForm
          title={title}
          width="30em"
          open={open}
          initialValues={data}
          modalProps={{
            destroyOnClose: true,
            onCancel: onFinishFailed,
          }}
          onOpenChange={setModalOpen}
          onFinish={handleFinish}
        >
          {/* <ProFormDigit
            rules={[
              {
                required: true,
                type: 'number',
                message: '请填写字典ID',
              },
            ]}
            label="字典ID"
            name="dictId"
          /> */}
          <ProFormText
            rules={[
              {
                required: true,
                message: '请填写字典编号',
              },
            ]}
            label="字典编号"
            name="dictCode"
          />
          <ProFormText
            rules={[
              {
                required: true,
                message: '请填写字典名称',
              },
            ]}
            label="字典名称"
            name="dictName"
          />
        </ModalForm>
      )}
    </>
  );
};

export default DictManagedModel;
