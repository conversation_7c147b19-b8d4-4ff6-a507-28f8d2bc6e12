/* eslint-disable guard-for-in */
import { timeColumn } from '@/components/ColumnRender';
import {
  addDictionary,
  fetchDictionaryList,
  fetchEnum,
  updateDictionary,
  updateDictionaryState,
} from '@/services/api';
import { PlusOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import {
  ModalForm,
  PageContainer,
  ProFormSelect,
  ProFormText,
  ProTable,
} from '@ant-design/pro-components';
import { Button, Form, message } from 'antd';
import React, { useEffect, useMemo, useRef, useState } from 'react';

const DictionaryList: React.FC = () => {
  /**
   * @en-US Pop-up window of new window
   * @zh-CN 新建窗口的弹窗
   *  */
  const [modalOpen, setModalOpen] = useState<boolean>(false);

  const [dictionaryCategory, setDictionaryCategory] = useState<any>({});

  const [currentRow, setCurrentRow] = useState<any>({});

  const actionRef = useRef<ActionType>();

  const [form] = Form.useForm();

  useEffect(() => {
    fetchEnum().then((res) => {
      const { DictionaryCategory } = res?.data;

      if (res?.status === 0) {
        for (let key in DictionaryCategory) {
          DictionaryCategory[key]['text'] = DictionaryCategory[key]['desc'];
        }
        setDictionaryCategory(DictionaryCategory);
      }
    });
  }, []);

  // 新增表单-类型-下拉项
  const optionsArr = useMemo(() => {
    return Object.keys(dictionaryCategory).map((key) => ({
      value: key,
      label: dictionaryCategory[key]['desc'],
    }));
  }, [dictionaryCategory]);

  const handleModalOpen = ({ type, record }) => {
    form.resetFields();
    setCurrentRow({});

    if (type === 'edit') {
      setCurrentRow(record);
      form.setFieldsValue({
        value: record.value,
        category: record.category.toString(),
      });
    }

    setModalOpen(true);
  };

  // 新增配置项
  const handleAdd = async (value: { value: string; category: string }) => {
    try {
      let res: API.AdminResponse = {};

      if (!!currentRow?.id) {
        res = await updateDictionary({ ...value, id: currentRow?.id, sort: 0 });
      } else {
        res = await addDictionary({ ...value, sort: 0, category: Number(value.category) });
      }

      if (res?.status === 0) {
        message.success(!currentRow?.id ? '新建成功' : '更新成功');
        setModalOpen(false);
        if (actionRef.current) {
          actionRef.current.reload();
        }
      }
    } catch (error) {
      message.error(error?.message || '新建失败');
    }
  };

  // 删除配置项
  const handleDelete = async (record: any) => {
    const hide = message.loading('正在更新状态');

    try {
      await updateDictionaryState({
        id: record?.id,
        state: record?.state === 0 ? 1 : 0,
      });
      hide();
      message.success('Update successfully and will refresh soon');
      if (actionRef.current) {
        actionRef.current.reload();
      }
      return true;
    } catch (error) {
      hide();
      message.error(error?.message || 'Update failed, please try again');
      return false;
    }
  };

  const columns: ProColumns<API.RuleListItem>[] = [
    {
      title: '编号',
      dataIndex: 'id',
      valueType: 'text',
      width: 80,
      search: false,
    },
    {
      title: '类型',
      dataIndex: 'category',
      valueType: 'select',
      width: 120,
      render: (_, record) => <span>{dictionaryCategory[record.category]?.desc}</span>,
      valueEnum: dictionaryCategory,
    },
    {
      title: 'value',
      dataIndex: 'value',
      valueType: 'text',
      width: 120,
    },
    {
      title: '状态',
      dataIndex: 'state',
      // hideInForm: true,
      // search: false,
      width: 80,
      valueEnum: {
        0: { color: 'red', text: '停用', status: 'Default' },
        1: { color: 'green', text: '启用', status: 'Processing' },
      },
    },
    timeColumn,
    {
      title: '操作',
      ellipsis: true,
      dataIndex: 'action',
      key: 'action',
      valueType: 'option',
      width: '14em',
      search: false,
      render: (_, record) => [
        <Button
          size="small"
          type="primary"
          key="edit"
          onClick={() => {
            handleModalOpen({ type: 'edit', record });
          }}
        >
          编辑
        </Button>,
        <Button
          size="small"
          type="primary"
          key="delete"
          danger={!!record?.state}
          onClick={async () => {
            await handleDelete(record);
          }}
        >
          {record?.state === 0 ? '启用' : '停用'}
        </Button>,
      ],
    },
  ];

  return (
    <PageContainer>
      <ProTable<API.RuleListItem, API.PageParams>
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 'auto',
          defaultCollapsed: false,
          collapsed: false,
          collapseRender: false,
        }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              handleModalOpen({ type: 'add' });
            }}
          >
            <PlusOutlined /> 新建
          </Button>,
        ]}
        request={fetchDictionaryList}
        columns={columns}
      />
      <ModalForm
        title={!currentRow?.id ? '新建配置' : '编辑配置'}
        width="400px"
        form={form}
        open={modalOpen}
        modalProps={{
          maskClosable: false,
        }}
        onOpenChange={setModalOpen}
        onFinish={async (value) => {
          await handleAdd(value);
        }}
      >
        <ProFormSelect
          label="类型"
          width="md"
          name="category"
          rules={[
            {
              required: true,
              message: '请选择类型',
            },
          ]}
          options={optionsArr}
          disabled={!!currentRow?.id}
        />
        <ProFormText
          rules={[
            {
              required: true,
              message: '请填写数据值',
            },
          ]}
          label="数据值"
          width="md"
          name="value"
        />
      </ModalForm>
    </PageContainer>
  );
};

export default DictionaryList;
