// 字典项

/* eslint-disable guard-for-in */
// import { AntFormsItem, FormFields, FormTypes } from '@/components/UseForms';
import { deleteDictItem, getDictItem } from '@/services/toolKit';
// import type { ProColumns } from '@ant-design/pro-components';
import { PageContainer, ProTable } from '@ant-design/pro-components';
import { PlusOutlined } from '@ant-design/icons';
import DictManagedModel from '../components/DictManagedModel';
import React, { useRef, useState } from 'react';
// import dayjs from 'dayjs';
import { Button, message, Popconfirm } from 'antd';
import { useSearchParams } from 'umi';
// import { keyBy } from 'lodash';

const DictItem: React.FC = () => {
  const [type, setType] = useState<string>('add');
  const [open, setOpen] = useState<boolean>(false);
  const [data, setData] = useState<object>({});
  const [searchParams] = useSearchParams();
  const id = searchParams.get('dictId');

  const actionRef = useRef<ActionType>();
  // 删除
  const handleDeleteDict = async (record: any) => {
    console.log(record);
    const { dictItemId } = record;
    const res = await deleteDictItem(dictItemId);
    if (res.status === 0) {
      message.success('删除成功');
      actionRef.current?.reload();
      return;
    }
    message.error(res?.error?.stack || '删除失败');
  };
  // 详情
  const handleDetailDict = async (record: any) => {
    setType('editItem');
    setData(record);
    setOpen(true);
  };

  const columns = [
    {
      title: '字典项序号',
      dataIndex: 'dictItemSort',
      valueType: 'text',
      width: 50,
      search: false,
    },
    // {
    //   title: '字典项ID',
    //   dataIndex: 'dictItemId',
    //   valueType: 'text',
    //   width: 50,
    //   search: false,
    // },
    {
      title: '字典项编号',
      dataIndex: 'dictItemCode',
      valueType: 'text',
      width: 40,
    },
    {
      title: '字典编号',
      dataIndex: 'dictCode',
      initialValue: id,
      valueType: 'text',
      width: 40,
      // 不显示在列表中
      search: false,
    },
    {
      title: '字典项名称',
      dataIndex: 'dictItemName',
      valueType: 'text',
      width: 50,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      valueType: 'text',
      width: 50,
      search: false,
    },
    {
      title: '修改时间',
      dataIndex: 'updateTime',
      valueType: 'text',
      width: 50,
      search: false,
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      valueType: 'option',
      width: 50,
      search: false,
      render: (_, record) => [
        <Button
          size="small"
          type="link"
          key="detail"
          target="_blank"
          onClick={() => {
            handleDetailDict(record);
          }}
        >
          编辑
        </Button>,
        <Popconfirm
          title="删除"
          description="您确定删除这个字典吗?"
          onConfirm={() => handleDeleteDict(record)}
          // onCancel={cancel}

          key="delete"
        >
          <Button size="small" type="link" danger>
            删除
          </Button>
        </Popconfirm>,
      ],
    },
  ];

  return (
    <PageContainer>
      <ProTable<API.PageParams>
        actionRef={actionRef}
        rowKey="dictId"
        search={{
          labelWidth: 'auto',
          defaultCollapsed: false,
          collapsed: false,
          collapseRender: false,
        }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              setType('addItem');
              setOpen(true);
              setData({ dictCode: id });
              //   handleModalOpen({ type: 'add' });
            }}
          >
            <PlusOutlined /> 新建
          </Button>,
        ]}
        request={async (params) => {
          console.log(params);
          let response = await getDictItem({
            currentPage: params.current,
            pageSize: params.pageSize,
            pageModel: {
              dictCode: id,
              dictItemCode: params.dictItemCode,
              dictItemName: params.dictItemName,
            },
          });
          return {
            data: response.data,
            success: true,
            total: response?.metadata?.count,
          };
        }}
        columns={columns}
      />
      <DictManagedModel
        open={open}
        type={type}
        setModalOpen={setOpen}
        onRefresh={actionRef.current?.reload}
        data={data}
      />
    </PageContainer>
  );
};

export default DictItem;
