/* eslint-disable guard-for-in */
// import { AntFormsItem, FormFields, FormTypes } from '@/components/UseForms';
import { getPageDict, deleteDict } from '@/services/toolKit';
// import type { ProColumns } from '@ant-design/pro-components';
import { PageContainer, ProTable } from '@ant-design/pro-components';
import { PlusOutlined } from '@ant-design/icons';
import DictManagedModel from '../components/DictManagedModel';
import React, { useRef, useState } from 'react';
// import dayjs from 'dayjs';
import { Button, message, Popconfirm } from 'antd';
// import { keyBy } from 'lodash';

const Dict: React.FC = () => {
  const [type, setType] = useState<string>('add');
  const [open, setOpen] = useState<boolean>(false);
  const [data, setData] = useState<object>({});
  const actionRef = useRef<ActionType>();
  // 删除
  const handleDeleteDict = async (record: any) => {
    console.log(record);
    const { dictId } = record;
    const res = await deleteDict(dictId);
    if (res.status === 0) {
      message.success('删除成功');
      actionRef.current?.reload();
      return;
    }
    message.error(res?.error?.stack || '删除失败');
  };
  // 详情
  const handleDetailDict = async (record: any) => {
    setType('edit');
    setData(record);
    setOpen(true);
    // let res = await getDictDe(record.dictId);
    // if (res.status === 0) {
    //   setType('edit');
    //   setData(res.data);
    //   setOpen(true);
    //   return;
    // }
    // message.error(res?.error?.stack || '获取失败');
  };

  const columns = [
    {
      title: '序号',
      dataIndex: 'dictId',
      valueType: 'text',
      width: 50,
      search: false,
    },
    {
      title: '字典编号',
      dataIndex: 'dictCode',
      valueType: 'text',
      width: 40,
    },
    {
      title: '字典名称',
      dataIndex: 'dictName',
      valueType: 'text',
      width: 50,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      valueType: 'text',
      width: 50,
      search: false,
    },
    {
      title: '修改时间',
      dataIndex: 'updateTime',
      valueType: 'text',
      width: 50,
      search: false,
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      valueType: 'option',
      width: 50,
      search: false,
      render: (_, record) => [
        <Button
          size="small"
          type="link"
          key="detail"
          target="_blank"
          onClick={() => {
            console.log(record);
            handleDetailDict(record);
          }}
        >
          编辑
        </Button>,
        <Button
          size="small"
          type="link"
          key="dictItem"
          target="_blank"
          href={`/SystemManagement/dictManagedItem?dictId=${record.dictCode}`}
        >
          字典项
        </Button>,
        <Popconfirm
          title="删除"
          description="您确定删除这个字典吗?"
          onConfirm={() => handleDeleteDict(record)}
          // onCancel={cancel}

          key="delete"
        >
          <Button size="small" type="link" danger>
            删除
          </Button>
        </Popconfirm>,
      ],
    },
  ];

  return (
    <PageContainer>
      <ProTable<API.PageParams>
        actionRef={actionRef}
        rowKey="dictId"
        search={{
          labelWidth: 'auto',
          defaultCollapsed: false,
          collapsed: false,
          collapseRender: false,
        }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              setType('add');
              setOpen(true);
              //   handleModalOpen({ type: 'add' });
            }}
          >
            <PlusOutlined /> 新建
          </Button>,
        ]}
        request={async (
          // 第一个参数 params 查询表单和 params 参数的结合
          // 第一个参数中一定会有 pageSize 和  current ，这两个参数是 antd 的规范
          params: T & {
            pageSize: number;
            current: number;
            dictName?: string;
            dictCode?: string;
          },
        ) => {
          let res = await getPageDict({
            currentPage: params.current,
            pageSize: params.pageSize,
            dictName: params.dictName,
            dictCode: params.dictCode,
          });
          return {
            data: res.data,
            success: true,
          };
        }}
        columns={columns}
      />
      <DictManagedModel
        open={open}
        type={type}
        setModalOpen={setOpen}
        onRefresh={actionRef.current?.reload}
        data={data}
      />
    </PageContainer>
  );
};

export default Dict;
