/* eslint-disable guard-for-in */
import { Grade } from '@/services/constants';
import { checkPositiveInteger } from '@/services/utils';
import { ModalForm, ProFormDigit, ProFormText } from '@ant-design/pro-components';
import { message } from 'antd';
import React, { useEffect, useMemo } from 'react';

const AddGradeModal: React.FC = ({ form, modalOpen, setModalOpen, currentRow, handleAdd }) => {
  useEffect(() => {
    if (currentRow?.type === 'edit' && modalOpen) {
      form.setFieldsValue({
        title: currentRow?.title,
        sort: currentRow?.sort,
      });
    }
  }, [modalOpen]);

  const handleFinish = async (value) => {
    await handleAdd(value);
  };

  const onFinishFailed = () => {
    message.error('表单校验失败，请检查输入');
  };

  const modalTitle = useMemo(() => {
    let result: string = '';

    if (currentRow?.type === 'edit') {
      result = `编辑${Grade[currentRow?.grade]}`;
    } else if (currentRow?.id) {
      result = `新增${Grade[currentRow?.grade + 1]}`;
    } else {
      result = `新增领域`;
    }

    return result;
  }, [currentRow]);

  return (
    <ModalForm
      title={modalTitle}
      width="400px"
      form={form}
      open={modalOpen}
      modalProps={{
        maskClosable: false,
      }}
      onOpenChange={setModalOpen}
      onFinish={handleFinish}
      onFinishFailed={onFinishFailed}
    >
      <ProFormText
        rules={[
          {
            required: true,
            message: '请填写矩阵名称',
          },
        ]}
        label="矩阵名称"
        name="title"
      />
      <ProFormDigit
        rules={[{ validator: checkPositiveInteger }]}
        fieldProps={{
          min: 1, // 最小值
          max: 99999, // 最大值
        }}
        label="排序"
        name="sort"
      />
    </ModalForm>
  );
};

export default AddGradeModal;
