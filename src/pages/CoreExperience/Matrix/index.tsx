/* eslint-disable guard-for-in */
import { timeColumn } from '@/components/ColumnRender';
import { addMatrix, fetchMatrixTree, updateMatrix, updateMatrixState } from '@/services/api';
import { Grade } from '@/services/constants';
import { PlusOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { PageContainer, ProTable } from '@ant-design/pro-components';
import { Button, Form, message } from 'antd';
import React, { useRef, useState } from 'react';
import AddGradeModal from './components/add';

const Matrix: React.FC = () => {
  /**
   * @en-US Pop-up window of new window
   * @zh-CN 新建窗口的弹窗
   *  */
  const [modalOpen, setModalOpen] = useState<boolean>(false);

  const [currentRow, setCurrentRow] = useState<any>({});

  const actionRef = useRef<ActionType>();

  const [form] = Form.useForm();

  const handleModalOpen = ({ type, record }) => {
    form.resetFields();
    setCurrentRow({});

    setTimeout(() => {
      setCurrentRow({ ...record, type });
      setModalOpen(true);
    }, 0);
  };

  // 新增配置项
  const handleAdd = async (value: { key: string; value: string; category: string }) => {
    try {
      let res: API.AdminResponse = {};

      if (currentRow?.type === 'edit') {
        res = await updateMatrix({
          ...value,
          id: currentRow?.id,
          pid: currentRow?.pid,
          grade: currentRow?.grade,
        });
      } else {
        res = await addMatrix({
          ...value,
          pid: currentRow?.id || 0,
          grade: currentRow?.grade ? currentRow?.grade + 1 : 1,
        });
      }

      if (res?.status === 0) {
        message.success(!currentRow?.id ? '新建成功' : '更新成功');
        setModalOpen(false);
        if (actionRef.current) {
          actionRef.current.reload();
        }
      }
    } catch (error) {
      console.log(error);
      message.error(
        !currentRow?.id
          ? error?.message || '新建失败，请重试'
          : error?.message || '更新失败，请重试',
      );
    }
  };

  // 删除配置项
  const handleDelete = async (record: any) => {
    const hide = message.loading('正在更新状态');

    try {
      await updateMatrixState({
        id: record?.id,
        state: record?.state === 0 ? 1 : 0,
      });
      hide();
      message.success('Update successfully and will refresh soon');
      if (actionRef.current) {
        actionRef.current.reload();
      }
      return true;
    } catch (error) {
      hide();
      message.error(error?.message || 'Update failed, please try again');
      return false;
    }
  };

  const columns: ProColumns<API.RuleListItem>[] = [
    {
      title: '编号',
      dataIndex: 'id',
      valueType: 'text',
      width: 80,
      search: false,
    },
    {
      title: '名称',
      dataIndex: 'title',
      valueType: 'text',
      width: 120,
      search: false,
    },
    {
      title: '维度',
      dataIndex: 'grade',
      valueType: 'text',
      width: 120,
      render: (value) => {
        return Grade[value];
      },
      search: false,
    },
    {
      title: '关联商品数量',
      dataIndex: 'proSta',
      valueType: 'text',
      width: 120,
      render: (value) => {
        return value?.count;
      },
      search: false,
    },
    {
      title: '状态',
      dataIndex: 'state',
      // hideInForm: true,
      // search: false,
      width: 80,
      valueEnum: {
        0: { color: 'red', text: '停用', status: 'Default' },
        1: { color: 'green', text: '启用', status: 'Processing' },
      },
    },
    timeColumn,
    {
      title: '操作',
      ellipsis: true,
      dataIndex: 'action',
      key: 'action',
      valueType: 'option',
      width: '14em',
      search: false,
      render: (_, record) => [
        <Button
          size="small"
          type="primary"
          key="edit"
          onClick={() => {
            handleModalOpen({ type: 'edit', record });
          }}
        >
          编辑
        </Button>,
        record?.grade < 3 && (
          <Button
            size="small"
            type="primary"
            key="addChildren"
            onClick={() => {
              handleModalOpen({ type: 'add', record });
            }}
          >
            添加子级
          </Button>
        ),
        <Button
          size="small"
          type="primary"
          key="delete"
          danger={!!record?.state}
          onClick={async () => {
            await handleDelete(record);
          }}
        >
          {record?.state === 0 ? '启用' : '停用'}
        </Button>,
      ],
    },
  ];

  return (
    <PageContainer>
      <ProTable<API.RuleListItem, API.PageParams>
        actionRef={actionRef}
        rowKey="id"
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              handleModalOpen({ type: 'add' });
            }}
          >
            <PlusOutlined /> 新增领域
          </Button>,
        ]}
        request={fetchMatrixTree}
        columns={columns}
        expandable={{ defaultExpandAllRows: true }}
      />
      <AddGradeModal
        form={form}
        modalOpen={modalOpen}
        setModalOpen={setModalOpen}
        currentRow={currentRow}
        actionRef={actionRef}
        handleAdd={handleAdd}
      />
    </PageContainer>
  );
};

export default Matrix;
