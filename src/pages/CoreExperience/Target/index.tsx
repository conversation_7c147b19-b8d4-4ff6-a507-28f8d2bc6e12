/* eslint-disable guard-for-in */
import { timeColumn } from '@/components/ColumnRender';
import {
  addTarget,
  fetchMatrixListByPid,
  fetchTargetList,
  postRelateProduct,
  updateTarget,
  updateTargetState,
} from '@/services/api';
import { PlusOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { PageContainer, ProFormSelect, ProFormText, ProTable } from '@ant-design/pro-components';
import { Button, Form, message } from 'antd';
import { isEmpty } from 'lodash';
import React, { useRef, useState } from 'react';
import AddProductModal from './components/add';
import RelateProductModal from './components/relateProduct';

const TargetList: React.FC = () => {
  /**
   * @en-US Pop-up window of new window
   * @zh-CN 新建窗口的弹窗
   *  */
  const [modalOpen, setModalOpen] = useState<boolean>(false);

  const [relateModalOpen, setRelateModalOpen] = useState<boolean>(false);

  const [currentRow, setCurrentRow] = useState<any>({});

  const [matrix2Options, setMatrix2Options] = useState([]);
  const [matrix3Options, setMatrix3Options] = useState([]);

  const actionRef = useRef<ActionType>();

  const [form] = Form.useForm();

  const handleModalOpen = ({ type, record }) => {
    form.resetFields();
    setCurrentRow({});

    if (type === 'edit') {
      setCurrentRow(record);
    }

    setModalOpen(true);
  };

  // 新增配置项
  const handleAdd = async (value: { key: string; value: string; category: string }) => {
    console.log('value', value, currentRow);
    try {
      let res: API.AdminResponse = {};

      if (!!currentRow?.id) {
        res = await updateTarget({ ...value, id: currentRow?.id });
      } else {
        res = await addTarget({ ...value });
      }

      if (res?.status === 0) {
        message.success(!currentRow?.id ? '新增成功' : '更新成功');
        setModalOpen(false);
        if (actionRef.current) {
          actionRef.current.reload();
        }
      }
    } catch (error) {
      console.log(error);
      message.error(
        !currentRow?.id
          ? error?.message || '新增失败，请重试'
          : error?.message || '更新失败，请重试',
      );
    }
  };

  // 删除配置项
  const handleDelete = async (record: any) => {
    const hide = message.loading('正在更新状态');

    try {
      await updateTargetState({
        id: record?.id,
        state: record?.state === 0 ? 1 : 0,
      });
      hide();
      message.success('Update successfully and will refresh soon');
      if (actionRef.current) {
        actionRef.current.reload();
      }
      return true;
    } catch (error) {
      hide();
      message.error(error?.message || 'Update failed, please try again');
      return false;
    }
  };

  // const handleRelateModalOpen = ({ record }) => {
  //   form.resetFields();
  //   setCurrentRow({});

  //   setTimeout(() => {
  //     setCurrentRow(record);
  //     setRelateModalOpen(true);
  //   }, 0);
  // };

  // 关联商品
  const handleRelateProduct = async (value) => {
    console.log('value', value, currentRow);
    try {
      const res = await postRelateProduct({ ...value, targetId: currentRow?.id });

      if (res?.status === 0) {
        message.success('关联商品成功');
        setRelateModalOpen(false);
        if (actionRef.current) {
          actionRef.current.reload();
        }
      }
    } catch (error) {
      console.log(error);
      message.error(error?.message || '关联商品失败，请重试');
    }
  };

  const handleMatrixListByPid = async (pid) => {
    const { data } = await fetchMatrixListByPid({
      pid,
    });
    const options = data.map((item) => {
      return {
        label: item.title,
        value: item.id,
      };
    });
    return options;
  };

  const columns: ProColumns<API.RuleListItem>[] = [
    {
      title: '编号',
      dataIndex: 'id',
      valueType: 'text',
      width: 80,
      search: false,
    },
    {
      title: '指标名称',
      dataIndex: 'title',
      valueType: 'text',
      width: 200,
      search: false,
    },
    {
      title: '指标或商品名称',
      dataIndex: 'title',
      hideInTable: true,
      renderFormItem: () => {
        return <ProFormText name="title" />;
      },
    },
    {
      title: '年级',
      dataIndex: 'gradeTitle',
      valueType: 'text',
      width: 120,
      search: false,
    },
    {
      title: '领域',
      dataIndex: 'matrix1',
      valueType: 'text',
      width: 120,
      search: false,
      render: (value) => {
        return value?.title;
      },
    },
    {
      title: '维度',
      dataIndex: 'matrix2',
      valueType: 'text',
      width: 120,
      search: false,
      render: (value) => {
        return value?.title;
      },
    },
    {
      title: '子维度',
      dataIndex: 'matrix3',
      valueType: 'text',
      width: 120,
      search: false,
      render: (value) => {
        return value?.title;
      },
    },
    {
      title: '领域',
      dataIndex: 'matrix1Id',
      hideInTable: true,

      renderFormItem: () => {
        return (
          <ProFormSelect
            name="matrix1Id"
            showSearch
            request={async () => {
              return fetchMatrixListByPid({
                pid: 0,
              }).then(({ data }) => {
                return data.map((item) => {
                  return {
                    label: item.title,
                    value: item.id,
                  };
                });
              });
            }}
            onChange={async (value) => {
              if (value) {
                setMatrix2Options(await handleMatrixListByPid(value));
              } else {
                setMatrix2Options([]);
              }
            }}
          />
        );
      },
    },
    {
      title: '维度',
      dataIndex: 'matrix2Id',
      hideInTable: true,
      renderFormItem: () => {
        return (
          <ProFormSelect
            name="matrix2Id"
            options={matrix2Options}
            onChange={async (value) => {
              if (value) {
                setMatrix3Options(await handleMatrixListByPid(value));
              } else {
                setMatrix3Options([]);
              }
            }}
          />
        );
      },
    },
    {
      title: '子维度',
      dataIndex: 'matrix3Id',
      hideInTable: true,
      renderFormItem: () => {
        return <ProFormSelect name="matrix3Id" options={matrix3Options} />;
      },
    },
    {
      title: '关联商品',
      dataIndex: 'productTitles',
      valueType: 'text',
      width: 120,
      search: false,
      render: (result) => {
        return !isEmpty(result) && result !== '-' ? result.join('、') : '-';
      },
    },
    {
      title: '关联商品数量',
      dataIndex: 'productQuantity',
      valueType: 'text',
      width: 120,
      search: false,
    },
    {
      title: '状态',
      dataIndex: 'state',
      // hideInForm: true,
      // search: false,
      width: 80,
      valueEnum: {
        0: { color: 'red', text: '停用', status: 'Default' },
        1: { color: 'green', text: '启用', status: 'Processing' },
      },
    },
    timeColumn,
    {
      title: '操作',
      ellipsis: true,
      dataIndex: 'action',
      key: 'action',
      valueType: 'option',
      width: '14em',
      search: false,
      render: (_, record) => [
        <Button
          size="small"
          type="primary"
          key="edit"
          onClick={() => {
            handleModalOpen({ type: 'edit', record });
          }}
        >
          编辑
        </Button>,
        <Button
          size="small"
          type="primary"
          key="delete"
          danger={!!record?.state}
          onClick={async () => {
            await handleDelete(record);
          }}
        >
          {record?.state === 0 ? '启用' : '停用'}
        </Button>,
        // <Button
        //   size="small"
        //   type="primary"
        //   key="relate"
        //   onClick={() => {
        //     handleRelateModalOpen({ record });
        //   }}
        // >
        //   关联商品
        // </Button>,
      ],
    },
  ];

  return (
    <PageContainer>
      <ProTable<API.RuleListItem, API.PageParams>
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 'auto',
          defaultCollapsed: false,
          collapsed: false,
          collapseRender: false,
        }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              handleModalOpen({ type: 'add' });
            }}
          >
            <PlusOutlined /> 新建
          </Button>,
        ]}
        request={fetchTargetList}
        columns={columns}
        scroll={{ x: 1800 }}
      />
      <AddProductModal
        form={form}
        modalOpen={modalOpen}
        setModalOpen={setModalOpen}
        currentRow={currentRow}
        handleAdd={handleAdd}
      />
      <RelateProductModal
        form={form}
        modalOpen={relateModalOpen}
        setModalOpen={setRelateModalOpen}
        currentRow={currentRow}
        handleAdd={handleRelateProduct}
      />
    </PageContainer>
  );
};

export default TargetList;
