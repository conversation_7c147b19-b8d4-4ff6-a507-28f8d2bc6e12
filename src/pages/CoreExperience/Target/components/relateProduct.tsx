/* eslint-disable guard-for-in */
import { fetchProductList, fetchTargetDetail } from '@/services/api';
import { debounceSearch } from '@/services/utils';
import { ModalForm, ProFormSelect } from '@ant-design/pro-components';
import { message } from 'antd';
import React, { useEffect } from 'react';

const RelateProductModal: React.FC = ({ form, modalOpen, setModalOpen, currentRow, handleAdd }) => {
  useEffect(() => {
    if (!!currentRow?.id && modalOpen) {
      fetchTargetDetail(currentRow?.id).then((res) => {
        if (res?.status === 0) {
          const { data } = res;

          form.setFieldsValue({
            productIds: data?.products?.map((item) => {
              return {
                ...item,
                label: item?.title,
                value: item?.id,
              };
            }),
          });
        }
      });
    }
  }, [modalOpen]);

  const handleFinish = async (value) => {
    const productIds = value?.productIds?.map((item) => item?.value) || [];
    const result = {
      ...value,
      productIds,
    };
    await handleAdd(result);
  };

  const onFinishFailed = () => {
    message.error('表单校验失败，请检查输入');
  };

  return (
    <ModalForm
      title="关联商品"
      width="400px"
      form={form}
      open={modalOpen}
      modalProps={{
        maskClosable: false,
      }}
      onOpenChange={setModalOpen}
      onFinish={handleFinish}
      onFinishFailed={onFinishFailed}
    >
      <ProFormSelect
        rules={[
          {
            required: true,
            message: '请选择关联商品',
          },
        ]}
        name="productIds"
        label="关联商品"
        showSearch
        fieldProps={{
          mode: 'multiple',
          labelInValue: true,
        }}
        request={async (value) => {
          return new Promise((resolve) => {
            debounceSearch(value, fetchProductList, resolve, {
              current: 1,
              pageSize: 20,
              keyword: value?.keyWords,
              state: 1,
            });
          });
        }}
      />
    </ModalForm>
  );
};

export default RelateProductModal;
