/* eslint-disable guard-for-in */
import { fetchMatrixListByPid, fetchTargetDetail } from '@/services/api';
import { DictionaryCategory } from '@/services/constants';
import { checkPositiveInteger } from '@/services/utils';
import {
  DrawerForm,
  ProForm,
  ProFormDependency,
  ProFormDigit,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { Col, Row, message } from 'antd';
import { isEmpty } from 'lodash';
import React, { useEffect, useState } from 'react';

const AddTargetModal: React.FC = ({ form, modalOpen, setModalOpen, currentRow, handleAdd }) => {
  const { initialState } = useModel('@@initialState');
  const { dictionaryList } = initialState || {};

  // 年级列表
  const [grades, setGrades] = useState<any>([]);

  useEffect(() => {
    if (!isEmpty(dictionaryList)) {
      const result = dictionaryList?.map((item) => {
        return {
          value: item?.id,
          label: item?.value,
          category: item?.category,
        };
      });

      const _grades = result.filter((item) => item.category === DictionaryCategory.Grade);

      setGrades(_grades);
    }
  }, []);

  useEffect(() => {
    if (!!currentRow?.id && modalOpen) {
      fetchTargetDetail(currentRow?.id).then((res) => {
        if (res?.status === 0) {
          const { data } = res;

          form.setFieldsValue({
            title: data?.title,
            gradeId: data?.gradeId,
            matrix1Id: {
              label: data?.matrix1?.title,
              value: data?.matrix1?.id,
            },
            matrix2Id: {
              label: data?.matrix2?.title,
              value: data?.matrix2?.id,
            },
            matrix3Id: {
              label: data?.matrix3?.title,
              value: data?.matrix3?.id,
            },
            sort: data?.sort,
          });
        }
      });
    }
  }, [modalOpen]);

  const handleFinish = async (value) => {
    const result = {
      title: value?.title,
      gradeId: value?.gradeId,
      matrix3Id: value?.matrix3Id?.value,
      sort: value?.sort,
    };

    await handleAdd(result);
  };

  const onFinishFailed = () => {
    message.error('表单校验失败，请检查输入');
  };

  const handleFirstSelectChange = () => {
    form.setFieldsValue({
      matrix2Id: undefined,
      matrix3Id: undefined,
    });
  };

  const handleSecondSelectChange = () => {
    form.setFieldsValue({
      matrix3Id: undefined,
    });
  };

  return (
    <DrawerForm
      title={!currentRow?.id ? '新增指标' : '编辑指标'}
      width="800px"
      form={form}
      open={modalOpen}
      drawerProps={{
        maskClosable: false,
      }}
      onOpenChange={setModalOpen}
      onFinish={handleFinish}
      onFinishFailed={onFinishFailed}
    >
      <Row gutter={8}>
        <Col span={12}>
          <ProFormText
            rules={[
              {
                required: true,
                message: '请填写指标名称',
              },
            ]}
            label="指标名称"
            name="title"
          />
        </Col>
        <Col span={12}>
          <ProFormSelect
            label="年级"
            name="gradeId"
            rules={[
              {
                required: true,
                message: '请选择年级',
              },
            ]}
            options={grades}
          />
        </Col>
      </Row>
      <Row gutter={8}>
        <ProForm.Group size={8}>
          <ProFormSelect
            rules={[
              {
                required: true,
                message: '请选择领域',
              },
            ]}
            fieldProps={{
              labelInValue: true,
            }}
            label="选择领域"
            name="matrix1Id"
            width="sm"
            onChange={handleFirstSelectChange}
            request={async () => {
              return fetchMatrixListByPid({
                pid: 0,
              }).then(({ data }) => {
                return data.map((item) => {
                  return {
                    label: item.title,
                    value: item.id,
                  };
                });
              });
            }}
          />
          <ProFormDependency name={['matrix1Id']}>
            {({ matrix1Id }) => {
              return (
                <ProFormSelect
                  params={{
                    key: matrix1Id?.value,
                  }}
                  label="选择维度"
                  name="matrix2Id"
                  width="sm"
                  rules={[
                    {
                      required: true,
                      message: '请选择维度',
                    },
                  ]}
                  fieldProps={{
                    labelInValue: true,
                  }}
                  disabled={!matrix1Id}
                  onChange={handleSecondSelectChange}
                  request={async () => {
                    if (!matrix1Id?.key) {
                      return [];
                    }
                    return fetchMatrixListByPid({
                      pid: matrix1Id.key,
                    }).then(({ data }) => {
                      return data.map((item) => {
                        return {
                          label: item.title,
                          value: item.id,
                        };
                      });
                    });
                  }}
                />
              );
            }}
          </ProFormDependency>
          <ProFormDependency name={['matrix1Id', 'matrix2Id']}>
            {({ matrix2Id }) => {
              return (
                <ProFormSelect
                  params={{
                    key: matrix2Id?.value,
                  }}
                  label="选择子维度"
                  name="matrix3Id"
                  width="sm"
                  rules={[
                    {
                      required: true,
                      message: '请选择子维度',
                    },
                  ]}
                  fieldProps={{
                    labelInValue: true,
                  }}
                  disabled={!matrix2Id}
                  request={async () => {
                    if (!matrix2Id?.key) {
                      return [];
                    }
                    return fetchMatrixListByPid({
                      pid: matrix2Id.key,
                    }).then(({ data }) => {
                      return data.map((item) => {
                        return {
                          label: item.title,
                          value: item.id,
                        };
                      });
                    });
                  }}
                />
              );
            }}
          </ProFormDependency>
        </ProForm.Group>
      </Row>
      <Row gutter={8}>
        <Col span={12}>
          <ProFormDigit
            rules={[{ validator: checkPositiveInteger }]}
            fieldProps={{
              min: 1, // 最小值
              max: 99999, // 最大值
            }}
            label="排序"
            name="sort"
          />
        </Col>
      </Row>
    </DrawerForm>
  );
};

export default AddTargetModal;
