/* eslint-disable guard-for-in */
import { DrawerForm, FormInstance } from '@ant-design/pro-components';
import { message } from 'antd';
import React from 'react';
import AddRoleForm from './addRoleForm';

interface IProps {
  modalOpen: boolean;
  setModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
  form: FormInstance;
  currentRow?: {
    mobile: string;
    name: string;
    id: number;
  };
  isDetail?: string;
  handleAdd: (params: { name: string; mobile: string; id?: string }) => Promise<void>;
}

const Add = ({ form, modalOpen, setModalOpen, currentRow, handleAdd }: IProps) => {
  const handleFinish = async (params: { name: string; mobile: string; id?: string }) => {
    await handleAdd(params);
  };
  const onFinishFailed = () => {
    message.error('表单校验失败，请检查输入');
  };
  return (
    <DrawerForm
      title={!currentRow?.id ? '新建用户' : '编辑用户'}
      width="1200px"
      form={form}
      open={modalOpen}
      drawerProps={{
        maskClosable: false,
      }}
      initialValues={{
        ...currentRow,
      }}
      onOpenChange={setModalOpen}
      onFinish={handleFinish}
      onFinishFailed={onFinishFailed}
    >
      <AddRoleForm
        {...{
          form,
          modalOpen,
          currentRow,
        }}
      />
    </DrawerForm>
  );
};

export default Add;
