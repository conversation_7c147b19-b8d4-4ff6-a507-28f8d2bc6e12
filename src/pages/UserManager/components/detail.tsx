/* eslint-disable guard-for-in */
import { fetchUserManagerDetail } from '@/services/api';
import { PageContainer, ProForm } from '@ant-design/pro-components';
import { Card, Descriptions, Form } from 'antd';
import React, { useEffect, useState } from 'react';
import { useParams } from 'umi';
import Roles from './addRoleForm';

const detailInfo = [
  {
    title: '名称',
    dataIndex: 'name',
  },
  {
    title: '用户昵称',
    dataIndex: 'nickname',
  },
  {
    title: '手机号码',
    dataIndex: 'mobile',
  },
  {
    title: '职务',
    dataIndex: 'post',
  },
  {
    title: '状态',
    dataIndex: 'state',
    render: (text) => {
      return text === 1 ? '激活' : '已关闭';
    },
  },
  {
    title: '备注',
    dataIndex: 'note',
  },
];
const SchoolDetail: React.FC = () => {
  const { id } = useParams();

  const [detailData, setDetailData] = useState<any>({});

  useEffect(() => {
    if (id) {
      Promise.all([fetchUserManagerDetail(id)])
        .then((result) => {
          setDetailData(result[0]?.data);
        })
        .catch((e) => {
          console.log('%c Line:21 🍒 e', 'color:#ffdd4d', e);
        });
    }
  }, []);

  const [form] = Form.useForm();
  return (
    <PageContainer>
      <Card bordered={false}>
        <Descriptions
          // title="用户信息"
          style={{
            marginBottom: 32,
          }}
          column={2}
        >
          {detailInfo?.map((item) => {
            const value = detailData[item.dataIndex];
            return (
              <Descriptions.Item key={item.title} label={item.title}>
                {item.render ? item.render(value) : value || '-'}
              </Descriptions.Item>
            );
          })}
        </Descriptions>
      </Card>
      <Card>
        <ProForm readonly={true} form={form} submitter={false}>
          <Roles modalOpen={true} form={form} currentRow={{ id: id || '' }} isDetail={true} />
        </ProForm>
      </Card>
    </PageContainer>
  );
};

export default SchoolDetail;
