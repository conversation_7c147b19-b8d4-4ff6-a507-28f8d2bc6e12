/* eslint-disable guard-for-in */
import { fetchClassList, fetchDictionaryList, fetchSchoolList } from '@/services/apis';
import { getClassByScholl, getUserRoleDetail } from '@/services/user';
import { CloseCircleOutlined } from '@ant-design/icons';
import { FormInstance, ProFormGroup, ProFormList, ProFormSelect } from '@ant-design/pro-components';
import { useEffect, useState } from 'react';

interface IProps {
  modalOpen: boolean;
  form: FormInstance;
  currentRow?: {
    mobile?: string;
    name?: string;
    id: number | string;
  };
  isDetail?: boolean;
}
const getSchool = async () => {
  try {
    const res = await fetchSchoolList({
      current: 1,
      pageSize: 100,
    });
    return (
      res?.data?.map((item) => {
        return {
          ...item,
          label: item.title,
          value: `${item.id}`,
        };
      }) || []
    );
  } catch {
    return [];
  }
};

const getRole = async () => {
  try {
    const res = await fetchDictionaryList({
      current: 1,
      pageSize: 100,
      category: 21,
    });
    return (
      res?.data?.map((item) => {
        return {
          ...item,
          label: item.value,
          // value: `${item.id}`,
          value: item.id,
        };
      }) || []
    );
  } catch {
    return [];
  }
};

const getDetail = async (id) => {
  if (!id) {
    return {};
  }
  try {
    const res = await getUserRoleDetail(id);
    return res?.data;
  } catch {
    return {};
  }
};

const getClassBySchools = async (ids: string[]) => {
  try {
    const res = await getClassByScholl({
      schoolIds: ids,
    });
    return res?.data;
  } catch {
    return {};
  }
};

const Add = ({ form, modalOpen, currentRow, isDetail }: IProps) => {
  const [role, setRole] = useState([]);
  const [school, setSchool] = useState([]);
  const [classId, setClassId] = useState([]);

  useEffect(() => {
    if (modalOpen) {
      form.setFieldsValue({
        mobile: currentRow?.mobile,
        name: currentRow?.name,
      });
      Promise.all([getRole(), getSchool(), getDetail(currentRow?.id)]).then(async (res) => {
        setRole(res[0]);
        setSchool(res[1]);
        if (isDetail) {
          const _class = await getClassBySchools(res[2]?.map((item) => item.schoolId));
          const classIds = _class?.map((item) => ({
            ...item,
            label: item.title,
            // value: `${item.id}`,
            value: item.id,
          }));
          setClassId(classIds || []);
        }
        setTimeout(() => {
          form?.setFieldsValue({
            roles: res[2],
          });
        });
      });
    }
  }, [modalOpen]);

  return (
    <ProFormList
      name="roles"
      label="角色信息"
      copyIconProps={false}
      deleteIconProps={{
        Icon: CloseCircleOutlined,
        tooltipText: '不需要这行了',
      }}
    >
      <ProFormGroup key="group">
        <ProFormSelect width={340} name="schoolId" showSearch label="学校" options={school} />
        <ProFormSelect
          name="classIds"
          width={280}
          label="班级"
          dependencies={['schoolId']}
          fieldProps={{
            mode: 'multiple',
          }}
          request={async (params) => {
            if (isDetail) {
              return classId;
            }
            if (!params?.schoolId) {
              return [];
            }

            return fetchClassList({
              current: 1,
              pageSize: 100,
              schoolId: params?.schoolId,
            })
              .then((res) => {
                return (
                  res?.data?.map((item) => {
                    return {
                      ...item,
                      label: item.title,
                      value: item.id,
                    };
                  }) || []
                );
              })
              .catch(() => {
                return [];
              });
          }}
        />
        <ProFormSelect
          width={320}
          name="roleIds"
          showSearch
          label="角色"
          fieldProps={{
            mode: 'multiple',
          }}
          options={role}
        />
      </ProFormGroup>
    </ProFormList>
  );
};

export default Add;
