/* eslint-disable guard-for-in */
import {
  DrawerForm,
  FormInstance,
  ProFormText,
  ProFormTextArea,
  ProFormSelect,
} from '@ant-design/pro-components';
import { Col, Row, message } from 'antd';
import React, { useEffect } from 'react';

interface IProps {
  modalOpen: boolean;
  setModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
  form: FormInstance;
  currentRow?: {
    mobile: string;
    name: string;
    id: number;
    isTeacher: number;
    nickname: string;
    note: string;
    post: string;
  };
  handleAdd: (params: { name: string; mobile: string; id?: string }) => Promise<void>;
}
const Add = ({ form, modalOpen, setModalOpen, currentRow, handleAdd }: IProps) => {
  const handleFinish = async (params: { name: string; mobile: string; id?: string }) => {
    await handleAdd(params);
  };

  useEffect(() => {
    if (modalOpen) {
      form.setFieldsValue({
        mobile: currentRow?.mobile,
        name: currentRow?.name,
        isTeacher: currentRow?.isTeacher,
        nickname: currentRow?.nickname,
        note: currentRow?.note,
        post: currentRow?.post,
      });
    }
  }, [modalOpen]);

  const onFinishFailed = () => {
    message.error('表单校验失败，请检查输入');
  };

  return (
    <DrawerForm
      title={!currentRow?.id ? '新建用户' : '编辑用户'}
      width="800px"
      form={form}
      open={modalOpen}
      drawerProps={{
        maskClosable: false,
      }}
      initialValues={{
        ...currentRow,
      }}
      onOpenChange={setModalOpen}
      onFinish={handleFinish}
      onFinishFailed={onFinishFailed}
    >
      <Row gutter={8}>
        <Col span={12}>
          <ProFormText
            rules={[
              {
                required: true,
                message: '请填写用户名称',
              },
            ]}
            label="用户名称"
            name="name"
          />
        </Col>
        <Col span={12}>
          <ProFormText label="用户昵称" name="nickname" />
        </Col>
      </Row>
      <Row gutter={8}>
        <Col span={12}>
          <ProFormText
            rules={[
              {
                required: true,
                message: '请填写手机号码',
              },
            ]}
            label="手机号码"
            name="mobile"
          />
        </Col>
        <Col span={12}>
          <ProFormText label="职务" name="post" />
        </Col>
      </Row>
      <Row gutter={8}>
        <Col span={12}>
          <ProFormSelect
            options={[
              {
                value: 1,
                label: '是',
              },
              {
                value: 0,
                label: '否',
              },
            ]}
            width="lg"
            name="isTeacher"
            label="是否为教师"
          />
        </Col>
        <Col span={12}>
          <ProFormTextArea label="备注" name="note" />
        </Col>
      </Row>
    </DrawerForm>
  );
};

export default Add;
