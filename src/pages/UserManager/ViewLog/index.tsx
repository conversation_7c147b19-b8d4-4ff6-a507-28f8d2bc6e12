import { PageContainer, ProTable, ProFormDatePicker } from '@ant-design/pro-components';
import { getoPerateLog } from '@/services/api';
import { useParams } from 'umi';
import dayjs from 'dayjs';

const ViewLog: React.FC = () => {
  const { id } = useParams();

  const columns = [
    {
      title: '编号',
      dataIndex: 'operateUserId',
      valueType: 'number',
      initialValue: id,
      width: 80,
    },
    {
      title: '开始时间',
      dataIndex: 'startAt',
      valueType: 'text',
      width: 80,
      hideInTable: true,
      renderFormItem: () => {
        return <ProFormDatePicker format="YYYY-MM-DD HH:mm:ss" showTime name="startAt" />;
      },
    },
    {
      title: '结束时间',
      dataIndex: 'endAt',
      valueType: 'text',
      width: 80,
      hideInTable: true,
      renderFormItem: () => {
        return <ProFormDatePicker format="YYYY-MM-DD HH:mm:ss" showTime name="endAt" />;
      },
    },
    {
      title: '用户IP',
      dataIndex: 'remoteIp',
      valueType: 'text',
      hideInSearch: true,
      width: 80,
    },
    {
      title: '请求地址',
      dataIndex: 'requestUrl',
      valueType: 'text',
      hideInSearch: true,
      width: 80,
    },
    {
      title: '服务名',
      dataIndex: 'serviceName',
      valueType: 'text',
      hideInSearch: true,
      width: 80,
    },
    {
      title: '操作时间',
      dataIndex: 'operateTime',
      valueType: 'text',
      hideInSearch: true,
      width: 80,
      // sorter: true,
      render: (text: any) => {
        return dayjs(text).isValid() ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-';
      },
    },
  ];

  return (
    <PageContainer>
      <ProTable<API.PageParams>
        rowKey={(record, index) => index}
        search={{
          labelWidth: 'auto',
          defaultCollapsed: false,
          collapsed: false,
          collapseRender: false,
        }}
        request={(params) => getoPerateLog(params)}
        columns={columns}
      />
    </PageContainer>
  );
};
export default ViewLog;
