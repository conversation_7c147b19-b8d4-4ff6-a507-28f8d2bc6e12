/* eslint-disable guard-for-in */
import { getClassColumn, getSchoolColumn } from '@/components/CommonColumn';
import {
  addUserManager,
  fetchUserManagerList,
  updateUserManager,
  updateUserManagerState,
  updateUserRole,
  resetUserPassWord,
} from '@/services/api';
import { PlusOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { PageContainer, ProTable, ProFormDatePicker } from '@ant-design/pro-components';
import { Button, Form, message } from 'antd';
import React, { useRef, useState } from 'react';
import Add from '../components/add';
import AddRole from '../components/addRole';
import dayjs from 'dayjs';

const List: React.FC = () => {
  /**
   * @en-US Pop-up window of new window
   * @zh-CN 新建窗口的弹窗
   *  */
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const [modalRoleOpen, setModalRoleOpen] = useState<boolean>(false);
  const [sortOrder, setSortOrder] = useState<string>('');

  const [currentRow, setCurrentRow] = useState<any>({});

  const actionRef = useRef<ActionType>();

  const [form] = Form.useForm();
  const role = Form.useForm();
  const roleForm = role[0];
  // 排序
  const onSotrTable = (PageContainer: any, filters: any, sorter: any) => {
    if (sorter && sorter.order) {
      const sort = sorter.order === 'ascend' ? 'asc' : 'desc';
      setSortOrder(sort);
    }
    if (!sorter.order) setSortOrder('');
  };
  const handleModalOpen = ({ type, record }) => {
    form.resetFields();
    setCurrentRow({});

    if (type === 'edit') {
      console.log(record);
      setCurrentRow(record);
    }

    setModalOpen(true);
  };

  // 更新用户
  const handleAdd = async (value: { name: string; mobile: string; id?: string }) => {
    try {
      let res: API.AdminResponse;
      if (currentRow.id) {
        res = await updateUserManager({
          ...value,
          id: currentRow?.id,
        });
      } else {
        res = await addUserManager({
          ...value,
        });
      }
      if (res?.status === 0) {
        message.success('更新成功');
        setModalOpen(false);
        if (actionRef.current) {
          actionRef.current.reload();
        }
      }
    } catch (error) {
      message.error(error?.message || '更新失败，请重试');
    }
  };

  const handleState = async ({ record }) => {
    try {
      let res: API.AdminResponse = await updateUserManagerState({
        id: record.id,
        state: record.state === 1 ? 0 : 1,
      });

      if (res?.status === 0) {
        message.success('更新成功');
        setModalOpen(false);
        if (actionRef.current) {
          actionRef.current.reload();
        }
      }
    } catch (error) {
      message.error(error?.message || '更新失败，请重试');
    }
  };

  const handleModalRoleOpen = ({ type, record }) => {
    roleForm.resetFields();
    setCurrentRow({});

    if (type === 'edit') {
      setCurrentRow(record);
    }

    setModalRoleOpen(true);
  };
  const handleAddRole = async (params) => {
    try {
      let res: API.AdminResponse = await updateUserRole({
        userId: currentRow.id,
        ...params,
      });

      if (res?.status === 0) {
        message.success('更新成功');
        setModalRoleOpen(false);
        if (actionRef.current) {
          actionRef.current.reload();
        }
      }
    } catch (error) {
      message.error(error?.message || '更新失败，请重试');
    }
  };
  // 重置密码
  const handlResetPassword = async ({ record }) => {
    console.log(record);
    let param = { userId: record.id };
    let res = await resetUserPassWord(param);
    if (res?.status === 0) {
      message.success('重置成功');
      if (actionRef.current) {
        actionRef.current.reload();
      }
      return;
    }
    message.error(res?.message || '重置失败，请重试');
  };

  const stateData: any = {
    '-1': '全部',
    '1': '激活',
    '0': '已关闭',
  };

  const columns: ProColumns<API.TUserManager>[] = [
    {
      title: '编号',
      dataIndex: 'id',
      valueType: 'text',
      width: 80,
    },
    {
      title: '名称',
      dataIndex: 'name',
      valueType: 'text',
      // hideInSearch: true,
      width: 80,
    },
    {
      title: '用户昵称',
      dataIndex: 'nickname',
      valueType: 'text',
      hideInSearch: true,
      width: 80,
    },
    {
      title: '手机号码',
      dataIndex: 'mobile',
      valueType: 'text',
      // hideInSearch: true,
      width: 80,
    },
    {
      title: '所在学校',
      dataIndex: 'schoolTitle',
      valueType: 'text',
      // hideInSearch: true,
      width: 180,
      render: (text: any) => (
        // 把，替换成\n
        <div style={{ whiteSpace: 'pre-line' }}>{text.replace(/,/g, '\n')}</div>
      ),
    },
    {
      title: '是否为老师',
      dataIndex: 'isTeacher',
      valueType: 'text',
      align: 'center',
      search: false,
      // hideInSearch: true,
      width: 80,
      render: (text, record) => {
        return record?.isTeacher === 1 ? '是' : '否';
      },
    },
    {
      title: '职务',
      dataIndex: 'post',
      valueType: 'text',
      hideInSearch: true,
      width: 80,
    },
    {
      title: '注册时间',
      dataIndex: 'registeredAt',
      valueType: 'text',
      align: 'center',
      hideInSearch: true,
      width: 150,
      render: (text: any) => {
        return dayjs(text).isValid() ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-';
      },
    },
    {
      title: '最后操作时间',
      dataIndex: 'lastSignedAt',
      valueType: 'text',
      align: 'center',
      hideInSearch: true,
      width: 150,
      renderFormItem: () => {
        return <ProFormDatePicker format="YYYY-MM-DD HH:mm:ss" showTime name="lastSignedAt" />;
      },
      render: (text: any) => {
        return dayjs(text).isValid() ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-';
      },
      sorter: true,

      // sorter: (a, b) => {
      //   const aTime = new Date(a.lastSignedAt).getTime(); // 需要先转换成时间戳
      //   const bTime = new Date(b.lastSignedAt).getTime();
      //   return aTime - bTime;
      // },
    },

    {
      title: '开始时间',
      dataIndex: 'startAt',
      valueType: 'text',
      align: 'center',
      width: 150,
      hideInTable: true,
      renderFormItem: () => {
        return <ProFormDatePicker format="YYYY-MM-DD HH:mm:ss" showTime name="startAt" />;
      },
    },
    {
      title: '结束时间',
      dataIndex: 'entAt',
      valueType: 'text',
      align: 'center',
      width: 150,
      hideInTable: true,
      renderFormItem: () => {
        return <ProFormDatePicker format="YYYY-MM-DD HH:mm:ss" showTime name="entAt" />;
      },
    },
    {
      title: '状态',
      dataIndex: 'state',
      valueType: 'select',
      initialValue: '1',
      width: 80,
      valueEnum: stateData,
      render: (text, record) => {
        return record.state === 1 ? '激活' : '已关闭';
      },
    },
    {
      title: '备注',
      dataIndex: 'note',
      valueType: 'text',
      hideInSearch: true,
      width: 80,
    },
    {
      title: '操作',
      ellipsis: true,
      dataIndex: 'action',
      key: 'action',
      valueType: 'option',
      search: false,
      width: '24em',
      render: (_, record) => [
        <Button
          type="primary"
          key="editUser1"
          size="small"
          href={`/userManager/viewlog/${record?.id}`}
          target="_blank"
        >
          查看日志
        </Button>,
        <Button
          size="small"
          type="primary"
          key="editUser2"
          onClick={() => {
            handleModalOpen({ type: 'edit', record });
          }}
        >
          编辑用户
        </Button>,
        <Button
          size="small"
          danger
          key="editUser3"
          onClick={() => {
            handleState({ record });
          }}
        >
          {record.state === 1 ? '关闭' : '激活'}
        </Button>,
        <Button
          size="small"
          type="default"
          key="editUser4"
          onClick={() => {
            handleModalRoleOpen({ type: 'edit', record });
          }}
        >
          编辑角色
        </Button>,
        <Button
          size="small"
          type="link"
          key="detail"
          href={`/userManager/detail/${record?.id}`}
          target="_blank"
        >
          用户详情
        </Button>,
        <Button
          size="small"
          type="link"
          key="resetPassword"
          onClick={() => {
            handlResetPassword({ record });
          }}
          target="_blank"
        >
          重置密码
        </Button>,
      ],
    },

    getSchoolColumn(),
    getClassColumn(),
  ];

  return (
    <PageContainer>
      <ProTable<API.RuleListItem, API.PageParams>
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 'auto',
          defaultCollapsed: false,
          collapsed: false,
          collapseRender: false,
        }}
        columnsState={{
          persistenceKey: ``,
          persistenceType: 'localStorage',
          // value: columnsState,
          defaultValue: {
            nickname: { show: false },
            registeredAt: { show: false },
          },
        }}
        onChange={onSotrTable}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              handleModalOpen({ type: 'add' });
            }}
          >
            <PlusOutlined /> 新建
          </Button>,
        ]}
        request={(params) => {
          console.log(sortOrder, 'sortOrder');
          const from = {
            ...params,
          };
          if (sortOrder !== '') {
            from.sortOrder = sortOrder;
          }
          return fetchUserManagerList(from);
        }}
        columns={columns}
      />
      <Add
        form={form}
        modalOpen={modalOpen}
        setModalOpen={setModalOpen}
        currentRow={currentRow}
        // actionRef={actionRef}
        handleAdd={handleAdd}
      />
      <AddRole
        form={roleForm}
        modalOpen={modalRoleOpen}
        setModalOpen={setModalRoleOpen}
        currentRow={currentRow}
        // actionRef={actionRef}
        handleAdd={handleAddRole}
      />
    </PageContainer>
  );
};

export default List;
