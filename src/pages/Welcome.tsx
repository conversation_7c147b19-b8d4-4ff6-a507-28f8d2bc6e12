import { fetchCurrentUser } from '@/services/apis';
import { PageContainer } from '@ant-design/pro-layout';
import { Avatar, Card, Col, Divider, Row, Typography } from 'antd';
import React, { useEffect, useState } from 'react';

/**
 * steps
 * - 获取用户信息
 * - 获取已绑定的学校
 * - 获取已选择的学校 如果有则获取已绑定的班级
 *
 * 操作
 * - 选择学校 绑定学校同时清空已选择的班级  绑定成功之后获取班级列表
 * - 选择班级 绑定班级
 */
interface WelcomeState {
  currentUser: API.CurrentUser;
}
const Welcome: React.FC = () => {
  const [config, setConfig] = useState<WelcomeState>({
    currentUser: {},
  });

  useEffect(() => {
    try {
      /** 获取当前用户信息 */
      fetchCurrentUser().then((res) => {
        setConfig((c) => ({ ...c, currentUser: res.data }));
      });
    } catch (error) {
      console.log(error);
    }
  }, []);

  return (
    <PageContainer>
      <Row gutter={24}>
        <Col span={24}>
          <Card style={{ height: '500px' }}>
            <Divider orientation="left">用户信息</Divider>
            <Row align={'middle'} gutter={24}>
              <Col
                span={8}
                style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}
              >
                <Avatar
                  size={60}
                  src="https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png"
                />
              </Col>
              <Col span={16}>
                <Row style={{ height: '60px', display: 'flex', alignItems: 'center' }}>
                  <Typography.Text strong>{config.currentUser.nickname}</Typography.Text>
                </Row>
                <Row style={{ height: '60px', display: 'flex', alignItems: 'center' }}>
                  登录名:&nbsp;&nbsp; {config.currentUser.mobile}
                </Row>
                <Row style={{ height: '60px', display: 'flex', alignItems: 'center' }}>
                  账号状态:&nbsp;&nbsp; {config.currentUser.state === 1 ? '激活' : '已停用'}
                </Row>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>
    </PageContainer>
  );
};

export default Welcome;
