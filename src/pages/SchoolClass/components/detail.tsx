/* eslint-disable guard-for-in */
import { fetchClassDetail, fetchClassSta } from '@/services/api';
import { Grade } from '@/services/constants';
import { PageContainer, ProTable } from '@ant-design/pro-components';
import { Card, Descriptions, Divider, Image } from 'antd';
import { isEmpty } from 'lodash';
import React, { useEffect, useState } from 'react';
import { useParams } from 'umi';

const StatisticsCategory = {
  // 矩阵-商品总计数量
  Matrix: 1,
  // 班级商品-指标-商品数量
  ClassTarget: 5,
  // 班级商品-矩阵-商品数量
  ClassMatrix: 6,
};

const ClassDetail: React.FC = () => {
  const { id } = useParams();

  const [detailData, setDetailData] = useState<any>({});

  const [targetTable, setTargetTable] = useState<any>([]);
  const [matrixTable, setMatrixTable] = useState<any>([]);

  useEffect(() => {
    fetchClassDetail(Number(id)).then((res) => {
      if (res?.status === 0) {
        setDetailData(res?.data);
      }
    });
  }, []);

  useEffect(() => {
    fetchClassSta({
      current: 1,
      pageSize: 0,
      id: Number(id),
      category: StatisticsCategory.ClassTarget,
    }).then((res) => {
      if (res?.status === 0) {
        setTargetTable(res?.data);
      }
    });
  }, []);

  useEffect(() => {
    fetchClassSta({
      current: 1,
      pageSize: 0,
      id: Number(id),
      category: StatisticsCategory.ClassMatrix,
    }).then((res) => {
      if (res?.status === 0) {
        setMatrixTable(res?.data);
      }
    });
  }, []);

  const targetColumns = [
    {
      title: '编号',
      dataIndex: 'id',
      key: 'index',
    },
    {
      title: '指标名称',
      dataIndex: 'title',
      key: 'title',
    },
    {
      title: '商品数量',
      dataIndex: 'count',
      key: 'count',
    },
    {
      title: '商品名称',
      dataIndex: 'extro',
      key: 'extro',
      render: (result) => {
        return result?.join('、');
      },
    },
    {
      title: '领域',
      dataIndex: 'matrix1Title',
      key: 'matrix1Title',
    },
    {
      title: '维度',
      dataIndex: 'matrix2Title',
      key: 'matrix2Title',
    },
    {
      title: '子维度',
      dataIndex: 'matrix3Title',
      key: 'matrix3Title',
    },
  ];

  const matrixColumns = [
    {
      title: '编号',
      dataIndex: 'id',
      key: 'index',
    },
    {
      title: '矩阵名称',
      dataIndex: 'title',
      key: 'title',
    },
    {
      title: '维度',
      dataIndex: 'grade',
      key: 'title',
      render: (value) => {
        return Grade[value];
      },
      search: false,
    },
    {
      title: '商品数量',
      dataIndex: 'count',
      key: 'count',
    },
    {
      title: '商品名称',
      dataIndex: 'extro',
      key: 'extro',
      render: (result) => {
        return result?.join('、');
      },
    },
  ];

  return (
    <PageContainer>
      <Card bordered={false}>
        <Descriptions
          title="班级信息"
          style={{
            marginBottom: 32,
          }}
        >
          <Descriptions.Item label="班级名称">{detailData?.title}</Descriptions.Item>
          <Descriptions.Item label="班级昵称">{detailData?.nickname}</Descriptions.Item>
          <Descriptions.Item label="英文名称">{detailData?.englishTitle}</Descriptions.Item>
          <Descriptions.Item label="学校">{detailData?.schoolTitle}</Descriptions.Item>
          <Descriptions.Item label="年级">
            {!isEmpty(detailData?.gradeTitles) ? detailData.gradeTitles.join('、') : '-'}
          </Descriptions.Item>
          <Descriptions.Item label="男生人数">{detailData?.boy}</Descriptions.Item>
          <Descriptions.Item label="女生人数">{detailData?.girl}</Descriptions.Item>
          <Descriptions.Item label="总人数人数">
            {detailData?.boy + detailData?.girl}
          </Descriptions.Item>
          <Descriptions.Item label="班级区域">
            {!isEmpty(detailData?.areas) ? detailData.areas.map((item) => item.value + '、') : '-'}
          </Descriptions.Item>
          <Descriptions.Item label="角色区或表演区主题">{detailData?.roleTheme}</Descriptions.Item>
          <Descriptions.Item label="特色区主题">{detailData?.specialArea}</Descriptions.Item>
          <Descriptions.Item label="班主任姓名">{detailData?.extra?.masterTitle}</Descriptions.Item>
          <Descriptions.Item label="班主任联系电话">{detailData?.extra?.mobile}</Descriptions.Item>
          <Descriptions.Item label="班主任最高学历">
            {detailData?.extra?.masterHigQua}
          </Descriptions.Item>
          <Descriptions.Item label="班主任第一学历">
            {detailData?.extra?.masterFirQua}
          </Descriptions.Item>
          <Descriptions.Item label="班主任教龄">{detailData?.teaLen}</Descriptions.Item>
          <Descriptions.Item label="配班1姓名">
            {detailData?.extra?.teacher1Title}
          </Descriptions.Item>
          <Descriptions.Item label="配班1联系电话">
            {detailData?.extra?.teacher1Mobile}
          </Descriptions.Item>
          <Descriptions.Item label="配班2姓名">
            {detailData?.extra?.teacher2Title}
          </Descriptions.Item>
          <Descriptions.Item label="配班2联系电话">
            {detailData?.extra?.teacher2Mobile}
          </Descriptions.Item>
        </Descriptions>
        <Divider
          style={{
            marginBottom: 32,
          }}
        />
        <div style={{ fontWeight: 500, fontSize: '16px', marginBottom: '16px' }}>关联指标</div>
        <ProTable
          style={{
            marginBottom: 32,
          }}
          pagination={false}
          search={false}
          options={false}
          toolBarRender={false}
          dataSource={targetTable}
          columns={targetColumns}
          rowKey="id"
        />
        <div style={{ fontWeight: 500, fontSize: '16px', marginBottom: '16px' }}>关联矩阵</div>
        <ProTable
          style={{
            marginBottom: 32,
          }}
          pagination={false}
          search={false}
          options={false}
          toolBarRender={false}
          dataSource={matrixTable}
          columns={matrixColumns}
          rowKey="id"
        />
        <Descriptions
          title="预览图"
          style={{
            marginBottom: 32,
          }}
        >
          <Descriptions.Item label="">
            <Image width={100} src={detailData?.header?.uri} preview />
          </Descriptions.Item>
        </Descriptions>
        <Descriptions
          title="详情图"
          style={{
            marginBottom: 32,
          }}
        >
          <Descriptions.Item label="">
            {detailData?.sections?.map((item: any) => (
              <div key={item?.id} style={{ marginRight: '20px', marginBottom: '20px' }}>
                <Image width={100} src={item?.uri} preview />
              </div>
            ))}
          </Descriptions.Item>
        </Descriptions>
      </Card>
    </PageContainer>
  );
};

export default ClassDetail;
