/* eslint-disable guard-for-in */
import UploadImageComponent from '@/components/UploadImage';
import { fetchClassDetail, fetchEnum, fetchSchoolList } from '@/services/api';
import { DictionaryCategory } from '@/services/constants';
import { fetchClient } from '@/services/fileUpload';
import { checkArea, checkPositiveInteger, debounceSearch, phoneValidator } from '@/services/utils';
import {
  DrawerForm,
  ProFormDatePicker,
  ProFormDigit,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { Col, Row, message } from 'antd';
import { isEmpty } from 'lodash';
import React, { useEffect, useState } from 'react';

const AddClassModal: React.FC = ({ form, modalOpen, setModalOpen, currentRow, handleAdd }) => {
  const [client, setClient] = useState<any>();

  const [headerList, setHeaderList] = useState<API.FileItem[]>([]);
  const [sectionList, setSectionList] = useState<API.FileItem[]>([]);

  const { initialState } = useModel('@@initialState');
  const { dictionaryList } = initialState || {};

  // 年级列表
  const [grades, setGrades] = useState<any>([]);

  // 区域列表
  const [areas, setAreas] = useState<any>([]);

  // 学历列表
  const [degrees, setDegrees] = useState<any>([]);

  const [term, setTerm] = useState([])
  

  useEffect(() => {
    fetchEnum().then(res => {
      const option = Object.keys( res?.data?.SubjectTermEnum || {}).map(item => {
        return {
          label: res?.data?.SubjectTermEnum[item],
          value: parseInt(item)
        }
      });
      setTerm(option);
    })
    if (!isEmpty(dictionaryList)) {

      const result = dictionaryList?.map((item) => {
        return {
          value: item?.id,
          label: item?.value,
          category: item?.category,
        };
      });

      const _grades = result.filter((item) => item.category === DictionaryCategory.Grade);
      const _areas = result.filter((item) => item.category === DictionaryCategory.Area);
      const _degrees = result.filter(
        (item) => item.category === DictionaryCategory.TeacherEducation,
      );
      setGrades(_grades);
      setAreas(_areas);
      setDegrees(_degrees);

    }
  }, []);

  useEffect(() => {
    if (!!currentRow?.id && modalOpen) {
      fetchClassDetail(currentRow?.id).then((res) => {
        if (res?.status === 0) {
          const { data } = res;

          if (data?.header?.id) {
            const file = {
              id: data.header.id,
              uri: data.header.uri,
              filename: data.header.filename,
            };
            setHeaderList([file]);
          } else {
            setHeaderList([]);
          }

          if (data?.sections?.length) {
            const files = data.sections.map((item) => {
              return {
                id: item.id,
                uri: item.uri,
                filename: item.filename,
              };
            });
            setSectionList(files);
          } else {
            setSectionList([]);
          }

          form.setFieldsValue({
            nickname: data?.nickname,
            englishTitle: data?.englishTitle,
            startYear: String(data.startYear),
            sort: data?.sort,
            schoolId: { label: data?.schoolTitle, value: data?.schoolId },
            gradeIds: data?.gradeIds,
            areaIds: data?.area,
            boy: data?.boy,
            girl: data?.girl,
            roleTheme: data?.roleTheme,
            specialArea: data?.specialArea,
            term: data?.term,
            extra: {
              ...data?.extra,
            },
          });
        }
      });
    } else {
      setHeaderList([]);
      setSectionList([]);
    }
  }, [modalOpen]);

  useEffect(() => {
    fetchClient(setClient);
  }, []);

  const handleFinish = async (value) => {
    const result = {
      ...value,
      schoolId: value?.schoolId?.value,
    };

    if (headerList?.length) {
      result['headerId'] = headerList?.[0]?.id;
    }

    if (sectionList?.length) {
      result['sectionIds'] = sectionList?.map((item) => item?.id);
    }

    await handleAdd(result);
  };

  const onFinishFailed = () => {
    message.error('表单校验失败，请检查输入');
  };

  return (
    <DrawerForm
      title={!currentRow?.id ? '新建班级' : '编辑班级'}
      width="800px"
      form={form}
      open={modalOpen}
      drawerProps={{
        maskClosable: false,
      }}
      onOpenChange={setModalOpen}
      onFinish={handleFinish}
      onFinishFailed={onFinishFailed}
    >
      <Row gutter={8}>
        <Col span={12}>
          <ProFormText label="班级昵称" name="nickname" tooltip="此为班级昵称，比如：小熊班" />
        </Col>
        <Col span={12}>
          <ProFormText label="英文名称" name="englishTitle" />
        </Col>
        <Col span={12}>
          <ProFormDatePicker.Year
            width={'lg'}
            rules={[
              {
                required: true,
                message: '请选择入学年份',
              },
            ]}
            label="入学年份"
            name="startYear"
            tooltip="用于计算当前班级所在年级，8月1日后切换。比如入学年份为2024年的小班，则2025年8月2日，此班级升级为中班。"
          />
        </Col>
        <Col span={12}>
          <ProFormDigit
            rules={[
              { validator: checkPositiveInteger },
              {
                required: true,
                message: '请输入班级序号',
              },
            ]}
            fieldProps={{
              min: 1, // 最小值
              max: 99, // 最大值
            }}
            label="班级序号"
            name="sort"
            tooltip="用于展示班级在当前年级中的排序，比如小（1）班即为1，中（2）班即为2"
          />
        </Col>
        <Col span={12}>
          <ProFormSelect
            rules={[
              {
                required: true,
                message: '请选择关联学校',
              },
            ]}
            name="schoolId"
            label="关联学校"
            showSearch
            fieldProps={{
              labelInValue: true,
            }}
            request={async (value) => {
              return new Promise((resolve) => {
                debounceSearch(value, fetchSchoolList, resolve);
              });
            }}
          />
        </Col>
        <Col span={12} className="col-select-tooltip-red">
          <ProFormSelect
            rules={[
              {
                required: true,
                message: '请选择年级可选项',
              },
            ]}
            label="年级可选项"
            name="gradeIds"
            options={grades}
            fieldProps={{
              mode: 'multiple',
            }}
            tooltip="本班级按年升级可选项，比如首年为小班，则下一年为中班"
          />
        </Col>
        <Col span={12}>
          <ProFormDigit
            rules={[{ validator: checkPositiveInteger }]}
            fieldProps={{
              min: 0, // 最小值
              max: 99, // 最大值
            }}
            label="男生人数"
            name="boy"
          />
        </Col>
        <Col span={12}>
          <ProFormDigit
            rules={[{ validator: checkPositiveInteger }]}
            fieldProps={{
              min: 0, // 最小值
              max: 99, // 最大值
            }}
            label="女生人数"
            name="girl"
          />
        </Col>
        <Col span={12}>
          <ProFormSelect
            label="班级区域"
            name="areaIds"
            options={areas}
            fieldProps={{
              mode: 'multiple',
            }}
          />
        </Col>
        <Col span={12}>
          <ProFormSelect
            label="当前学期"
            name="term"
            options={term}
          />
        </Col>
        <Col span={12}>
          <ProFormText label="角色区或表演区主题" name="roleTheme" />
        </Col>
        <Col span={12}>
          <ProFormText label="特色区主题" name="specialArea" />
        </Col>
        <Col span={12}>
          <ProFormText
            rules={[
              {
                required: true,
                message: '请填写班主任姓名',
              },
            ]}
            label="班主任姓名"
            name={['extra', 'masterTitle']}
          />
        </Col>
        <Col span={12}>
          <ProFormSelect
            label="班主任最高学历（全日制）"
            name={['extra', 'masterHigQua']}
            options={degrees}
          />
        </Col>
        <Col span={12}>
          <ProFormSelect
            label="班主任第一学历（全日制）"
            name={['extra', 'masterFirQua']}
            options={degrees}
          />
        </Col>
        <Col span={12}>
          <ProFormDatePicker.Year
            width={'lg'}
            label="班主任任教年份"
            name={['extra', 'masterStartYear']}
            tooltip="从当年1月1日开始计算"
          />
        </Col>
        <Col span={12}>
          <ProFormText
            rules={[
              {
                required: true,
                message: '请填写班主任联系电话',
              },
              {
                validator: phoneValidator,
              },
            ]}
            label="班主任联系电话"
            name={['extra', 'masterMobile']}
          />
        </Col>
        <Col span={12}>
          <ProFormText label="配班1姓名" name={['extra', 'teacher1Title']} />
        </Col>
        <Col span={12}>
          <ProFormText
            rules={[
              {
                validator: phoneValidator,
              },
            ]}
            label="配班1联系电话"
            name={['extra', 'teacher1Mobile']}
          />
        </Col>
        <Col span={12}>
          <ProFormText label="配班2姓名" name={['extra', 'teacher2Title']} />
        </Col>
        <Col span={12}>
          <ProFormText
            rules={[
              {
                validator: phoneValidator,
              },
            ]}
            label="配班2联系电话"
            name={['extra', 'teacher2Mobile']}
          />
        </Col>
        <Col span={12}>
          <ProFormDigit
            rules={[{ validator: checkArea }]}
            label="班级面积"
            name={['extra', 'area']}
          />
        </Col>
      </Row>

      <Row>
        <UploadImageComponent
          key="upload"
          fileName="headerId"
          label="预览图"
          max={1}
          client={client}
          fileList={headerList}
          setClient={setClient}
          setFileList={setHeaderList}
          accept=".png,.jpg,.jpeg,.gif"
        />
      </Row>
      <Row>
        <UploadImageComponent
          key="upload"
          fileName="sectionIds"
          label="详情图"
          max={10}
          client={client}
          fileList={sectionList}
          setClient={setClient}
          setFileList={setSectionList}
          accept=".png,.jpg,.jpeg,.gif"
        />
      </Row>
    </DrawerForm>
  );
};

export default AddClassModal;
