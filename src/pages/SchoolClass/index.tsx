/* eslint-disable guard-for-in */
import { timeColumn } from '@/components/ColumnRender';
import {
  addClass,
  fetchClassList,
  fetchEnum,
  fetchSchoolList,
  updateClass,
  updateClassState,
} from '@/services/api';
import { DictionaryCategory } from '@/services/constants';
import { debounceSearch } from '@/services/utils';
import { PlusOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import {
  PageContainer,
  ProFormDatePicker,
  ProFormSelect,
  ProFormText,
  ProTable,
} from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { Button, Form, Image, message } from 'antd';
import { isEmpty, keyBy } from 'lodash';
import moment from 'moment';
import React, { useEffect, useRef, useState } from 'react';
import AddProductModal from './components/add';

const SchoolClass: React.FC = () => {
  /**
   * @en-US Pop-up window of new window
   * @zh-CN 新建窗口的弹窗
   *  */
  const [modalOpen, setModalOpen] = useState<boolean>(false);

  const [currentRow, setCurrentRow] = useState<any>({});

  const actionRef = useRef<ActionType>();

  const [form] = Form.useForm();

  const { initialState } = useModel('@@initialState');
  const { dictionaryList } = initialState || {};

  // 年级列表
  const [grades, setGrades] = useState<any>([]);

  // 区域列表
  const [areas, setAreas] = useState<any>([]);

  // 学历列表
  const [degrees, setDegrees] = useState<any>([]);

  // const [term, setTerm] = useState([])
  const [term, setTerm] = useState([])
  

  useEffect(() => {
    fetchEnum().then(res => {
      const option = Object.keys( res?.data?.SubjectTermEnum || {}).map(item => {
        return {
          label: res?.data?.SubjectTermEnum[item],
          value: parseInt(item)
        }
      });
      setTerm(option);
    })
    if (!isEmpty(dictionaryList)) {
      const result = dictionaryList?.map((item) => {
        return {
          value: item?.id,
          label: item?.value,
          category: item?.category,
        };
      });

      const _grades = result.filter((item) => item.category === DictionaryCategory.Grade);
      const _areas = result.filter((item) => item.category === DictionaryCategory.Area);
      const _degrees = result.filter(
        (item) => item.category === DictionaryCategory.TeacherEducation,
      );

      setGrades(_grades);
      setAreas(_areas);
      setDegrees(_degrees);
    }
  }, []);

  const handleModalOpen = ({ type, record }) => {
    form.resetFields();
    setCurrentRow({});

    if (type === 'edit') {
      setCurrentRow(record);
    }

    setModalOpen(true);
  };

  // 新增配置项
  const handleAdd = async (value: { key: string; value: string; category: string }) => {
    try {
      let res: API.AdminResponse = {};

      if (!!currentRow?.id) {
        res = await updateClass({ ...value, id: currentRow?.id });
      } else {
        res = await addClass({ ...value });
      }

      if (res?.status === 0) {
        message.success(!currentRow?.id ? '新建成功' : '更新成功');
        setModalOpen(false);
        if (actionRef.current) {
          actionRef.current.reload();
        }
      }
    } catch (error) {
      console.log(error);
      message.error(
        !currentRow?.id
          ? error?.message || '新建失败，请重试'
          : error?.message || '更新失败，请重试',
      );
    }
  };

  // 删除配置项
  const handleDelete = async (record: any) => {
    const hide = message.loading('正在更新状态');

    try {
      await updateClassState({
        id: record?.id,
        state: record?.state === 0 ? 1 : 0,
      });
      hide();
      message.success('Update successfully and will refresh soon');
      if (actionRef.current) {
        actionRef.current.reload();
      }
      return true;
    } catch (error) {
      hide();
      message.error(error?.message || 'Update failed, please try again');
      return false;
    }
  };



  const columns: ProColumns<API.RuleListItem>[] = [
    {
      title: '操作',
      ellipsis: true,
      dataIndex: 'action',
      key: 'action',
      valueType: 'option',
      width: '14em',
      search: false,
      render: (_, record) => [
        <Button
          size="small"
          type="primary"
          key="edit"
          onClick={() => {
            handleModalOpen({ type: 'edit', record });
          }}
        >
          编辑
        </Button>,
        <Button
          size="small"
          type="primary"
          key="delete"
          danger={!!record?.state}
          onClick={async () => {
            await handleDelete(record);
          }}
        >
          {record?.state === 0 ? '启用' : '停用'}
        </Button>,
        <Button
          size="small"
          type="link"
          key="detail"
          href={`/class/detail/${record?.id}`}
          target="_blank"
        >
          查看详情
        </Button>,
        <Button
          size="small"
          type="link"
          key="detail"
          href={`/class-product/list/${record?.id}`}
          target="_blank"
        >
          查看商品
        </Button>,
      ],
    },
    {
      title: '编号',
      dataIndex: 'id',
      valueType: 'text',
      width: 80,
      search: false,
    },
    {
      title: '班级名称',
      dataIndex: 'title',
      valueType: 'text',
      width: 120,
    },
    {
      title: '班级昵称',
      dataIndex: 'nickname',
      valueType: 'text',
      width: 120,
    },
    {
      title: '英文名称',
      dataIndex: 'englishTitle',
      valueType: 'text',
      width: 120,
      search: false,
    },
    {
      title: '预览图',
      dataIndex: 'header',
      valueType: 'text',
      width: 120,
      search: false,
      render: (result) => {
        return <Image src={result?.uri} height={50} />;
      },
    },
    {
      title: '学校',
      dataIndex: 'schoolTitle',
      valueType: 'text',
      width: 80,
      search: false,
    },
    {
      title: '学校',
      dataIndex: 'schoolId',
      hideInTable: true,
      renderFormItem: () => {
        return (
          <ProFormSelect
            name="schoolId"
            showSearch
            request={async (value) => {
              return new Promise((resolve) => {
                debounceSearch(value, fetchSchoolList, resolve);
              });
            }}
          />
        );
      },
    },
    {
      title: '年级',
      dataIndex: 'gradeTitles',
      valueType: 'text',
      width: 80,
      search: false,
      render: (result) => {
        return !isEmpty(result) && result !== '-' ? result.join('、') : '-';
      },
    },

    {
      title: '年级可选项',
      dataIndex: 'gradeIds',
      hideInTable: true,
      renderFormItem: () => {
        return (
          <ProFormSelect
            name="gradeIds"
            fieldProps={{
              mode: 'multiple',
            }}
            options={grades}
          />
        );
      },
    },
    
    {
      title: '班级区域',
      dataIndex: 'area',
      valueType: 'text',
      width: 120,
      render: (result) => {
        return dictionaryList?.find((item) => item?.id === Number(result))?.value;
      },
      search: false,
    },
    {
      title: '班级区域',
      dataIndex: 'areaIds',
      hideInTable: true,
      renderFormItem: () => {
        return (
          <ProFormSelect
            name="areaIds"
            fieldProps={{
              mode: 'multiple',
            }}
            options={areas}
          />
        );
      },
    },
    {
      title: '学期',
      dataIndex: 'term',
      hideInForm: true,
      valueType: 'select',
      // valueEnum: {},
      fieldProps: {
        options: term,
      },
      render: (text,record) => {
        const _render = term.find(item => {
          return item.value == record.term
        })?.label
        return _render || '-';
      }
    },
    
    {
      title: '男生人数',
      dataIndex: 'boy',
      valueType: 'text',
      width: 80,
      search: false,
    },
    {
      title: '女生人数',
      dataIndex: 'girl',
      valueType: 'text',
      width: 80,
      search: false,
    },
    {
      title: '角色区或表演区主题',
      dataIndex: 'roleTheme',
      valueType: 'text',
      width: 120,
      search: false,
    },
    {
      title: '角色区或表演区主题',
      dataIndex: 'roleTheme',
      hideInTable: true,
      renderFormItem: () => {
        return <ProFormText name="roleTheme" />;
      },
    },
    {
      title: '特色区主题',
      dataIndex: 'specialArea',
      valueType: 'text',
      width: 120,
      search: false,
    },
    {
      title: '特色区主题',
      dataIndex: 'specialArea',
      hideInTable: true,
      renderFormItem: () => {
        return <ProFormText name="specialArea" />;
      },
    },
    {
      title: '班级面积',
      dataIndex: 'extra',
      valueType: 'text',
      width: 80,
      search: false,
      render: (_, record) => {
        return record?.extra?.area;
      },
    },
    {
      title: '班主任',
      dataIndex: 'extra',
      valueType: 'text',
      width: 120,
      search: false,
      render: (_, record) => {
        return `${record?.extra?.masterTitle} - ${record?.extra?.masterMobile}`;
      },
    },
    {
      title: '班主任学历',
      dataIndex: 'extra',
      valueType: 'text',
      width: 160,
      search: false,
      render: (_, record) => {
        return (
          <>
            {`最高学历：${record?.extra?.masterHigQua || '-'}；`}
            <br />
            {`第一学历：${record?.extra?.masterFirQua || '-'}`}
          </>
        );
      },
    },
    {
      title: '班主任教龄',
      dataIndex: 'teaLen',
      valueType: 'text',
      width: 120,
      search: false,
    },
    {
      title: '班主任姓名',
      dataIndex: 'masterTitle',
      hideInTable: true,
      renderFormItem: () => {
        return <ProFormText name="masterTitle" />;
      },
    },
    {
      title: '班主任最高学历',
      dataIndex: 'masterHigQua',
      hideInTable: true,
      renderFormItem: () => {
        return <ProFormSelect name="masterHigQua" options={degrees} />;
      },
    },
    {
      title: '班主任第一学历',
      dataIndex: 'masterFirQua',
      hideInTable: true,
      renderFormItem: () => {
        return <ProFormSelect name="masterFirQua" options={degrees} />;
      },
    },
    {
      title: '班主任任教年份上限',
      dataIndex: 'masterStartYearStart',
      hideInTable: true,
      renderFormItem: () => {
        return <ProFormDatePicker.Year name="masterStartYearStart" />;
      },
    },
    {
      title: '班主任任教年份下限',
      dataIndex: 'masterStartYearEnd',
      hideInTable: true,
      renderFormItem: () => {
        return <ProFormDatePicker.Year name="masterStartYearEnd" />;
      },
    },
    {
      title: '联系电话',
      dataIndex: 'masterMobile',
      hideInTable: true,
      renderFormItem: () => {
        return <ProFormText name="masterMobile" />;
      },
    },
    {
      title: '配班1',
      dataIndex: 'extra',
      valueType: 'text',
      width: 120,
      search: false,
      render: (_, record) => {
        return `${record?.extra?.teacher1Title || ''} - ${record?.extra?.teacher1Mobile || ''}`;
      },
    },
    {
      title: '配班2',
      dataIndex: 'extra',
      valueType: 'text',
      width: 120,
      search: false,
      render: (_, record) => {
        return `${record?.extra?.teacher2Title || ''} - ${record?.extra?.teacher2Mobile || ''}`;
      },
    },
    {
      title: '状态',
      dataIndex: 'state',
      // hideInForm: true,
      // search: false,
      width: 80,
      valueEnum: {
        0: { color: 'red', text: '停用', status: 'Default' },
        1: { color: 'green', text: '启用', status: 'Processing' },
      },
    },
    timeColumn,
  ];

  // 处理表格查询参数
  const beforeSearchSubmit = (params) => {
    return {
      ...params,
      masterStartYearStart:
        params.masterStartYearStart && moment(params.masterStartYearStart).format('YYYY'),
      masterStartYearEnd:
        params.masterStartYearEnd && moment(params.masterStartYearEnd).format('YYYY'),
    };
  };

  return (
    <PageContainer>
      <ProTable<API.RuleListItem, API.PageParams>
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 'auto',
          defaultCollapsed: false,
          collapsed: false,
          collapseRender: false,
        }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              handleModalOpen({ type: 'add' });
            }}
          >
            <PlusOutlined /> 新建
          </Button>,
        ]}
        request={fetchClassList}
        columns={columns}
        scroll={{ x: 3600 }}
        beforeSearchSubmit={beforeSearchSubmit}
      />
      <AddProductModal
        form={form}
        modalOpen={modalOpen}
        setModalOpen={setModalOpen}
        currentRow={currentRow}
        actionRef={actionRef}
        handleAdd={handleAdd}
      />
    </PageContainer>
  );
};

export default SchoolClass;
