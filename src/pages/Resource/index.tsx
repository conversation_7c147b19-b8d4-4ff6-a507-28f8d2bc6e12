import { timeColumn } from '@/components/ColumnRender';
import Player from '@/components/Player';
import UploadImageComponent from '@/components/UploadImage';
import { delResource, fetchSourceList } from '@/services/api';
import { fetchClient } from '@/services/fileUpload';
import { PlusOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ModalForm, PageContainer, ProTable } from '@ant-design/pro-components';
import { Button, Image, message, Modal } from 'antd';
import React, { useEffect, useRef, useState } from 'react';

/**
 *  Delete node
 * @zh-CN 删除节点
 *
 * @param selectedRows
 */
const handleDelete = async (record: Record<string, any>, reload: () => void) => {
  Modal.confirm({
    title: '是否确认删除',
    async onOk() {
      try {
        const hide = message.loading('正在删除');

        await delResource({
          id: record.id,
        });
        reload?.();
        hide();
        message.success('删除成功');
        return true;
      } catch (error) {
        hide();
        message.error('删除失败，请重试');
        return false;
      }
    },
  });
};

const ResourceList: React.FC = () => {
  /**
   * @en-US Pop-up window of new window
   * @zh-CN 新建窗口的弹窗
   *  */
  const [createModalOpen, handleModalOpen] = useState<boolean>(false);

  const [client, setClient] = useState<any>();

  const [fileList, setFileList] = useState<API.FileItem[]>([]);

  const actionRef = useRef<ActionType>();

  useEffect(() => {
    fetchClient(setClient);
  }, []);

  const columns: ProColumns<API.RuleListItem>[] = [
    {
      title: '编号',
      dataIndex: 'id',
      valueType: 'text',
      width: 80,
      search: false,
    },
    {
      title: '素材名称',
      dataIndex: 'filename',
      valueType: 'text',
      width: 120,
      // search: {
      //   transform: (value: any) => ({
      //     sourceName: `*${value}*`,
      //   }),
      // },
    },
    {
      title: '图片预览',
      dataIndex: 'imageInfo',
      valueType: 'text',
      width: 120,
      search: false,
      render: (dom, record) => {
        return <Player {...record}/>
        return <Image src={record.uri} height={50} />;
      },
    },
    timeColumn,
    {
      title: '操作',
      ellipsis: true,
      dataIndex: 'action',
      key: 'action',
      valueType: 'option',
      width: '14em',
      search: false,
      render: (_, record) => [
        <Button
          size="small"
          type="primary"
          key="delete"
          danger
          onClick={async () => {
            await handleDelete(record, actionRef.current?.reload);
          }}
        >
          删除
        </Button>,
      ],
    },
  ];

  return (
    <PageContainer>
      <ProTable<API.RuleListItem, API.PageParams>
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 'auto',
          defaultCollapsed: false,
          collapsed: false,
          collapseRender: false,
        }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              handleModalOpen(true);
            }}
          >
            <PlusOutlined /> 上传
          </Button>,
        ]}
        request={fetchSourceList}
        columns={columns}
      />
      <ModalForm
        title="上传资源"
        width="400px"
        open={createModalOpen}
        modalProps={{
          maskClosable: false,
        }}
        onOpenChange={handleModalOpen}
        onFinish={() => {
          handleModalOpen(false);
          if (actionRef.current) {
            actionRef.current.reload();
          }
        }}
      >
        <UploadImageComponent
          key="upload"
          max={1}
          client={client}
          fileList={fileList}
          setClient={setClient}
          setFileList={setFileList}
        />
      </ModalForm>
    </PageContainer>
  );
};

export default ResourceList;
