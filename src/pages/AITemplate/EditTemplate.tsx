import UploadImageComponent from '@/components/UploadImage';
import { createAITemplate, updateAITemplate } from '@/services/aiTemplate';
import { fetchClient } from '@/services/fileUpload';
import {
  DrawerForm,
  ProCard,
  ProFormDependency,
  ProFormGroup,
  ProFormList,
  ProFormSelect,
  ProFormSwitch,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { message } from 'antd';
import type { FormInstance } from 'antd/es/form/hooks/useForm';
import { FC, useEffect, useState } from 'react';

type Props = {
  open: boolean;
  setOpen: (open: boolean) => void;
  form?: FormInstance<API.AITemplateListItem>;
  onChangeSuccess?: () => void;
  id?: number;
};

const INPUT_TYPE_OPTIONS = [
  {
    value: 'input',
    label: '文本框',
  },
  {
    value: 'select',
    label: '单选框',
  },
  {
    value: 'multiSelect',
    label: '多选框',
  },
  {
    value: 'upload',
    label: '上传框',
  },
];

const AI_TYPE_OPTIONS = [
  {
    value: 'shortConversation',
    label: '短问答',
  },
  {
    value: 'longConversation',
    label: '长问答',
  },
  {
    value: 'speechToText1Min',
    label: '60秒语音转写',
  },
  {
    value: 'speechToText1hour',
    label: '1小时语音转写',
  },
  {
    value: 'faceRecognition',
    label: '人脸识别',
  },
  {
    value: 'PPTGeneration',
    label: 'PPT生成',
  },
  {
    value: 'imageRecognition',
    label: '图像识别',
  },
  {
    value: 'imageGeneration',
    label: '图片生成',
  },
  {
    value: 'videoGeneration',
    label: '视频生成',
  },
];

const EditTemplate: FC<Props> = ({ id, open, setOpen, form, onChangeSuccess }) => {
  const [client, setClient] = useState<any>();
  const [iconList, setIconList] = useState<API.SyncFileData[]>([]);
  const isEdit = id !== undefined;

  useEffect(() => {
    fetchClient(setClient);
  }, [fetchClient, setClient]);

  useEffect(() => {
    const imgUrl = form?.getFieldValue('templateImageUrl');
    if (open && isEdit && !!imgUrl) {
      setIconList([
        {
          uri: imgUrl,
          filename: '模版图标',
          id: 0,
          url: imgUrl,
        },
      ]);
      return;
    }
    setIconList([]);
  }, [open, id]);

  return (
    <DrawerForm<API.AITemplateListItem>
      title={`${isEdit ? '编辑' : '新建'}模版`}
      open={open}
      form={form}
      grid
      drawerProps={{
        destroyOnClose: true,
      }}
      onOpenChange={setOpen}
      onFinish={async (values) => {
        const templateImageUrl = iconList[0]?.uri;
        const templatesForms = values.templatesForms.map((item) => ({
          ...item,
          extend: (item?.extend as unknown as string[])?.join?.(','),
          required: item.required ? 1 : 0,
        }));

        const res = isEdit
          ? await updateAITemplate({
              ...values,
              id,
              templateImageUrl: templateImageUrl || values.templateImageUrl,
              templatesForms,
            })
          : await createAITemplate({
              ...values,
              templateImageUrl,
              templatesForms,
            });

        if (res.status === 0) {
          onChangeSuccess?.();
          message.success('操作成功');
          return true;
        }
        message.error(res.message);
      }}
      rowProps={{ gutter: 16 }}
    >
      <ProFormText
        colProps={{
          span: 20,
        }}
        name="name"
        label="模版名称"
        required
        rules={[{ required: true, message: '请输入模版名称！' }]}
      />
      <ProFormSwitch
        colProps={{
          span: 4,
        }}
        initialValue={true}
        name="state"
        label="是否启用"
        convertValue={(value) => !!value}
        transform={(value) => (value ? 1 : 0)}
      />
      <ProFormTextArea name="applicationScenarios" label="应用场景" />

      <UploadImageComponent
        accept={'image/*'}
        key="templateImageUrl"
        fileName="templateImageUrl"
        client={client}
        label="模版图标"
        max={1}
        setClient={setClient}
        fileList={iconList}
        setFileList={setIconList}
        edit={isEdit}
      />
      <ProFormList
        rules={[
          {
            validator: async (_, value) => {
              if (!value || value?.length === 0) {
                return Promise.reject(new Error('请添加模版字段！'));
              }
            },
          },
        ]}
        required
        name="templatesForms"
        label="模版字段"
        itemRender={({ listDom, action }, { record }) => {
          return (
            <ProCard
              bordered
              extra={action}
              title={record?.name}
              style={{
                marginBlockEnd: 8,
              }}
            >
              {listDom}
            </ProCard>
          );
        }}
      >
        {() => {
          return (
            <>
              <ProFormGroup rowProps={{ gutter: 16 }}>
                <ProFormText
                  colProps={{
                    span: 10,
                  }}
                  name="keyName"
                  label="字段名"
                  rules={[{ required: true, message: '请输入字段名！' }]}
                />

                <ProFormSelect
                  colProps={{
                    span: 10,
                  }}
                  name="type"
                  label="输入类型"
                  options={INPUT_TYPE_OPTIONS}
                  required
                  rules={[{ required: true, message: '请选择输入类型！' }]}
                />
                <ProFormSwitch
                  colProps={{
                    span: 4,
                  }}
                  name="required"
                  label="是否必填"
                  convertValue={(value) => !!value}
                />
                <ProFormTextArea
                  colProps={{
                    span: 24,
                  }}
                  name="tip"
                  label="提示"
                  rules={[{ required: true, message: '请选择输入提示内容！' }]}
                />
                <ProFormTextArea
                  colProps={{
                    span: 24,
                  }}
                  name="defaultValue"
                  label="默认值"
                  rules={[{ required: true, message: '请选择输入默认值！' }]}
                />
              </ProFormGroup>
              <ProFormDependency name={['type']}>
                {({ type }) => {
                  if (type === 'select' || type === 'multiSelect') {
                    return (
                      <ProFormSelect
                        name="extend"
                        label="选项"
                        placeholder="请输入选项，多个选项用逗号分隔"
                        mode="tags"
                        rules={[{ required: true, message: '请输入选项！' }]}
                        convertValue={(value) => value?.split?.(',') || value}
                      />
                    );
                  }
                  return null;
                }}
              </ProFormDependency>
            </>
          );
        }}
      </ProFormList>
      <ProFormSelect name="aiType" label="AI类型" options={AI_TYPE_OPTIONS} />
      <ProFormTextArea
        name="content"
        label="模版内容"
        placeholder="请输入"
        fieldProps={{
          autoSize: { minRows: 3 },
        }}
        rules={[
          { required: true, message: '请输入模版内容！' },
          ({ getFieldValue }) => ({
            validateTrigger: 'onSubmit',
            validator(_, value) {
              const templateForms =
                (getFieldValue('templatesForms') as API.AITemplateListItem['templatesForms'])?.map(
                  (item) => item.keyName,
                ) ?? [];

              const isMatch = templateForms.every((item) => value.includes(`{${item}}`));

              if (isMatch) {
                return Promise.resolve();
              }
              return Promise.reject(new Error('模版字段与模版内容不匹配，请检查！'));
            },
          }),
        ]}
      />
    </DrawerForm>
  );
};

export default EditTemplate;
