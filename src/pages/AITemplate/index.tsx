import { deleteAITemplate, getAITemplateList, updateAITemplate } from '@/services/aiTemplate';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { Button, Form, message, Popconfirm } from 'antd';
import pick from 'lodash/pick';
import { useRef, useState } from 'react';
import EditTemplate from './EditTemplate';

/** 状态 */
export enum StateStatus {
  // 停用
  Off = 0,
  // 启用
  On = 1,
}

const AITemplate = () => {
  const [open, setOpen] = useState(false);
  const [templateId, setTemplateId] = useState<number>();
  const [form] = Form.useForm<API.AITemplateListItem>();
  const ref = useRef<ActionType>();

  const handleAction = () => {
    ref.current?.reload();
  };

  const toggleTemplateState = async (
    formValues: Partial<API.AITemplateListItem>,
    state: StateStatus,
  ) => {
    const res = await updateAITemplate({
      ...formValues,
      state,
    });

    if (res.status === 0) {
      message.success('操作成功');
      handleAction();
      return;
    }
    message.error(res.message);
  };

  const columns: ProColumns<API.AITemplateListItem>[] = [
    {
      dataIndex: 'name',
      title: '模版名称',
    },
    {
      dataIndex: 'applicationScenarios',
      title: '应用场景',
      hideInSearch: true,
    },
    {
      dataIndex: 'recordNum',
      title: '模板使用次数',
      width: 130,
      hideInSearch: true,
    },
    {
      dataIndex: 'tokens',
      title: '调用总token千字数',
      width: 150,
      hideInSearch: true,
      renderText: (text) => text / 1000,
    },
    {
      dataIndex: 'createdAt',
      title: '创建时间',
      key: 'createdAt',
      valueType: 'date',
      hideInSearch: true,
      width: 120,
    },
    {
      dataIndex: 'updatedAt',
      title: '更新时间',
      key: 'updatedAt',
      valueType: 'date',
      hideInSearch: true,
      width: 120,
    },
    {
      dataIndex: 'state',
      title: '状态',
      initialValue: '2',
      width: 120,
      valueEnum: {
        2: { text: '全部', status: 'Default' },
        1: { text: '已发布', status: 'Processing' },
        0: { text: '已停用', status: 'Default' },
      },
    },
    {
      title: '操作',
      key: 'option',
      valueType: 'option',
      width: 200,
      render: (_node, row) => {
        const formValues = pick(row, [
          'name',
          'content',
          'applicationScenarios',
          'state',
          'templatesForms',
          'aiType',
          'id',
          'templateImageUrl',
        ]);
        return [
          <Button
            size="small"
            type="link"
            key="edit"
            onClick={() => {
              form.setFieldsValue(formValues);
              setTemplateId(row.id);
              setOpen(true);
            }}
          >
            编辑
          </Button>,

          row.state === StateStatus.Off && (
            <Button
              size="small"
              type="link"
              key="release"
              onClick={() => toggleTemplateState(formValues, StateStatus.On)}
            >
              发布
            </Button>
          ),
          row.state === StateStatus.On && (
            <Button
              size="small"
              type="link"
              key="suspended"
              onClick={() => toggleTemplateState(formValues, StateStatus.Off)}
            >
              停用
            </Button>
          ),
          <Popconfirm
            key={'delete'}
            title="确定要删除这条模版吗？"
            onConfirm={async () => {
              const res = await deleteAITemplate(row.id!);

              if (res.status === 0) {
                message.success('操作成功');
                handleAction();
                return;
              }
            }}
          >
            <Button size="small" type="link" key="delete">
              删除
            </Button>
          </Popconfirm>,
        ];
      },
    },
  ];
  return (
    <PageContainer>
      <ProTable<API.AITemplateListItem>
        actionRef={ref}
        columns={columns}
        rowKey="id"
        toolBarRender={() => [
          <Button
            key="button"
            type="primary"
            onClick={() => {
              setTemplateId(undefined);
              form.setFieldValue('state', 1);
              setOpen(true);
            }}
          >
            新建模版
          </Button>,
        ]}
        request={async (params) => {
          const response = await getAITemplateList({
            current: params.current,
            pageSize: params.pageSize,
            state: params.state,
            name: params.name,
          });
          return {
            data: response.data,
            success: true,
            total: response.total,
          };
        }}
      />
      <EditTemplate
        open={open}
        form={form}
        setOpen={setOpen}
        id={templateId}
        onChangeSuccess={handleAction}
      />
    </PageContainer>
  );
};

export default AITemplate;
