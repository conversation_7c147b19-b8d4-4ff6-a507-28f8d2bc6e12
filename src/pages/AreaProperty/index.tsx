import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { PageContainer, ProTable, ModalForm, ProFormSelect } from '@ant-design/pro-components';
import { Button, message, Popconfirm } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import {
  queryAreaPropertyList,
  addAreaProperty,
  editAreaProperty,
  deleteAreaProperty,
  getMaterialAttr,
  getMatrix,
} from '@/services/areaProperty';
import { fetchUserSelectedSchoolsAndClasses } from '@/services/apis';
import { useModel } from '@umijs/max';
import { DictionaryCategory } from '@/services/constants';
import { isEmpty } from 'lodash';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';

const AreaPropertyList: React.FC = () => {
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<any>({});
  const [isEdit, setIsEdit] = useState<boolean>(false);
  const [areas, setAreas] = useState<any[]>([]);
  const [materialAttrs, setMaterialAttrs] = useState<any[]>([]);
  const [matrixList, setMatrixList] = useState<any[]>([]);
  const [currentSchoolId, setCurrentSchoolId] = useState<number>(0);

  const actionRef = useRef<ActionType>();
  const { initialState } = useModel('@@initialState');
  const { dictionaryList } = initialState || {};

  // 获取区域数据
  useEffect(() => {
    if (!isEmpty(dictionaryList)) {
      const result: any = dictionaryList?.map((item) => {
        return {
          value: item?.id,
          label: item?.value,
          category: item?.category,
        };
      });

      const _areas = result.filter((item: any) => item.category === DictionaryCategory.Area);
      setAreas(_areas);
    }
  }, [dictionaryList]);

  // 获取材料属性维度列表
  const fetchMaterialAttrs = async () => {
    try {
      const response = await getMaterialAttr({});
      if ((response as any)?.status === 0) {
        const data = (response as any)?.data || [];
        const formattedData = data.map((item: any) => ({
          value: item.dictCode,
          label: item.dictItemName,
        }));
        setMaterialAttrs(formattedData);
      }
    } catch (error) {
      console.error('获取材料属性维度失败:', error);
    }
  };

  // 获取核心经验子维度列表
  const fetchMatrixList = async () => {
    try {
      const response = await getMatrix({});
      if ((response as any)?.status === 0) {
        const data = (response as any)?.data || [];
        const formattedData = data.map((item: any) => ({
          value: item.dictCode,
          label: item.dictItemName,
        }));
        setMatrixList(formattedData);
      }
    } catch (error) {
      console.error('获取核心经验子维度失败:', error);
    }
  };

  // 获取当前用户选择的学校ID
  const fetchCurrentSchoolId = async () => {
    try {
      const response = await fetchUserSelectedSchoolsAndClasses();
      if ((response as any)?.status === 0) {
        const schoolId = (response as any)?.data?.currentSchoolId || 0;
        setCurrentSchoolId(schoolId);
      }
    } catch (error) {
      console.error('获取当前学校ID失败:', error);
    }
  };

  // 初始化下拉框数据
  useEffect(() => {
    fetchMaterialAttrs();
    fetchMatrixList();
    fetchCurrentSchoolId();
  }, []);

  // 处理新增
  const handleAdd = () => {
    setCurrentRow({});
    setIsEdit(false);
    setModalOpen(true);
  };

  // 处理编辑
  const handleEdit = (record: any) => {
    setCurrentRow(record);
    setIsEdit(true);
    setModalOpen(true);
  };

  // 处理删除
  const handleDelete = async (record: any) => {
    try {
      const response = await deleteAreaProperty({ id: record.id });
      if ((response as any)?.status === 0) {
        message.success('删除成功');
        actionRef.current?.reload();
      } else {
        message.error((response as any)?.message || '删除失败');
      }
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败');
    }
  };

  // 处理表单提交
  const handleSubmit = async (values: any) => {
    try {
      // 根据选择的区域ID找到对应的区域名称
      const selectedArea = areas.find((area) => area.value === values.area);
      const areaName = selectedArea?.label || '';

      const params: any = {
        area: areaName, // 传递区域名称而不是区域ID
        attributesIds: values.attributesIds?.join(',') || '',
        matrixIds: values.matrixIds?.join(',') || '',
        schoolId: currentSchoolId, // 使用获取到的学校ID
      };

      if (isEdit) {
        params.id = currentRow.id;
      }

      const response = isEdit ? await editAreaProperty(params) : await addAreaProperty(params);

      if ((response as any)?.status === 0) {
        message.success(isEdit ? '编辑成功' : '新增成功');
        setModalOpen(false);
        actionRef.current?.reload();
        return true;
      } else {
        message.error((response as any)?.message || (isEdit ? '编辑失败' : '新增失败'));
        return false;
      }
    } catch (error) {
      console.error(isEdit ? '编辑失败:' : '新增失败:', error);
      message.error(isEdit ? '编辑失败' : '新增失败');
      return false;
    }
  };

  const columns: ProColumns<any>[] = [
    {
      title: '编号',
      dataIndex: 'id',
      valueType: 'text',
      width: 80,
      search: false,
    },
    {
      title: '区域',
      dataIndex: 'area',
      valueType: 'text',
      width: 120,
    },
    {
      title: '材料属性维度',
      dataIndex: 'attributesIds',
      valueType: 'text',
      search: false,
      render: (_, record) => {
        if (!record.attributesIds) return '-';
        const ids = record.attributesIds.split(',');
        const names = ids.map((id: string) => {
          const attr = materialAttrs.find((item) => item.value.toString() === id);
          return attr?.label || id;
        });
        return names.join(', ');
      },
    },
    {
      title: '核心经验子维度',
      dataIndex: 'matrixIds',
      valueType: 'text',
      search: false,
      render: (_, record) => {
        if (!record.matrixIds) return '-';
        const ids = record.matrixIds.split(',');
        const names = ids.map((id: string) => {
          const matrix = matrixList.find((item) => item.value.toString() === id);
          return matrix?.label || id;
        });
        return names.join(', ');
      },
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      width: 150,
      render: (_, record) => [
        <Button
          key="edit"
          type="link"
          size="small"
          icon={<EditOutlined />}
          onClick={() => handleEdit(record)}
        >
          编辑
        </Button>,
        <Popconfirm
          key="delete"
          title="确定要删除这条记录吗？"
          onConfirm={() => handleDelete(record)}
          okText="确定"
          cancelText="取消"
        >
          <Button type="link" size="small" danger icon={<DeleteOutlined />}>
            删除
          </Button>
        </Popconfirm>,
      ],
    },
  ];

  return (
    <PageContainer>
      <ProTable<any>
        headerTitle="区域属性管理"
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 120,
        }}
        toolBarRender={() => [
          <Button type="primary" key="primary" icon={<PlusOutlined />} onClick={handleAdd}>
            新增
          </Button>,
        ]}
        request={async (params) => {
          const response = await queryAreaPropertyList({
            ...params,
            pageNo: params.current || 1,
            pageSize: params.pageSize || 10,
          });

          if ((response as any)?.status === 0) {
            const data = (response as any)?.data || {};
            const list = data.list || data.records || data || [];
            const total = data.total || data.totalCount || list.length;

            return {
              data: list,
              success: true,
              total: total,
            };
          } else {
            message.error((response as any)?.message || '获取数据失败');
            return {
              data: [],
              success: false,
              total: 0,
            };
          }
        }}
        columns={columns}
        scroll={{ x: 1000 }}
      />

      <ModalForm
        title={isEdit ? '编辑区域属性' : '新增区域属性'}
        width={600}
        open={modalOpen}
        onOpenChange={setModalOpen}
        onFinish={handleSubmit}
        initialValues={
          isEdit
            ? {
                ...currentRow,
                attributesIds:
                  currentRow.attributesIds?.split(',').map((id: string) => parseInt(id)) || [],
                matrixIds: currentRow.matrixIds?.split(',').map((id: string) => parseInt(id)) || [],
              }
            : {}
        }
      >
        <ProFormSelect
          name="area"
          label="区域"
          placeholder="请选择区域"
          options={areas}
          rules={[{ required: true, message: '请选择区域' }]}
          showSearch
          fieldProps={{
            filterOption: (input: string, option: any) =>
              option?.label?.toLowerCase().includes(input.toLowerCase()),
          }}
        />

        <ProFormSelect
          name="attributesIds"
          label="材料属性维度"
          placeholder="请选择材料属性维度"
          options={materialAttrs}
          rules={[{ required: true, message: '请选择材料属性维度' }]}
          fieldProps={{
            mode: 'multiple',
            showSearch: true,
            filterOption: (input: string, option: any) =>
              option?.label?.toLowerCase().includes(input.toLowerCase()),
          }}
        />

        <ProFormSelect
          name="matrixIds"
          label="核心经验子维度"
          placeholder="请选择核心经验子维度"
          options={matrixList}
          rules={[{ required: true, message: '请选择核心经验子维度' }]}
          fieldProps={{
            mode: 'multiple',
            showSearch: true,
            filterOption: (input: string, option: any) =>
              option?.label?.toLowerCase().includes(input.toLowerCase()),
          }}
        />
      </ModalForm>
    </PageContainer>
  );
};

export default AreaPropertyList;
