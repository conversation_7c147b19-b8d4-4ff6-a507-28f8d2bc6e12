/* eslint-disable guard-for-in */
// import { AntFormsItem, FormFields, FormTypes } from '@/components/UseForms';
import { geUserList } from '@/services/toolKit';
import type { ProColumns } from '@ant-design/pro-components';
import { PageContainer, ProTable } from '@ant-design/pro-components';
import dayjs from 'dayjs';
// import { Button, Form, Image, message } from 'antd';
// import { keyBy } from 'lodash';

const Toolkit: React.FC = () => {
  const columns: ProColumns<{
    id: string;
    title: string;
    content: string;
  }>[] = [
    {
      title: '编号',
      dataIndex: 'id',
      valueType: 'text',
      width: 20,
      search: false,
    },
    {
      title: '姓名',
      dataIndex: 'name',
      valueType: 'text',
      width: 50,
    },
    {
      title: '幼儿园',
      dataIndex: 'school',
      valueType: 'text',
      width: 80,
    },
    {
      title: '职务',
      dataIndex: 'post',
      valueType: 'text',
      width: 30,
      search: false,
    },
    {
      title: '手机号',
      dataIndex: 'mobile',
      valueType: 'text',
      width: 50,
      search: false,
    },

    {
      title: '填写时间',
      dataIndex: 'createdAt',
      valueType: 'text',
      width: 80,
      search: false,
      render: (text: any) => dayjs(text).format('YYYY-MM-DD HH:mm'),
    },
  ];

  return (
    <PageContainer>
      <ProTable<API.RuleListItem, API.PageParams>
        // actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 'auto',
          defaultCollapsed: false,
          collapsed: false,
          collapseRender: false,
        }}
        toolBarRender={false}
        request={geUserList}
        columns={columns}
      />
    </PageContainer>
  );
};

export default Toolkit;
