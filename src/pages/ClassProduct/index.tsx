/* eslint-disable guard-for-in */
import { timeColumn } from '@/components/ColumnRender';
import {
  addClassProduct,
  fetchClassDetail,
  fetchClassProductList,
  updateClassProduct,
  updateClassProductState,
} from '@/services/api';
import { exportFile } from '@/services/utils';
import { ExportOutlined, PlusOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { PageContainer, ProTable } from '@ant-design/pro-components';
import { Button, Form, Tooltip, message } from 'antd';
import { isEmpty } from 'lodash';
import React, { useEffect, useRef, useState } from 'react';
import { useParams } from 'umi';
import AddProductModal from './components/add';

const ClassProduct: React.FC = () => {
  const { id } = useParams();

  /**
   * @en-US Pop-up window of new window
   * @zh-CN 新建窗口的弹窗
   *  */
  const [modalOpen, setModalOpen] = useState<boolean>(false);

  const [currentRow, setCurrentRow] = useState<any>({});

  const actionRef = useRef<ActionType>();

  const [form] = Form.useForm();

  const [detailData, setDetailData] = useState<any>({});

  const [selectedRows, setSelectedRows] = useState<any>([]);

  useEffect(() => {
    fetchClassDetail(Number(id)).then((res) => {
      if (res?.status === 0) {
        setDetailData(res?.data);
      }
    });
  }, []);

  const handleModalOpen = ({ type, record }) => {
    form.resetFields();
    setCurrentRow({});

    if (type === 'edit') {
      setCurrentRow(record);
    }

    setModalOpen(true);
  };

  // 新增配置项
  const handleAdd = async (value: { key: string; value: string; category: string }) => {
    try {
      let res: API.AdminResponse = {};

      if (!!currentRow?.id) {
        res = await updateClassProduct({ ...value, id: currentRow?.id, schoolClassId: id });
      } else {
        res = await addClassProduct({ ...value, schoolClassId: id });
      }

      if (res?.status === 0) {
        message.success(!currentRow?.id ? '新增成功' : '更新成功');
        setModalOpen(false);
        if (actionRef.current) {
          actionRef.current.reload();
        }
      }
    } catch (error) {
      message.error(
        !currentRow?.id
          ? error?.message || '新增失败，请重试'
          : error?.message || '更新失败，请重试',
      );
    }
  };

  // 删除配置项
  const handleDelete = async (record: any) => {
    const hide = message.loading('正在更新状态');

    try {
      await updateClassProductState({
        id: record?.id,
        state: record?.state === 0 ? 1 : 0,
      });
      hide();
      message.success('Update successfully and will refresh soon');
      if (actionRef.current) {
        actionRef.current.reload();
      }
      return true;
    } catch (error) {
      hide();
      message.error(error?.message || 'Update failed, please try again');
      return false;
    }
  };

  // 商品导出
  const handleExport = async () => {
    message.open({
      type: 'loading',
      content: '正在导出中...',
      duration: 60,
    });
    const ids = selectedRows.map((item) => item.id);
    await exportFile({ ids, classId: id });
  };

  const columns: ProColumns<API.RuleListItem>[] = [
    {
      title: '编号',
      dataIndex: 'id',
      valueType: 'text',
      width: 80,
      search: false,
    },
    {
      title: '商品名称',
      dataIndex: 'product',
      valueType: 'text',
      width: 120,
      render: (value) => {
        return (
          <Tooltip title={value?.title}>
            <Button
              size="small"
              type="link"
              key="detail"
              href={`/product/detail/${value?.id}`}
              target="_blank"
              className="link-ellipsis"
            >
              {value?.title}
            </Button>
          </Tooltip>
        );
      },
    },
    {
      title: '已有数量',
      dataIndex: 'quantity',
      valueType: 'text',
      width: 80,
      search: false,
    },
    {
      title: '可玩时间',
      dataIndex: 'happyTime',
      valueType: 'text',
      width: 80,
      search: false,
    },
    {
      title: '实际区域',
      dataIndex: 'actualArea',
      valueType: 'text',
      width: 80,
      search: false,
      render: (result) => result?.value,
    },
    {
      title: '状态',
      dataIndex: 'state',
      // hideInForm: true,
      // search: false,
      width: 80,
      valueEnum: {
        0: { color: 'red', text: '停用', status: 'Default' },
        1: { color: 'green', text: '启用', status: 'Processing' },
      },
    },
    timeColumn,
    {
      title: '操作',
      ellipsis: true,
      dataIndex: 'action',
      key: 'action',
      valueType: 'option',
      width: '14em',
      search: false,
      render: (_, record) => [
        <Button
          size="small"
          type="primary"
          key="edit"
          onClick={() => {
            handleModalOpen({ type: 'edit', record });
          }}
        >
          编辑
        </Button>,
        <Button
          size="small"
          type="primary"
          key="delete"
          danger={!!record?.state}
          onClick={async () => {
            await handleDelete(record);
          }}
        >
          {record?.state === 0 ? '启用' : '停用'}
        </Button>,
      ],
    },
  ];

  return (
    <PageContainer
      content={
        <div>
          所属班级：
          <Button type="link" href={`/class/detail/${detailData?.id}`} target="_blank">
            {detailData?.title}
          </Button>
        </div>
      }
    >
      <ProTable<API.RuleListItem, API.PageParams>
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 'auto',
          defaultCollapsed: false,
          collapsed: false,
          collapseRender: false,
        }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              handleModalOpen({ type: 'add' });
            }}
          >
            <PlusOutlined /> 新建
          </Button>,
          <Button
            type="primary"
            key="export"
            disabled={isEmpty(selectedRows)}
            onClick={handleExport}
          >
            <ExportOutlined /> 导出
          </Button>,
        ]}
        request={async (params = {}) => {
          console.log(params, 'params');
          const res = await fetchClassProductList({ ...params, schoolClassId: id });
          return res;
        }}
        columns={columns}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
      />
      <AddProductModal
        form={form}
        modalOpen={modalOpen}
        setModalOpen={setModalOpen}
        currentRow={currentRow}
        actionRef={actionRef}
        handleAdd={handleAdd}
      />
    </PageContainer>
  );
};

export default ClassProduct;
