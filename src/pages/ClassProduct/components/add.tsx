/* eslint-disable guard-for-in */
import { fetchClassProductDetail, fetchProductList } from '@/services/api';
import { DictionaryCategory } from '@/services/constants';
import { checkPositiveInteger, debounceSearch } from '@/services/utils';
import { DrawerForm, ProFormDigit, ProFormSelect } from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { Col, Row, message } from 'antd';
import { isEmpty } from 'lodash';
import React, { useEffect, useState } from 'react';

const AddClassProductModal: React.FC = ({
  form,
  modalOpen,
  setModalOpen,
  currentRow,
  handleAdd,
}) => {
  const { initialState } = useModel('@@initialState');
  const { dictionaryList } = initialState || {};

  // 区域列表
  const [areas, setAreas] = useState<any>([]);

  useEffect(() => {
    if (!isEmpty(dictionaryList)) {
      const result = dictionaryList?.map((item) => {
        return {
          value: item?.id,
          label: item?.value,
          category: item?.category,
        };
      });

      const _areas = result.filter((item) => item.category === DictionaryCategory.Area);

      setAreas(_areas);
    }
  }, []);

  useEffect(() => {
    if (!!currentRow?.id && modalOpen) {
      fetchClassProductDetail(currentRow?.id).then((res) => {
        if (res?.status === 0) {
          const { data } = res;

          form.setFieldsValue({
            productId: { label: data?.title, value: data?.productId },
            quantity: data?.quantity,
            happyTime: data?.happyTime,
            actualAreaId: data?.actualAreaId,
          });
        }
      });
    }
  }, [modalOpen]);

  const handleFinish = async (value) => {
    const result = {
      ...value,
      productId: value?.productId?.value,
    };
    await handleAdd(result);
  };

  const onFinishFailed = () => {
    message.error('表单校验失败，请检查输入');
  };

  return (
    <DrawerForm
      title={!currentRow?.id ? '新增班级商品' : '编辑班级商品'}
      width="800px"
      form={form}
      open={modalOpen}
      drawerProps={{
        maskClosable: false,
      }}
      onOpenChange={setModalOpen}
      onFinish={handleFinish}
      onFinishFailed={onFinishFailed}
    >
      <Row gutter={8}>
        <Col span={12}>
          <ProFormSelect
            rules={[
              {
                required: true,
                message: '请选择关联商品',
              },
            ]}
            name="productId"
            label="关联商品"
            showSearch
            fieldProps={{
              labelInValue: true,
            }}
            request={async (value) => {
              return new Promise((resolve) => {
                debounceSearch(value, fetchProductList, resolve, {
                  current: 1,
                  pageSize: 20,
                  keyword: value?.keyWords,
                  state: 1,
                });
              });
            }}
          />
        </Col>
        <Col span={12}>
          <ProFormDigit
            rules={[
              {
                required: true,
                message: '请填写已有数量',
              },
              { validator: checkPositiveInteger },
            ]}
            fieldProps={{
              min: 0, // 最小值
              max: 999, // 最大值
            }}
            label="已有数量"
            name="quantity"
          />
        </Col>
        <Col span={12}>
          <ProFormDigit
            rules={[
              // {
              //   required: true,
              //   message: '请填写可玩时间',
              // },
              { validator: checkPositiveInteger },
            ]}
            fieldProps={{
              min: 0, // 最小值
              max: 999, // 最大值
            }}
            label="可玩时间"
            name="happyTime"
          />
        </Col>
        <Col span={12}>
          <ProFormSelect label="实际区域" name="actualAreaId" options={areas} />
        </Col>
      </Row>
    </DrawerForm>
  );
};

export default AddClassProductModal;
