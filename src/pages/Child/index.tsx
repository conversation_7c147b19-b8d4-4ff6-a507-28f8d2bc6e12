/* eslint-disable guard-for-in */

import { getChildList } from '@/services/api';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { PageContainer, ProTable } from '@ant-design/pro-components';
import { Button, Image } from 'antd';
import React, { useRef } from 'react';
// import { EducationalEnum } from './data';

import { timeColumn } from '@/components/ColumnRender';

import { getClassColumn, getSchoolColumn } from '@/components/CommonColumn';
import dayjs from 'dayjs';

const School: React.FC = () => {
  const actionRef = useRef<ActionType>();

  // 新增配置项

  const columns: ProColumns<API.RuleListItem>[] = [
    getSchoolColumn(),
    getClassColumn(),
    {
      title: '编号',
      dataIndex: 'id',
      valueType: 'text',
      width: 80,
      search: false,
    },
    {
      title: '儿童姓名',
      dataIndex: 'title',
      valueType: 'text',
      search: false,
      width: 120,
    },
    {
      title: '出生年月',
      dataIndex: 'birthday',
      valueType: 'text',
      width: 120,
      search: false,
      renderText(text) {
        return dayjs(text).format('YYYY-MM-DD');
      },
    },
    {
      title: '入园时间',
      dataIndex: 'entryTime',
      valueType: 'text',
      width: 120,
      search: false,
      renderText(text) {
        return dayjs(text).format('YYYY-MM-DD');
      },
    },
    {
      title: '是否有发展症状',
      dataIndex: 'symptoms',
      valueType: 'text',
      width: 180,
      search: false,
      valueEnum: {
        1: '正常儿童',
        2: '疑似特殊',
        3: '诊断特殊',
      },
    },

    {
      title: '性别',
      dataIndex: 'sex',
      valueType: 'text',
      width: 80,
      search: false,
      valueEnum: {
        1: '男',
        2: '女',
      },
    },
    {
      title: '是否独生子女',
      dataIndex: 'isSingle',
      valueType: 'text',
      width: 120,
      search: false,
      valueEnum: {
        0: '否',
        1: '是',
      },
    },

    // {
    //   title: '父亲姓名',
    //   dataIndex: 'fatherName',
    //   valueType: 'text',
    //   width: 120,
    //   search: false,
    // },
    // {
    //   title: '父亲手机号',
    //   dataIndex: 'fatherMobile',
    //   valueType: 'text',
    //   width: 120,
    //   search: false,
    // },
    // {
    //   title: '父亲学历',
    //   dataIndex: 'fatherEducational',
    //   valueType: 'text',
    //   width: 120,
    //   search: false,
    //   valueEnum: EducationalEnum,
    // },
    // {
    //   title: '父亲职业',
    //   dataIndex: 'fatherProfession',
    //   valueType: 'text',
    //   width: 120,
    //   search: false,
    // },
    // {
    //   title: '父亲工作单位',
    //   dataIndex: 'fatherWorkspace',
    //   valueType: 'text',
    //   width: 120,
    //   search: false,
    // },

    // {
    //   title: '母亲姓名',
    //   dataIndex: 'motherName',
    //   valueType: 'text',
    //   width: 120,
    //   search: false,
    // },
    // {
    //   title: '母亲手机号',
    //   dataIndex: 'motherMobile',
    //   valueType: 'text',
    //   width: 120,
    //   search: false,
    // },
    // {
    //   title: '母亲学历',
    //   dataIndex: 'motherEducational',
    //   valueType: 'text',
    //   width: 120,
    //   search: false,
    //   valueEnum: EducationalEnum,
    // },
    // {
    //   title: '母亲职业',
    //   dataIndex: 'motherProfession',
    //   valueType: 'text',
    //   width: 120,
    //   search: false,
    // },
    // {
    //   title: '母亲工作单位',
    //   dataIndex: 'motherWorkspace',
    //   valueType: 'text',
    //   width: 120,
    //   search: false,
    // },

    {
      title: '备注',
      dataIndex: 'note',
      valueType: 'text',
      width: 120,
      disable: false,
      search: false,
    },

    {
      title: '照片',
      dataIndex: 'header',
      valueType: 'text',
      width: 120,
      search: false,
      render: (result: any) => {
        return result?.uri ? <Image src={result?.uri} height={50} /> : null;
      },
    },
    {
      title: '状态',
      dataIndex: 'state',
      // hideInForm: true,
      search: false,
      width: 80,
      valueEnum: {
        0: { color: 'red', text: '离园', status: 'Default' },
        1: { color: 'green', text: '正常', status: 'Processing' },
      },
    },
    timeColumn,
    {
      title: '操作',
      fixed: 'right',
      width: 80,
      search: false,
      valueType: 'operation',
      render(_, entity: any) {
        return (
          <>
            <Button
              size="small"
              type="link"
              key="detail"
              href={`/child/detail/${entity.id}`}
              target="_blank"
            >
              详情
            </Button>
          </>
        );
      },
    },
  ];

  return (
    <PageContainer>
      <ProTable<API.RuleListItem, API.PageParams>
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 'auto',
          defaultCollapsed: false,
          collapsed: false,
          collapseRender: false,
        }}
        columnsState={{
          defaultValue: {
            note: {
              show: false,
            },
          },
        }}
        toolBarRender={() => []}
        request={getChildList}
        columns={columns}
        // scroll={{ x: 2000 }}
      />
    </PageContainer>
  );
};

export default School;
