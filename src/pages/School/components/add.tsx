/* eslint-disable guard-for-in */
import MapContainer from '@/components/MapContainer';
import UploadImageComponent from '@/components/UploadImage';
import { fetchArea, fetchSchoolDetail } from '@/services/api';
import { fetchClient } from '@/services/fileUpload';
import { phoneValidator } from '@/services/utils';
import {
  DrawerForm,
  ProForm,
  ProFormDependency,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { Col, Row, message } from 'antd';
import React, { useEffect, useState } from 'react';

const AddSchoolModal: React.FC = ({ form, modalOpen, setModalOpen, currentRow, handleAdd }) => {
  const [client, setClient] = useState<any>();

  const [headerList, setHeaderList] = useState<API.FileItem[]>([]);
  const [sectionList, setSectionList] = useState<API.FileItem[]>([]);

  useEffect(() => {
    if (!!currentRow?.id && modalOpen) {
      fetchSchoolDetail(currentRow?.id).then((res) => {
        if (res?.status === 0) {
          const { data } = res;

          if (data?.header?.id) {
            const file = {
              id: data.header.id,
              uri: data.header.uri,
              filename: data.header.filename,
            };
            setHeaderList([file]);
          } else {
            setHeaderList([]);
          }

          if (data?.sections?.length) {
            const files = data.sections.map((item) => {
              return {
                id: item.id,
                uri: item.uri,
                filename: item.filename,
              };
            });
            setSectionList(files);
          } else {
            setSectionList([]);
          }

          form.setFieldsValue({
            title: data?.title,
            englishTitle: data?.englishTitle,
            simpleTitle: data?.simpleTitle,
            country: data?.country,
            address: data?.address,
            master: data?.master,
            contact: data?.contact,
            mobile: data?.mobile,
            tin: data?.tin,
            longitude: data?.longitude,
            latitude: data?.latitude,
            province: data?.province,
            city: data?.city,
            district: data?.district,
            street: data?.street,
          });
        }
      });
    } else {
      setHeaderList([]);
      setSectionList([]);
    }
  }, [modalOpen]);

  useEffect(() => {
    fetchClient(setClient);
  }, []);

  const handleFinish = async (value) => {
    const result = value;

    if (headerList?.length) {
      result['headerId'] = headerList?.[0]?.id;
    }

    if (sectionList?.length) {
      result['sectionIds'] = sectionList?.map((item) => item?.id);
    }

    result['province'] = value?.province?.label;
    result['city'] = value?.city?.label;
    result['district'] = value?.district?.label;
    result['street'] = value?.street?.label;

    await handleAdd(result);
  };

  const onFinishFailed = () => {
    message.error('表单校验失败，请检查输入');
  };

  const handleFirstSelectChange = () => {
    form.setFieldsValue({
      city: undefined,
      district: undefined,
      street: undefined,
    });
  };

  const handleSecondSelectChange = () => {
    form.setFieldsValue({
      district: undefined,
      street: undefined,
    });
  };

  const handleThirdSelectChange = () => {
    form.setFieldsValue({
      street: undefined,
    });
  };

  return (
    <DrawerForm
      title={!currentRow?.id ? '新建学校' : '编辑学校'}
      width="800px"
      form={form}
      open={modalOpen}
      drawerProps={{
        maskClosable: false,
      }}
      onOpenChange={setModalOpen}
      onFinish={handleFinish}
      onFinishFailed={onFinishFailed}
    >
      <Row gutter={8}>
        <Col span={12}>
          <ProFormText
            rules={[
              {
                required: true,
                message: '请填写学校名称',
              },
            ]}
            label="学校名称"
            name="title"
          />
        </Col>
        <Col span={12}>
          <ProFormText
            // rules={[
            //   {
            //     required: true,
            //     message: '请填写英文名称',
            //   },
            // ]}
            label="英文名称"
            name="englishTitle"
          />
        </Col>
        <Col span={12}>
          <ProFormText
            rules={[
              {
                required: true,
                message: '请填写简称',
              },
            ]}
            label="简称"
            name="simpleTitle"
          />
          <ProFormText name="country" hidden initialValue="中国" />
        </Col>
      </Row>
      <Row gutter={8}>
        <ProForm.Group size={8}>
          <ProFormSelect
            rules={[
              {
                required: true,
                message: '请选择省份',
              },
            ]}
            fieldProps={{
              labelInValue: true,
            }}
            label="选择省份"
            name="province"
            width="sm"
            onChange={handleFirstSelectChange}
            request={async () => {
              console.log(form.getFieldsValue());

              return fetchArea({ current: 1, pageSize: 50, pid: 1, areaType: 1 }).then(
                ({ data }) => {
                  return data.map((item) => {
                    return {
                      label: item.title,
                      value: item.id,
                    };
                  });
                },
              );
            }}
          />
          <ProFormDependency name={['province']}>
            {({ province }) => {
              return (
                <ProFormSelect
                  params={{
                    key: province?.value,
                  }}
                  label="选择城市"
                  name="city"
                  width="sm"
                  rules={[
                    {
                      required: true,
                      message: '请选择城市',
                    },
                  ]}
                  fieldProps={{
                    labelInValue: true,
                  }}
                  disabled={!province}
                  onChange={handleSecondSelectChange}
                  request={async () => {
                    if (!province?.key) {
                      return [];
                    }
                    return fetchArea({
                      current: 1,
                      pageSize: 50,
                      pid: province.key,
                      areaType: 2,
                    }).then(({ data }) => {
                      return data.map((item) => {
                        return {
                          label: item.title,
                          value: item.id,
                        };
                      });
                    });
                  }}
                />
              );
            }}
          </ProFormDependency>
          <ProFormDependency name={['province', 'city']}>
            {({ city }) => {
              return (
                <ProFormSelect
                  params={{
                    key: city?.value,
                  }}
                  label="选择区县"
                  name="district"
                  width="sm"
                  rules={[
                    {
                      required: true,
                      message: '请选择区县',
                    },
                  ]}
                  fieldProps={{
                    labelInValue: true,
                  }}
                  disabled={!city}
                  onChange={handleThirdSelectChange}
                  request={async () => {
                    if (!city?.key) {
                      return [];
                    }
                    return fetchArea({
                      current: 1,
                      pageSize: 50,
                      pid: city.key,
                      areaType: 3,
                    }).then(({ data }) => {
                      return data.map((item) => {
                        return {
                          label: item.title,
                          value: item.id,
                        };
                      });
                    });
                  }}
                />
              );
            }}
          </ProFormDependency>
          <ProFormDependency name={['province', 'city', 'district']}>
            {({ district }) => {
              return (
                <ProFormSelect
                  params={{
                    key: district?.value,
                  }}
                  label="选择街道"
                  name="street"
                  width="sm"
                  rules={[
                    {
                      required: true,
                      message: '请选择区县',
                    },
                  ]}
                  fieldProps={{
                    labelInValue: true,
                  }}
                  disabled={!district}
                  request={async () => {
                    if (!district?.key) {
                      return [];
                    }
                    return fetchArea({
                      current: 1,
                      pageSize: 50,
                      pid: district.key,
                      areaType: 4,
                    }).then(({ data }) => {
                      return data.map((item) => {
                        return {
                          label: item.title,
                          value: item.id,
                        };
                      });
                    });
                  }}
                />
              );
            }}
          </ProFormDependency>
        </ProForm.Group>
      </Row>
      <Row gutter={8}>
        <Col span={12}>
          <ProFormText
            rules={[
              {
                required: true,
                message: '请输入详细地址',
              },
            ]}
            label="详细地址"
            name="address"
          />
        </Col>
        <Col span={12}>
          <ProFormText
            rules={[
              {
                required: true,
                message: '请填写园长姓名',
              },
            ]}
            label="园长姓名"
            name="master"
          />
        </Col>
        <Col span={12}>
          <ProFormText
            // rules={[
            //   {
            //     required: true,
            //     message: '请填写联系人',
            //   },
            // ]}
            label="联系人"
            name="contact"
          />
        </Col>
        <Col span={12}>
          <ProFormText
            rules={[
              // {
              //   required: true,
              //   message: '请填写联系电话',
              // },
              {
                validator: phoneValidator,
              },
            ]}
            label="联系电话"
            name="mobile"
          />
        </Col>
        <Col span={12}>
          <ProFormText label="统一社会信用代码" name="tin" />
        </Col>
      </Row>

      <ProForm.Item name="location" label="经纬度搜索">
        <MapContainer mapStyle={{ height: '400px' }} form={form} />
      </ProForm.Item>
      <Row gutter={8}>
        <Col span={12}>
          <ProFormText label="经度" name="longitude" disabled />
        </Col>
        <Col span={12}>
          <ProFormText label="纬度" name="latitude" disabled />
        </Col>
      </Row>

      <Row>
        <UploadImageComponent
          key="upload"
          fileName="headerId"
          label="预览图"
          max={1}
          client={client}
          fileList={headerList}
          setClient={setClient}
          setFileList={setHeaderList}
          accept=".png,.jpg,.jpeg,.gif"
        />
      </Row>
      <Row>
        <UploadImageComponent
          key="upload"
          fileName="sectionIds"
          label="详情图"
          max={10}
          client={client}
          fileList={sectionList}
          setClient={setClient}
          setFileList={setSectionList}
          accept=".png,.jpg,.jpeg,.gif"
        />
      </Row>
    </DrawerForm>
  );
};

export default AddSchoolModal;
