/* eslint-disable guard-for-in */
import { fetchSchoolDetail } from '@/services/api';
import { PageContainer } from '@ant-design/pro-components';
import { Card, Descriptions, Divider, Image } from 'antd';
import React, { useEffect, useState } from 'react';
import { useParams } from 'umi';

const SchoolDetail: React.FC = () => {
  const { id } = useParams();

  const [detailData, setDetailData] = useState<any>({});

  useEffect(() => {
    fetchSchoolDetail(Number(id)).then((res) => {
      if (res?.status === 0) {
        setDetailData(res?.data);
      }
    });
  }, []);

  return (
    <PageContainer>
      <Card bordered={false}>
        <Descriptions
          title="学校信息"
          style={{
            marginBottom: 32,
          }}
        >
          <Descriptions.Item label="名称">{detailData?.title}</Descriptions.Item>
          <Descriptions.Item label="英文名称">{detailData?.englishTitle}</Descriptions.Item>
          <Descriptions.Item label="简称">{detailData?.simpleTitle}</Descriptions.Item>
          <Descriptions.Item label="地址">
            {detailData?.province || '-'}
            {detailData?.city || '-'}
            {detailData?.district || '-'}
            {detailData?.street || '-'}
            {detailData?.address || '-'}
          </Descriptions.Item>
          <Descriptions.Item label="园长姓名">{detailData?.master}</Descriptions.Item>
          <Descriptions.Item label="联系人">
            {detailData?.contact} - {detailData?.mobile}
          </Descriptions.Item>
          <Descriptions.Item label="统一社会信用代码">{detailData?.tin}</Descriptions.Item>
        </Descriptions>
        <Divider
          style={{
            marginBottom: 32,
          }}
        />
        <Descriptions
          title="预览图"
          style={{
            marginBottom: 32,
          }}
        >
          <Descriptions.Item label="">
            <Image width={100} src={detailData?.header?.uri} preview />
          </Descriptions.Item>
        </Descriptions>
        <Descriptions
          title="详情图"
          style={{
            marginBottom: 32,
          }}
        >
          <Descriptions.Item label="">
            {detailData?.sections?.map((item: any) => (
              <div key={item?.id} style={{ marginRight: '20px', marginBottom: '20px' }}>
                <Image width={100} src={item?.uri} preview />
              </div>
            ))}
          </Descriptions.Item>
        </Descriptions>
      </Card>
    </PageContainer>
  );
};

export default SchoolDetail;
