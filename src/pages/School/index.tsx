/* eslint-disable guard-for-in */
import { timeColumn } from '@/components/ColumnRender';
import { addSchool, fetchSchoolList, updateSchool, updateSchoolState } from '@/services/api';
import { PlusOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { PageContainer, ProTable } from '@ant-design/pro-components';
import { Button, Form, Image, message } from 'antd';
import React, { useRef, useState } from 'react';
import AddProductModal from './components/add';

const School: React.FC = () => {
  /**
   * @en-US Pop-up window of new window
   * @zh-CN 新建窗口的弹窗
   *  */
  const [modalOpen, setModalOpen] = useState<boolean>(false);

  const [currentRow, setCurrentRow] = useState<any>({});

  const actionRef = useRef<ActionType>();

  const [form] = Form.useForm();

  const handleModalOpen = ({ type, record }) => {
    form.resetFields();
    setCurrentRow({});

    if (type === 'edit') {
      setCurrentRow(record);
    }

    setModalOpen(true);
  };

  // 新增配置项
  const handleAdd = async (value: { key: string; value: string; category: string }) => {
    console.log('value', value, currentRow);
    try {
      let res: API.AdminResponse = {};

      if (!!currentRow?.id) {
        res = await updateSchool({ ...value, id: currentRow?.id });
      } else {
        res = await addSchool({ ...value });
      }

      if (res?.status === 0) {
        message.success(!currentRow?.id ? '新建成功' : '更新成功');
        setModalOpen(false);
        if (actionRef.current) {
          actionRef.current.reload();
        }
      }
    } catch (error) {
      console.log(error);
      message.error(
        !currentRow?.id
          ? error?.message || '新建失败，请重试'
          : error?.message || '更新失败，请重试',
      );
    }
  };

  // 删除配置项
  const handleDelete = async (record: any) => {
    const hide = message.loading('正在更新状态');

    try {
      await updateSchoolState({
        id: record?.id,
        state: record?.state === 0 ? 1 : 0,
      });
      hide();
      message.success('Update successfully and will refresh soon');
      if (actionRef.current) {
        actionRef.current.reload();
      }
      return true;
    } catch (error) {
      hide();
      message.error(error?.message || 'Update failed, please try again');
      return false;
    }
  };

  const columns: ProColumns<API.RuleListItem>[] = [
    {
      title: '编号',
      dataIndex: 'id',
      valueType: 'text',
      width: 80,
      search: false,
    },
    {
      title: '名称',
      dataIndex: 'title',
      valueType: 'text',
      width: 120,
    },
    {
      title: '英文名称',
      dataIndex: 'englishTitle',
      valueType: 'text',
      width: 120,
      search: false,
    },
    {
      title: '简称',
      dataIndex: 'simpleTitle',
      valueType: 'text',
      width: 120,
      search: false,
    },
    {
      title: '地址',
      dataIndex: 'province',
      valueType: 'text',
      width: 180,
      search: false,
      render: (_, record) => {
        return `${record?.province || '-'}${record?.city || '-'}${record?.district || '-'}${
          record?.street || '-'
        }${record?.address || '-'}`;
      },
    },
    {
      title: '园长姓名',
      dataIndex: 'master',
      valueType: 'text',
      width: 120,
      search: false,
    },
    {
      title: '班级数量',
      dataIndex: 'classCount',
      valueType: 'text',
      width: 120,
      search: false,
    },
    {
      title: '联系人',
      dataIndex: 'contact',
      valueType: 'text',
      width: 140,
      search: false,
      render: (_, record) => {
        return `${record?.contact} - ${record?.mobile}`;
      },
    },
    {
      title: '统一社会信用代码',
      dataIndex: 'tin',
      valueType: 'text',
      width: 140,
      search: false,
    },
    {
      title: '预览图',
      dataIndex: 'header',
      valueType: 'text',
      width: 120,
      search: false,
      render: (result) => {
        return <Image src={result?.uri} height={50} />;
      },
    },
    {
      title: '状态',
      dataIndex: 'state',
      // hideInForm: true,
      // search: false,
      width: 80,
      valueEnum: {
        0: { color: 'red', text: '停用', status: 'Default' },
        1: { color: 'green', text: '启用', status: 'Processing' },
      },
    },
    timeColumn,
    {
      title: '操作',
      ellipsis: true,
      dataIndex: 'action',
      key: 'action',
      valueType: 'option',
      width: '14em',
      search: false,
      render: (_, record) => [
        <Button
          size="small"
          type="primary"
          key="edit"
          onClick={() => {
            handleModalOpen({ type: 'edit', record });
          }}
        >
          编辑
        </Button>,
        <Button
          size="small"
          type="primary"
          key="delete"
          danger={!!record?.state}
          onClick={async () => {
            await handleDelete(record);
          }}
        >
          {record?.state === 0 ? '启用' : '停用'}
        </Button>,
        <Button
          size="small"
          type="link"
          key="detail"
          href={`/school/detail/${record?.id}`}
          target="_blank"
        >
          查看详情
        </Button>,
      ],
    },
  ];

  return (
    <PageContainer>
      <ProTable<API.RuleListItem, API.PageParams>
        actionRef={actionRef}
        rowKey="id"
        search={{
          labelWidth: 'auto',
          defaultCollapsed: false,
          collapsed: false,
          collapseRender: false,
        }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              handleModalOpen({ type: 'add' });
            }}
          >
            <PlusOutlined /> 新建
          </Button>,
        ]}
        request={fetchSchoolList}
        columns={columns}
        scroll={{ x: 2000 }}
      />
      <AddProductModal
        form={form}
        modalOpen={modalOpen}
        setModalOpen={setModalOpen}
        currentRow={currentRow}
        actionRef={actionRef}
        handleAdd={handleAdd}
      />
    </PageContainer>
  );
};

export default School;
