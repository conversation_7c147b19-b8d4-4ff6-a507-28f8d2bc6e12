import { fetchEnum } from '@/services/apis';
import {
  deleteDocumentTemplate,
  getDocumentTemplateList,
  updateDocumentTemplate,
} from '@/services/documentTemplate';
import { parseEnumMapToOptions } from '@/services/enums';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { Button, Form, message, Popconfirm } from 'antd';
import pick from 'lodash/pick';
import { useEffect, useRef, useState } from 'react';
import EditTemplate from './EditTemplate';

/** 状态 */
export enum StateStatus {
  // 停用
  Off = 0,
  // 启用
  On = 1,
}

const AITemplate = () => {
  const [open, setOpen] = useState(false);
  const [resource, setResource] = useState<any>();
  const [form] = Form.useForm<API.DocumentTemplateListItem>();
  const [subjectCategoryOptions, setSubjectCategoryOptions] = useState<any>({});
  const [subjectTypeOptions, setSubjectTypeOptions] = useState<any>({});
  const ref = useRef<ActionType>();
  const urlParams = new URL(window.location.href).searchParams;
  const handleAction = () => {
    ref.current?.reload();
  };

  useEffect(() => {
    fetchEnum().then((res) => {
      setSubjectCategoryOptions(res?.data?.DocumentTemplateCategoryEnumDesc);
      setSubjectTypeOptions(res?.data?.SubjectTypeEnumDesc);
    });
  }, []);
  const toggleTemplateState = async (
    formValues: Partial<API.DocumentTemplateListItem>,
    state: StateStatus,
  ) => {
    const res = await updateDocumentTemplate({
      ...formValues,
      state,
    });

    if (res.status === 0) {
      message.success('操作成功');
      handleAction();
      return;
    }
    message.error(res.message);
  };

  const columns: ProColumns<API.DocumentTemplateListItem>[] = [
    {
      dataIndex: 'name',
      title: '模版名称',
      width: 120,
    },
    {
      dataIndex: 'documentTemplateCategory',
      title: '模板类别',
      width: 120,
      search: false,
      valueEnum: subjectCategoryOptions,
    },
    {
      dataIndex: 'subjectType',
      title: '学科类型',
      width: 120,
      search: false,
      valueEnum: subjectTypeOptions,
    },
    {
      dataIndex: 'school',
      title: '学校',
      search: false,
      renderText(_, record) {
        return record.school && record.school.title;
      },
    },
    {
      dataIndex: 'createdAt',
      title: '创建时间',
      key: 'createdAt',
      valueType: 'date',
      hideInSearch: true,
      width: 120,
    },
    {
      dataIndex: 'updatedAt',
      title: '更新时间',
      key: 'updatedAt',
      valueType: 'date',
      hideInSearch: true,
      width: 120,
    },
    {
      dataIndex: 'state',
      title: '状态',
      initialValue: '2',
      width: 120,
      valueEnum: {
        2: { text: '全部', status: 'Default' },
        1: { text: '已发布', status: 'Processing' },
        0: { text: '已停用', status: 'Default' },
      },
    },
    {
      title: '操作',
      key: 'option',
      valueType: 'option',
      width: 200,
      render: (_node, row) => {
        const formValues = pick(row, [
          'documentTemplateCategory',
          'subjectType',
          'subjectId',
          'description',
          'templateResource',
          'id',
          'state',
          'name',
        ]);
        return [
          <Button
            size="small"
            type="link"
            key="edit"
            onClick={() => {
              form.setFieldsValue(formValues);
              setResource(row);

              setOpen(true);
            }}
          >
            编辑
          </Button>,

          row.state === StateStatus.Off && (
            <Button
              size="small"
              type="link"
              key="release"
              onClick={() => toggleTemplateState(formValues, StateStatus.On)}
            >
              发布
            </Button>
          ),
          row.state === StateStatus.On && (
            <Button
              size="small"
              type="link"
              key="suspended"
              onClick={() => toggleTemplateState(formValues, StateStatus.Off)}
            >
              停用
            </Button>
          ),
          <Popconfirm
            key={'delete'}
            title="确定要删除这条模版吗？"
            onConfirm={async () => {
              const res = await deleteDocumentTemplate(row.id!);

              if (res.status === 0) {
                message.success('操作成功');
                handleAction();
                return;
              }
            }}
          >
            <Button size="small" type="link" key="delete">
              删除
            </Button>
          </Popconfirm>,
        ];
      },
    },
  ];
  return (
    <PageContainer>
      <ProTable<API.DocumentTemplateListItem>
        actionRef={ref}
        columns={columns}
        rowKey="id"
        toolBarRender={() => [
          <Button
            key="button"
            type="primary"
            onClick={() => {
              setResource(undefined);
              form.setFieldValue('state', 1);
              form.setFieldValue('subjectId', Number(urlParams.get('subjectId')));
              form.setFieldValue('subjectType', urlParams.get('subjectType'));
              setOpen(true);
            }}
          >
            新建模版
          </Button>,
        ]}
        request={async (params) => {
          console.log(urlParams.get('subjectId'));
          const response = await getDocumentTemplateList({
            current: params.current,
            pageSize: params.pageSize,
            state: Number(params.state) === 2 ? '' : params.state,
            name: params.name,
            subjectId: Number(urlParams.get('subjectId')),
            subjectType: urlParams.get('subjectType') || undefined,
          });
          return {
            data: response.data,
            success: true,
            total: response.total,
          };
        }}
      />
      <EditTemplate
        open={open}
        form={form}
        setOpen={setOpen}
        resource={resource}
        onChangeSuccess={handleAction}
        subjectCategoryOptions={parseEnumMapToOptions(subjectCategoryOptions)}
        subjectTypeOptions={parseEnumMapToOptions(subjectTypeOptions)}
      />
    </PageContainer>
  );
};

export default AITemplate;
