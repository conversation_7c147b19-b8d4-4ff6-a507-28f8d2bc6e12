import { fetchClassList, fetchSchoolList } from '@/services/apis';
import {
  createDocumentTemplate,
  CreateDocumentTemplateParams,
  updateDocumentTemplate,
} from '@/services/documentTemplate';
import { fetchClient, handleUpload } from '@/services/fileUpload';
import {
  DrawerForm,
  ProFormSelect,
  ProFormSwitch,
  ProFormText,
  ProFormTextArea,
  ProFormUploadButton,
} from '@ant-design/pro-components';
import { message } from 'antd';
import { useWatch } from 'antd/es/form/Form';
import type { FormInstance } from 'antd/es/form/hooks/useForm';
import { FC, useEffect, useState } from 'react';

type Props = {
  open: boolean;
  setOpen: (open: boolean) => void;
  form?: FormInstance<API.DocumentTemplateListItem>;
  onChangeSuccess?: () => void;
  resource?: any;
  subjectCategoryOptions: any[];
  subjectTypeOptions: any[];
};

const EditTemplate: FC<Props> = ({
  resource,
  open,
  setOpen,
  form,
  onChangeSuccess,
  subjectCategoryOptions,
  subjectTypeOptions,
}) => {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [subjectIdOptions, setSubjectIdOptions] = useState<any[]>([]);
  const isEdit = resource !== undefined;
  const [templateRecource, setTemplateResource] = useState<any>([]);
  // const urlParams = new URL(window.location.href).searchParams;
  // const isInSchool = !!urlParams.get('subjectId');

  useEffect(() => {
    if (resource) {
      if (resource?.templateResource) {
        setTemplateResource([
          {
            name: resource.templateResource.filename,
            url: resource.templateResource.uri,
            uid: resource.templateResource.hash,
            id: resource.templateResource.id,
          },
        ]);
      }
    }
  }, [resource]);
  useEffect(() => {
    if (!isEdit) setTemplateResource([]);
  }, [isEdit]);
  const subjectType = useWatch('subjectType', form);

  useEffect(() => {
    fetchSchoolList({
      current: 1,
      pageSize: 1000,
    }).then((res) => {
      setSubjectIdOptions(res.data.map((d: any) => ({ label: d.title, value: d.id })));
    });
  }, []);
  useEffect(() => {
    if (subjectType) {
      switch (subjectType) {
        case 'School':
          fetchSchoolList({
            current: 1,
            pageSize: 1000,
          }).then((res) => {
            setSubjectIdOptions(res.data.map((d: any) => ({ label: d.title, value: d.id })));
          });
          break;
        case 'Class':
          fetchClassList({
            current: 1,
            pageSize: 1000,
          }).then((res) => {
            setSubjectIdOptions(res.data.map((d: any) => ({ label: d.title, value: d.id })));
          });
          break;
        default:
          break;
      }
    }
  }, [subjectType]);

  return (
    <DrawerForm<API.DocumentTemplateListItem>
      title={`${isEdit ? '编辑' : '新建'}模版`}
      open={open}
      form={form}
      grid
      drawerProps={{
        destroyOnClose: true,
      }}
      onOpenChange={setOpen}
      onFinish={async (values) => {
        const createParams: CreateDocumentTemplateParams = {
          name: values.name,
          subjectType: values.subjectType,
          subjectId: values.subjectId,
          documentTemplateCategory: values.documentTemplateCategory,
          description: values.description,
          templateResourceId: templateRecource[0]?.id || 0,
          state: values.state,
        };
        const res = isEdit
          ? await updateDocumentTemplate({
              ...createParams,
              id: resource.id,
            })
          : await createDocumentTemplate(createParams);

        if (res.status === 0) {
          onChangeSuccess?.();

          message.success('操作成功');
          return true;
        }
        message.error(res.message);
      }}
      rowProps={{ gutter: 16 }}
    >
      <ProFormText
        colProps={{
          span: 20,
        }}
        name="name"
        label="模版名称"
        required
        rules={[{ required: true, message: '请输入模版名称！' }]}
      />
      <ProFormSwitch
        colProps={{
          span: 4,
        }}
        initialValue={true}
        name="state"
        label="是否启用"
        convertValue={(value) => !!value}
        transform={(value) => (value ? 1 : 0)}
      />
      <ProFormSelect
        // disabled={isEdit || isInSchool}
        name="subjectType"
        label="模版范围"
        options={subjectTypeOptions}
      />
      {subjectType === 'School' ? (
        <ProFormSelect
          name="subjectId"
          label={subjectType === 'School' ? '选择学校' : '选择学校'}
          options={subjectIdOptions}
          // disabled={isEdit || isInSchool}
        />
      ) : null}
      <ProFormSelect
        name="documentTemplateCategory"
        label="模版类型"
        options={subjectCategoryOptions}
      />

      <ProFormUploadButton
        name="templateResourceId"
        label="模版文件"
        fileList={templateRecource}
        listType="text"
        onChange={({ file }) => {
          console.log('123');
          console.log(file);
          if (file.status === 'removed') {
            // 删除对应的文件
            setTemplateResource([]);
            return;
          }
          fetchClient(async (client: any) => {
            const resource = await handleUpload(client, file.originFileObj);
            setTemplateResource([
              {
                name: resource.filename,
                url: resource.uri,
                uid: resource.hash,
                id: resource.id,
              },
            ]);
          });
        }}
      />
      <ProFormTextArea name="content" label="内容描述" placeholder="请输入" />
    </DrawerForm>
  );
};

export default EditTemplate;
