import { fetchEnum } from '@/services/apis';
import { getDocumentTemplateGroupList } from '@/services/documentTemplate';
import { parseEnumMapToOptions } from '@/services/enums';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { Button, Form } from 'antd';
import { useEffect, useRef, useState } from 'react';
import EditTemplate from './EditTemplate';

/** 状态 */
export enum StateStatus {
  // 停用
  Off = 0,
  // 启用
  On = 1,
}

const AITemplate = () => {
  const [open, setOpen] = useState(false);
  const [form] = Form.useForm<API.DocumentTemplateListItem>();
  const [subjectCategoryOptions, setSubjectCategoryOptions] = useState<any>({});
  const [subjectTypeOptions, setSubjectTypeOptions] = useState<any>({});
  const ref = useRef<ActionType>();

  const handleAction = () => {
    ref.current?.reload();
  };

  useEffect(() => {
    fetchEnum().then((res) => {
      setSubjectCategoryOptions(res?.data?.DocumentTemplateCategoryEnumDesc);
      setSubjectTypeOptions(res?.data?.SubjectTypeEnumDesc);
    });
  }, []);

  const columns: ProColumns<API.DocumentTemplateListItem>[] = [
    {
      dataIndex: 'documentTemplateCategory',
      title: '模板类别',
      search: false,
      valueEnum: subjectCategoryOptions,
    },
    {
      dataIndex: 'subjectType',
      title: '学科类型',
      search: false,
      valueEnum: subjectTypeOptions,
    },
    {
      dataIndex: 'subjectName',
      title: '学校名称',
    },
    {
      dataIndex: 'count',
      title: '数量',
      search: false,
    },
    {
      dataIndex: 'latestUpdatedAt',
      title: '上次更新时间',
      key: 'latestUpdatedAt',
      valueType: 'date',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '操作',
      key: 'option',
      valueType: 'option',
      width: 80,
      render(node, row) {
        return [
          <Button
            size="small"
            type="link"
            key="edit"
            target="_blank"
            href={`/YLFManage/documentTemplate/templateList?subjectId=${row.subjectId}&subjectType=${row.subjectType}`}
          >
            查看
          </Button>,
        ];
      },
    },
  ];
  return (
    <PageContainer>
      <ProTable<API.DocumentTemplateListItem>
        actionRef={ref}
        columns={columns}
        rowKey="id"
        toolBarRender={() => [
          <Button
            key="button"
            type="primary"
            onClick={() => {
              form.setFieldValue('state', 1);
              setOpen(true);
            }}
          >
            新建模版
          </Button>,
        ]}
        request={async (params) => {
          const response = await getDocumentTemplateGroupList({
            current: params.current,
            pageSize: params.pageSize,
            state: Number(params.state) === 2 ? '' : params.state,
            subjectName: params.subjectName,
          });
          return {
            data: response.data,
            success: true,
            total: response.total,
          };
        }}
      />
      <EditTemplate
        open={open}
        form={form}
        setOpen={setOpen}
        onChangeSuccess={handleAction}
        subjectCategoryOptions={parseEnumMapToOptions(subjectCategoryOptions)}
        subjectTypeOptions={parseEnumMapToOptions(subjectTypeOptions)}
      />
    </PageContainer>
  );
};

export default AITemplate;
