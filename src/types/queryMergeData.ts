/**
 * queryMergeData 接口返回数据格式定义
 *
 * 该接口用于教师材料导入功能，返回需要合并的材料数据
 * 用户可以在返回的数据中选择保存哪个版本的材料信息
 */

// 材料详情 DTO
export interface AdminMaterialDetailDTO {
  /** 区域名称 */
  area: string;
  /** 品牌 */
  brand: string;
  /** 图片文字描述 */
  imageText: string;
  /** 是否已审核 (0: 未审核, 1: 已审核) */
  isAudited: number;
  /** 是否损坏 (0: 未损坏, 1: 已损坏) */
  isDamaged: number;
  /** 线下价格 */
  linePrice: number;
  /** 材料描述 */
  materialDesc: string;
  /** 材料类型 */
  materialType: string;
  /** 材料名称 */
  name: string;
  /** 价格 */
  price: number;
  /** 数量 */
  quantity: number;
  /** 用户选择的图片 (逗号分隔的URL字符串) */
  selectedImg: string;
  /** 标签 */
  tags: string;
  /** 淘宝商品ID */
  taobaoId: string;
  /** 淘宝链接 */
  taobaoLink: string;
  /** 淘宝商品名称 */
  taobaoName: string;
  /** 淘宝价格 */
  taobaoPrice: number;
  /** 垃圾图片 */
  trashImg: string;
  /** 权重分数 */
  weightScore: number;
  /** 组合材料ID */
  combinedId?: string;
}

// 材料分析信息
export interface MaterialAnalysis {
  /** 适用区域 */
  applicableAreas: string;
  /** 创建时间 */
  createTime: string;
  /** 是否删除 (0: 未删除, 1: 已删除) */
  isDeleted: number;
  /** 材料属性 (JSON字符串数组) */
  materialAttributes: string;
  /** 材料属性ID (逗号分隔) */
  materialAttributesId: string;
  /** 材料摘要 */
  materialSummary: string;
  /** 最大适用年龄 */
  maxAge: number;
  /** 最小适用年龄 */
  minAge: number;
  /** 投放目的 */
  purpose: string;
  /** 最大适用人数 */
  suitablePeopleMax: number;
  /** 最小适用人数 */
  suitablePeopleMin: number;
  /** 更新时间 */
  updateTime: string;
}

// 管理员材料审核详情 DTO
export interface AdminMaterialReviewDetailDTO {
  /** 材料详情 */
  adminMaterialDetailDTO: AdminMaterialDetailDTO;
  /** 材料分析信息 */
  materialAnalysis: MaterialAnalysis;
}

// 管理员材料审核合并 DTO
export interface AdminMaterialReviewMergeDTO {
  /** 原始数据 (如果存在) */
  originalDTO?: AdminMaterialReviewDetailDTO;
  /** 待保存数据 */
  saveData?: AdminMaterialReviewDetailDTO;
  /** 待处理数据列表 */
  pendingList?: AdminMaterialReviewDetailDTO[];
}

// API 响应元数据
export interface ApiMetadata {
  /** 接口端点 */
  endpoint: string;
  /** 请求方法 */
  method: string;
  /** 时间戳 */
  timestamp: string;
}

// queryMergeData 接口完整响应格式
export interface QueryMergeDataResponse {
  /** 响应数据 - 合并数据列表 */
  data: AdminMaterialReviewMergeDTO[];
  /** 响应消息 */
  message: string;
  /** 响应元数据 */
  metadata: ApiMetadata;
  /** 响应状态 (0: 成功, 其他: 失败) */
  status: number;
}

// 请求参数格式
export interface QueryMergeDataParams {
  /** 组合材料ID列表 (可以是数组或逗号分隔的字符串) */
  combinedIds: string[] | string;
}

// 保存教师材料详情的请求参数
export interface SaveTeacherMaterialDetailParams {
  /** 成功数量 */
  successCount: number;
  /** 保存数据列表 */
  saveDataList: AdminMaterialReviewDetailDTO[];
}

/**
 * 数据结构说明：
 *
 * 1. 接口返回一个包含多个合并数据项的数组
 * 2. 每个合并数据项 (AdminMaterialReviewMergeDTO) 包含：
 *    - originalDTO: 原始数据 (如果存在)
 *    - saveData: 待保存的新数据 (通常是从淘宝等渠道获取的数据)
 *    - pendingList: 待处理的数据列表 (可能有多个版本需要选择)
 *
 * 3. 用户需要在这些数据中选择最终要保存的版本
 * 4. 选择完成后，调用 saveTeacherMaterialDetail 接口完成导入
 *
 * 使用示例：
 * ```typescript
 * // 调用接口
 * const response = await queryMergeData({
 *   combinedIds: ['123', '456', '789']
 * });
 *
 * // 处理响应数据
 * if (response.status === 0) {
 *   const mergeDataList = response.data;
 *
 *   // 为每个材料选择最终数据版本
 *   const finalSaveDataList = mergeDataList.map(item => {
 *     // 优先选择 saveData，如果没有则选择 originalDTO
 *     return item.saveData || item.originalDTO;
 *   });
 *
 *   // 调用保存接口
 *   await saveTeacherMaterialDetail({
 *     successCount: finalSaveDataList.length,
 *     saveDataList: finalSaveDataList
 *   });
 * }
 * ```
 */
