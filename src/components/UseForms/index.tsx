/**
 *  基础表单组件
 *  该组件提供部分基础表单功能，基础布局
 *  复杂表单请使用render方法
 *  后续如果有通用功能可按照实际情况添加
 */
import {
  Col,
  DatePicker,
  DatePickerProps,
  Form,
  FormProps,
  Input,
  InputProps,
  Radio,
  RadioProps,
  Row,
  Select,
  SelectProps,
} from 'antd';
import { Rule } from 'antd/es/form';
import { FormInstance } from 'antd/lib';
import FileFormItem, { FileFormItemProps } from './FileFormItem';

type RenderProps = {
  render: () => JSX.Element;
};
type InputTextareaProps = InputProps & {
  autoSize?: boolean;
  classNames?: string;
  styles?: React.CSSProperties;
  onSubmit?: undefined;
  prefix?: undefined;
};

type CommonField = { label: string; name: string; rules?: Rule[] };
export type FormField =
  | (InputProps & CommonField & { formType: FormTypes.input })
  | (SelectProps & CommonField & { formType: FormTypes.select })
  | (DatePickerProps &
      CommonField & {
        formType: FormTypes.datepicker;
      })
  | (RadioProps &
      CommonField & {
        formType: FormTypes.radio;
        options: { label: string; value: any }[];
      })
  | (InputTextareaProps & CommonField & { formType: FormTypes.textarea })
  | (RenderProps & CommonField & { formType: FormTypes.render })
  | (FileFormItemProps & CommonField & { formType: FormTypes.file });

export type FormFields = (FormField | FormField[])[];

interface AntFormsProps {
  formProps: FormProps;
  forms: FormFields;
  children?: JSX.Element | JSX.Element[];
  form: FormInstance<any>;
}

export enum FormTypes {
  'input' = 'input',
  'select' = 'select',
  'datepicker' = 'datepicker',
  'radio' = 'radio',
  'render' = 'render',
  'textarea' = 'textarea',
  'file' = 'file',
}

function getFormEle(form: FormField, formInstance: FormInstance<any>) {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { formType, name, label, rules, ...restProps } = form;
  switch (form.formType) {
    case FormTypes.select:
      return <Select {...(restProps as SelectProps)}></Select>;

    case FormTypes.radio:
      return (
        <Radio.Group {...(restProps as RadioProps)}>
          {form.options!.map((o) => (
            <Radio key={o.value} value={o.value}>
              {o.label}
            </Radio>
          ))}
        </Radio.Group>
      );
    case FormTypes.datepicker:
      return <DatePicker style={{ width: '100%' }} {...(restProps as DatePickerProps)} />;

    case FormTypes.render:
      return form.render();
    case FormTypes.textarea:
      return <Input.TextArea {...(restProps as any)} />;
    case FormTypes.file:
      return <FileFormItem formInstance={formInstance} fileKey={form.fileKey} name={name} />;
    case FormTypes.input:
    default:
      return <Input {...(restProps as InputProps)} />;
  }
}

function renderForm(form: FormField, formInstance: FormInstance<any>) {
  return (
    <Form.Item key={form.name} name={form.name} rules={form.rules} label={form.label}>
      {getFormEle(form, formInstance)}
    </Form.Item>
  );
}

const AntForms: React.FC<AntFormsProps> = (props) => {
  return (
    <Form labelCol={{ style: { width: 120 } }} {...props.formProps} form={props.form}>
      {props.forms.map((form, index) => {
        if (Array.isArray(form)) {
          return (
            <Row justify={'space-around'} key={index} gutter={24}>
              {form.map((f: any) => (
                <Col
                  key={f.name}
                  style={{ padding: '0 8px', maxWidth: 360 }}
                  span={f.span || 24 / form.length}
                >
                  {renderForm(f, props.form)}
                </Col>
              ))}
            </Row>
          );
        } else {
          return renderForm(form, props.form);
        }
      })}

      <Form.Item>{props.children}</Form.Item>
    </Form>
  );
};

export const AntFormsItem = (props: {
  forms: FormFields;
  children?: JSX.Element | JSX.Element[];
  form: FormInstance<any>;
}) => {
  return (
    <>
      {props.forms.map((form, index) => {
        if (Array.isArray(form)) {
          return (
            <Row justify={'space-around'} key={index} gutter={24}>
              {form.map((f: any) => (
                <Col
                  key={f.name}
                  style={{ padding: '0 8px', maxWidth: 360 }}
                  span={f.span || 24 / form.length}
                >
                  {renderForm(f, props.form)}
                </Col>
              ))}
            </Row>
          );
        } else {
          return renderForm(form, props.form);
        }
      })}

      <Form.Item>{props.children}</Form.Item>
    </>
  );
};

export default AntForms;
