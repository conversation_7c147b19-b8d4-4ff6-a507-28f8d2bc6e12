/*
 * @Description:文件上传
 * @Author: SaraSL
 * @Date: 2024-02-26 13:25:01
 */

/**
 * 文件上传逻辑
 *
 * 1、前端上传文件组件，拿到文件对象后，使用hash算法，算得文件的 Hash 值，然后2
 * 2、前端请求后端 /admin/resource/hash 接口，查询该 Hash 值是否已有对应文件实体；有则6，无则3
 * 3、前端请求后端 /admin/resource/auth 接口，获取上传oss使用的auth信息；此步骤可在页面加载时直接调用，然后4
 * 4、前端从后端获取 auth 数据后，调用 ali-oss SDK中 简单上传 方法，上传文件到 阿里云OSS，然后5
 * 5、前端获取 oss 数据实体，请求后端 /admin/resource/sync 接口，将 oss 实体数据存储到 resource 实体，然后6
 * 6、前端获取 resource 数据实体，请求后端 /admin/resource/bind 接口，将 resource 实体数据绑定到对应业务实体，END
 */

import React, { useState } from 'react';

import { ModalForm, ProFormText, ProFormUploadButton } from '@ant-design/pro-form';
import { Divider, Image, Modal } from 'antd';

import { updateResource } from '@/services/api';
import { fetchClient, handleUpload } from '@/services/fileUpload';
import { handleAction, isImageFile } from '@/services/utils';

/**
 * ===== 上传图片组件
 * @see [阿里云OSS简单上传] (https://help.aliyun.com/document_detail/383950.html)
 * @see [antd上传图片到阿里云的oss服务器] (https://www.jianshu.com/p/7ca4b4fad6eb)
 */
type UploadImageProps = {
  label?: string;
  fileList: API.FileItem[];
  setFileList: (fileList: any[]) => void;
  max?: number;
  client: any;
  setClient: (client: any) => void;
  edit?: boolean;
  fileName?: string;
  accept?: string;
};
const UploadImageComponent: React.FC<UploadImageProps> = (props) => {
  // const [currentImage, setCurrentImage] = useState<API.FileItem>();
  const [currentImage] = useState<API.FileItem>();
  const [previewVisible, setPreviewVisible] = useState<boolean>(false);

  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [previewTitle, setPreviewTitle] = useState('');

  const {
    fileList = [],
    setFileList,
    max = 10,
    client,
    setClient,
    label,
    edit = false,
    fileName = 'uploadImages',
    accept,
  } = props;

  const pushFileList = (resource: API.ResourceItem) => {
    setFileList([
      ...fileList,
      {
        id: resource.id,
        uri: resource.uri,
        filename: resource.filename,
        url: resource.uri,
        name: resource.filename,
      },
    ]);
  };

  const handleRemove = async (file: any) => {
    setFileList(fileList.filter((item) => item.id !== file.id));
    return true;
  };

  const handleSave = async (file: any) => {
    if (!client) {
      fetchClient(setClient);
    }
    const resource = await handleUpload(client, file);
    pushFileList(resource);
    return '';
  };

  // const handlePreview = async (file: any) => {
  //   setCurrentImage(file);
  //   setPreviewVisible(true);
  // };

  const handlePreview = async (file) => {
    if (!file.url || !isImageFile(file?.filename)) {
      return;
    }

    setPreviewImage(file.url);
    setPreviewOpen(true);
    setPreviewTitle(file.name || file.url!.substring(file.url!.lastIndexOf('/') + 1));
  };

  const handleCancel = () => setPreviewOpen(false);

  const handleUpdate = async (values: any) => {
    await handleAction('更新', async () => {
      setFileList(
        fileList.map((file) => {
          if (file.id === values.id) {
            file.filename = values.filename;
          }
          return file;
        }),
      );
      await updateResource(values);
    });
    setPreviewVisible(false);
  };

  return (
    <>
      <ProFormUploadButton
        max={max}
        label={label}
        fieldProps={{
          name: 'file',
          listType: 'picture',
          fileList,
          onRemove: handleRemove,
          onPreview: handlePreview,
        }}
        name={fileName}
        accept={accept}
        action={handleSave}
      />
      <Modal open={previewOpen} title={previewTitle} footer={null} onCancel={handleCancel}>
        <img alt="example" style={{ width: '100%' }} src={previewImage} />
      </Modal>
      {edit ? (
        <ModalForm
          modalProps={{
            onCancel: () => {
              return true;
            },
            destroyOnClose: true,
          }}
          onFinish={handleUpdate}
          open={previewVisible}
          onOpenChange={setPreviewVisible}
          // visivle={previewVisible}
          // onVisibleChange={setPreviewVisible}
          title={`预览${label}`}
          labelCol={{ span: 4 }}
          layout="horizontal"
          initialValues={currentImage}
        >
          <Image alt={label} style={{ width: '100%' }} src={currentImage?.uri || ''} />
          <Divider />
          <ProFormText name="id" label="图片ID" disabled width="xs" />
          <ProFormText
            name="name"
            label="图片名称"
            width="sm"
            placeholder="请输入图片名称"
            rules={[{ required: true }]}
          />
        </ModalForm>
      ) : (
        ''
      )}
    </>
  );
};

export default UploadImageComponent;
