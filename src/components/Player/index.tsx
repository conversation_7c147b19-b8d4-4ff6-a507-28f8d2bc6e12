import { Image, Modal } from 'antd';
import { useRef, useState } from 'react';
import ReactPlayer from 'react-player';
import './index.less';

interface IProps {
  category: number;
  uri: string;
  filename: string;
}

enum ECategory {
  IMAGE = 1,
  FILE = 10,
  MEDIA = 5,
}

export default (props: IProps) => {
  const { category, uri, filename } = props;

  const [playing, setPlaying] = useState(false);
  const [isShow, setIsShow] = useState(false);

  const playerRef = useRef();
  const [firstFrame, setFirstFrame] = useState(null);

  const onReady = () => {
    // 当播放器准备就绪后，获取首帧图片
    if (playerRef.current) {
      const canvas = playerRef.current.getInternalPlayer('html5')?.canvas;
      const ctx = canvas.getContext('2d');
      ctx.drawImage(canvas, 0, 0, canvas.width, canvas.height);
      setFirstFrame(canvas.toDataURL('image/png'));
    }
  };

  if (category === ECategory.IMAGE) {
    return (
      <Image
        alt={filename}
        style={{
          maxHeight: 50,
        }}
        src={uri || ''}
      />
    );
  }
  if (category === ECategory.MEDIA) {
    return (
      <>
        <div className="playWrapper">
          <div
            onClick={() => {
              setPlaying(true);
              setIsShow(true);
            }}
            className="play"
          >
            <ReactPlayer
              className="playerWrapper"
              url={uri} // 视频链接
              onReady={onReady}
              // controls // 显示播放控件
              width="80px"
              height="40px"
              config={{
                file: {
                  attributes: {
                    disablePictureInPicture: true, // 禁用画中画
                  },
                },
              }}
            />
          </div>
        </div>
        {isShow ? (
          <Modal
            title="播放视频"
            open={isShow}
            cancelButtonProps={{
              style: {
                display: 'none',
              },
            }}
            destroyOnClose
            onCancel={() => {
              setPlaying(false);
              setIsShow(false);
            }}
            okButtonProps={{
              style: {
                display: 'none',
              },
            }}
          >
            <ReactPlayer
              className="playerWrapper"
              url={uri} // 视频链接
              onReady={onReady}
              // controls // 显示播放控件
              playing={playing}
              onClick={() => {
                setPlaying((res) => {
                  return !res;
                });
              }}
              width="480px"
              height="324px"
              config={{
                file: {
                  attributes: {
                    disablePictureInPicture: true, // 禁用画中画
                  },
                },
              }}
              onEnded={() => {
                setPlaying(false);
              }}
            />
          </Modal>
        ) : null}
      </>
    );
  }
  if (category === ECategory.FILE) {
    return (
      <a href={uri} download={filename}>
        {filename}
      </a>
    );
  }
  // return (
  //   <a href={uri} download={filename}>{filename}</a >
  // );
};
