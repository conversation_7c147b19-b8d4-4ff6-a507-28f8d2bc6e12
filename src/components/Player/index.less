.play {
    position: relative;
    width: 100px;
    height: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
}


.play::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5); /* 黑色半透明蒙层 */
    opacity: 0; /* 初始透明 */
    transition: opacity 0.5s ease; /* 渐变效果 */
  }
   
  .play:hover::after {
    opacity: 1; /* 鼠标悬停时不透明 */
  }
