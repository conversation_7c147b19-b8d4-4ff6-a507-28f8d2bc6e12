import AMapLoader from '@amap/amap-jsapi-loader';
import { SearchOutlined } from '@ant-design/icons';
import { Input } from 'antd';
import { useEffect } from 'react';
import './index.css';

window._AMapSecurityConfig = {
  securityJsCode: '8b1f6428b7719a3f9e0d54a03599eeb7',
};

export default function MapContainer({ mapStyle, form }) {
  let map: any = null;

  const createMap = (AMap: any) => {
    //1.创建地图实例
    map = new AMap.Map('container', {
      // 设置地图容器id
      viewMode: '2D', // 是否为3D地图模式
      zoom: 10, // 初始化地图级别
      //   center: [116.397428, 39.90923], // 初始化地图中心点位置
      resizeEnable: true, // 调整大小启用
      //   layers: [new AMap.TileLayer.Satellite()], //设置图层,可设置成包含一个或多个图层的数组
      //   mapStyle: 'amap://styles/whitesmoke', //设置地图的显示样式
    });

    // 2.加载插件
    AMap.plugin(
      ['AMap.AutoComplete', 'AMap.PlaceSearch'],
      function () {
        let auto = new AMap.AutoComplete({ input: 'amapInput' });
        let placeSearch = new AMap.PlaceSearch({
          map: map,
        }); //构造地点查询类
        auto.on('select', function (e: any) {
          placeSearch.setCity(e.poi.adcode);
          placeSearch.search(e.poi.name); //关键字查询查询
        }); //注册监听，当选中某条记录时会触发

        // 锚点点击事件
        placeSearch.on('markerClick', function (e) {
          // 根据点击marker进行下一步
          const lng = e.data.location.lng;
          const lat = e.data.location.lat;
          form.setFieldsValue({
            longitude: lng,
            latitude: lat,
          });
        });
      },

      // // 实例点击事件
      // map.on('click', e => {
      //   const lngLat = `${e.lnglat.getLat()},${e.lnglat.getLng()}`
      //   console.log('坐标位置:', lngLat)
      // //   this.props.onChange(lngLat)
      // })
    );
  };

  const onCatch = (e: any) => {
    console.log(e);
  };

  const mountMap = () => {
    map?.destroy(); //销毁地图
  };

  useEffect(() => {
    AMapLoader.load({
      key: '1c52698a1cf40f65f196dd4bd5dbc0fe', // 申请好的Web端开发者Key，首次调用 load 时必填
      version: '2.0', // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
      plugins: [], // 需要使用的的插件列表，如比例尺'AMap.Scale'等
    })
      .then(createMap)
      .catch(onCatch);

    return mountMap;
  }, []);

  return (
    <div className="map-box" style={{ position: 'relative' }}>
      <div id="container" className="map-container" style={{ ...mapStyle }}></div>
      <Input
        id="amapInput"
        className="amap-input"
        placeholder="请输入搜索内容"
        prefix={<SearchOutlined />}
      />
    </div>
  );
}
