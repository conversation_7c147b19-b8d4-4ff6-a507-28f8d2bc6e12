import React from 'react';

import { ExclamationCircleTwoTone } from '@ant-design/icons';
import { Divider, Space, Tag, Tooltip } from 'antd';

// ===== 创建&更新时间单元格
export const timeColumnRender = (dom: any, record: any) => {
  return record.createdAt || record.updatedAt ? (
    <Space direction="vertical">
      {record.createdAt ? (
        <Tag color="blue">
          创建人: {record.createdBy || '-'} <Divider type="vertical" /> 创建于: {record.createdAt}
        </Tag>
      ) : (
        ''
      )}
      {record.updatedAt ? (
        <Tag color="green">
          更新人: {record.updatedBy || '-'} <Divider type="vertical" /> 更新于: {record.updatedAt}
        </Tag>
      ) : (
        ''
      )}
    </Space>
  ) : (
    '-'
  );
};

export const timeColumn: any = {
  dataIndex: 'time',
  ellipsis: true,
  hideInForm: true,
  render: timeColumnRender,
  search: false,
  title: '创建&更新时间',
  width: '280px',
};

export const statusColumn: any = {
  dataIndex: 'status',
  hideInForm: true,
  search: false,
  title: '状态',
  valueEnum: {
    0: { color: 'red', text: '停用', status: 'Default' },
    1: { color: 'green', text: '启用', status: 'Processing' },
  },
};

export const idColumn: any = {
  title: '编号',
  dataIndex: 'id',
  hideInForm: true,
  valueType: 'text',
  width: '4em',
  search: false,
};

// ===== 提示信息组件
type TipProps = { title: string };

/**
 * 提示信息组件
 * @param props
 * @returns
 */
export const TipC: React.FC<TipProps> = (props) => {
  return (
    <Tooltip placement="top" title={props.title}>
      <ExclamationCircleTwoTone />
    </Tooltip>
  );
};
